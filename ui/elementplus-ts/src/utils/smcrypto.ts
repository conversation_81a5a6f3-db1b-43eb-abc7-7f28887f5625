import { sm2, sm4 } from 'sm-crypto';

const publicKey = import.meta.env.VITE_APP_RSA_PUBLIC_KEY;
const privateKey = import.meta.env.VITE_APP_RSA_PRIVATE_KEY;
const cipherMode = 1;

export const sm2Encryptor = (str: string) => {
  const encrypted = sm2.doEncrypt(str, publicKey, cipherMode);
  return encrypted.toString();
};
export const sm2Decrypt = (str: string) => {
  if (str && str.length > 2) {
    if (str.startsWith('04')) {
      str = str.substring(2, str.length);
    }
  }
  const decrypted = sm2.doDecrypt(str, privateKey, 1);
  return decrypted.toString();
};
export const sm2Decrypt2 = (str: string, key: string) => {
  const decrypted = sm2.doDecrypt(str, key, 1);
  return decrypted.toString();
};
export const sm4Encryptor = (str: string, sm4Key: string) => {
  const encrypted = sm4.encrypt(str, sm4Key);
  return encrypted.toString();
};
export const sm4Decrypt = (str: string, sm4Key: string) => {
  const decrypted = sm4.decrypt(str, sm4Key);
  return decrypted.toString();
};
export const sm2KeyPair = () => {
  let keypair = sm2.generateKeyPairHex();
  console.log('publicKey--' + keypair.publicKey);
  console.log('privateKey--' + keypair.privateKey);
  return keypair;
};
export const generateSm4Key = () => {
  let key = generateRandomString();
  return key;
};
/**
 * 随机生成32位的字符串
 * @returns {string}
 */
const generateRandomString = () => {
  const characters = 'ABCDEFabcdef0123456789';
  let result = '';
  const charactersLength = characters.length;
  for (let i = 0; i < 32; i++) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength));
  }
  return result;
};

export const stringToHex = (str: string) => {
  let hex = Array.prototype.map
    .call(str, (c) => {
      return c.charCodeAt(0).toString(16);
    })
    .join('');
  return hex;
};
