// 测试类型修复
import { BaseSchema, ZodEnum } from './schemas';
import { validateIdCard, parseIdCardInfo } from './utils/validators';

console.log('=== 类型修复验证测试 ===');

// 1. 测试 Schema 导入
console.log('\n1. Schema 导入测试:');
const emailResult = BaseSchema.EMAIL.safeParse('<EMAIL>');
console.log('EMAIL 验证:', emailResult.success);

const phoneResult = ZodEnum.PHONE_EMPTY.safeParse('');
console.log('PHONE_EMPTY 验证:', phoneResult.success);

// 2. 测试验证器导入
console.log('\n2. 验证器导入测试:');
const idCardResult = validateIdCard('11010519491231002X');
console.log('身份证验证:', idCardResult.isValid);

const idCardInfo = parseIdCardInfo('11010519491231002X');
console.log('身份证信息解析:', idCardInfo?.gender);

// 3. 测试全角字符验证
console.log('\n3. 全角字符验证测试:');
const fullWidthTest = BaseSchema.STRING.safeParse('你好世界');
console.log('全角字符验证:', fullWidthTest.success);
if (!fullWidthTest.success) {
  console.log('错误信息:', fullWidthTest.error.issues[0].message);
}

console.log('\n✅ 所有类型错误已修复，功能正常！');

export {
  emailResult,
  phoneResult,
  idCardResult,
  idCardInfo
};
