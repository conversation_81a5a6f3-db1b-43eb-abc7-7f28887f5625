/**
 * 验证器 Schema 定义
 * 包含所有验证逻辑和 Schema 构建
 */

import { z } from '#/adapter/form';
import { RegexEnum } from '#/constants/regex-enum';
import {
  validateIdCardAreaCode,
  validateIdCardBirthDate,
  validateIdCardChecksum,
} from './id-card-validator';
import { isChinese } from './chinese-name-rule';

/**
 * 为任何 schema 添加全角字符验证的包装器
 * 使用 superRefine 方法确保全角字符验证优先级最高
 * @param schema 要包装的 schema
 * @param message 自定义错误消息
 * @returns 包装后的 schema
 */
export const withNoFullWidth = <T extends z.ZodTypeAny>(
  schema: T,
  message?: string,
) =>
  z.string().superRefine((val, ctx) => {
    // 首先检查全角字符
    if (val && val !== '' && RegexEnum.FULL_WIDTH_REGEX.test(val)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: message || '不能包含全角字符，请使用半角字符',
      });
      return;
    }

    // 如果没有全角字符，执行原始验证
    const result = schema.safeParse(val);
    if (!result.success) {
      // 添加原始验证的错误
      result.error.issues.forEach((issue) => {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: issue.message,
          path: issue.path,
        });
      });
    }
  });

/**
 * 可选值包装器
 * @param schema 要包装的 schema
 * @returns 包装后的可选 schema
 */
export const optional = <T extends z.ZodTypeAny>(schema: T) =>
  schema.optional().or(z.literal(''));

/**
 * 基础验证 Schema 定义
 */
export const BaseSchema = {
  /**
   * 基础字符串验证
   */
  STRING: withNoFullWidth(z.string()),
  
  /**
   * 邮箱验证
   */
  EMAIL: withNoFullWidth(z.string().email('请输入正确的邮箱')),
  
  /**
   * 手机号验证
   */
  PHONE: withNoFullWidth(
    z.string().regex(RegexEnum.MOBILE, '请输入正确的手机号'),
  ),
  
  /**
   * 公安机关机构代码验证
   */
  GAJGJGDM: withNoFullWidth(
    z
      .string()
      .length(12, '请输入12位数字')
      .regex(/^\d{12}$/, '请输入12位数字'),
  ),
  
  /**
   * 公民身份号码（身份证号码）验证
   * 18位，包含地区代码、出生日期、顺序码和校验码的完整验证
   */
  ID_CARD: withNoFullWidth(
    z
      .string()
      .length(18, '身份证号码必须为18位')
      .regex(RegexEnum.ID_CARD_BASIC, '身份证号码格式不正确')
      .refine((val) => validateIdCardAreaCode(val), '身份证号码地区代码无效')
      .refine((val) => validateIdCardBirthDate(val), '身份证号码出生日期无效')
      .refine((val) => validateIdCardChecksum(val), '身份证号码校验码错误'),
  ),
  
  /**
   * 中文姓名验证
   * 完全按照您提供的规则验证：
   * /^[\u0391-\uFFE5]+(?:·[\u0391-\uFFE5]+)?$/
   * 然后检查禁用字符：/^[^【】、﹒▪•]+$/
   */
  CHINESE_NAME: withNoFullWidth(
    z
      .string()
      .min(1, '姓名不能为空')
      .refine((val) => isChinese(val, false), '只能输入中文字符'),
  ),
} as const;

/**
 * 验证 Schema 枚举
 * 包含基础 Schema 和可选 Schema
 */
export const ValidationSchemas = {
  // 基础 Schema
  STRING: BaseSchema.STRING,
  EMAIL: BaseSchema.EMAIL,
  PHONE: BaseSchema.PHONE,
  GAJGJGDM: BaseSchema.GAJGJGDM,
  ID_CARD: BaseSchema.ID_CARD,
  CHINESE_NAME: BaseSchema.CHINESE_NAME,
  
  // 可选 Schema
  EMAIL_EMPTY: optional(BaseSchema.EMAIL),
  PHONE_EMPTY: optional(BaseSchema.PHONE),
  GAJGJGDM_EMPTY: optional(BaseSchema.GAJGJGDM),
  ID_CARD_EMPTY: optional(BaseSchema.ID_CARD),
  CHINESE_NAME_EMPTY: optional(BaseSchema.CHINESE_NAME),
} as const;

/**
 * Schema 类型定义
 */
export type ValidationSchemaType = typeof ValidationSchemas;
export type ValidationSchemaKey = keyof ValidationSchemaType;
