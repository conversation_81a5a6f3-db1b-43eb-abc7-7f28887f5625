import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

export const tableColumns: VxeGridProps['columns'] = [
  {
    type: 'seq',
    title: '序号',
    width: 60,
  },
  {
    field: 'tokenId',
    title: '会话编号',
  },
  {
    field: 'userName',
    title: '登录账号',
  },
  {
    field: 'deptName',
    title: '部门名称',
  },
  {
    field: 'ipaddr',
    title: '主机',
  },
  {
    field: 'loginLocation',
    title: '登录地点',
  },
  {
    field: 'browser',
    title: '浏览器',
  },
  {
    field: 'os',
    title: '操作系统',
  },
  {
    field: 'loginTime',
    title: '登录时间',
    slots: { default: 'loginTime' },
    minWidth: 80,
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
  },
];

export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    label: '主机',
    fieldName: 'ipaddr',
  },
  {
    component: 'Input',
    label: '登录账号',
    fieldName: 'userName',
  },
];
