package com.ruoyi.system.domain.vo.sysxzqh;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 行政区划管理视图对象 sys_xzqh
 *
 * <AUTHOR>
 * @date 2024-11-15 17:37:59
 */
@Data
public class SysXzqhListVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private String id;

    /**
     * 父菜单ID
     */
    private String parentId;

    /**
     * 显示顺序
     */
    private Integer orderNum;
    /**
     * 名称
     */
    private String mc;
    /**
     * 简称
     */
    private String jc;
    /**
     * 省份简称
     */
    private String sfjc;
    /**
     * 地址元素类型代码
     */
    private String dzyslxdm;
    /**
     * 使用状态代码
     */
    private String syztdm;

    /**
     * 是否有子节点
     */
    private boolean hasChildren;
}
