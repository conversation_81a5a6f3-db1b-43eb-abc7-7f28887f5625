package com.ruoyi.flow.domain.bo.flowdefinition;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import jakarta.validation.constraints.*;
import org.dromara.warm.flow.orm.entity.FlowDefinition;

/**
 * 流程定义业务对象 flow_definition
 *
 * <AUTHOR>
 * @date 2025-01-06 10:04:52
 */
@Data
@AutoMapper(target = FlowDefinition.class, reverseConvertGenerate = false)
public class FlowDefinitionEditBo {

    /**
     * 主键id
     */
    @NotNull(message = "主键id不能为空")
    private Long id;
    /**
     * 流程编码
     */
    @NotBlank(message = "流程编码不能为空")
    private String flowCode;
    /**
     * 流程名称
     */
    @NotBlank(message = "流程名称不能为空")
    private String flowName;
    /**
     * 流程类别
     */
    @NotBlank(message = "流程类别不能为空")
    private String category;
    /**
     * 流程版本
     */
    private Integer version;
    /**
     * 是否发布（0未发布 1已发布 9失效）
     */
  //  @NotNull(message = "是否发布（0未发布 1已发布 9失效）不能为空")
    private Integer isPublish;
    /**
     * 审批表单是否自定义（Y是 N否）
     */
   // @NotBlank(message = "审批表单是否自定义（Y是 N否）不能为空")
    private String formCustom;
    /**
     * 审批表单路径
     */
  //  @NotBlank(message = "审批表单路径不能为空")
    private String formPath;
    /**
     * 流程激活状态（0挂起 1激活）
     */
   // @NotNull(message = "流程激活状态（0挂起 1激活）不能为空")
    private Integer activityStatus;
    /**
     * 监听器类型
     */
   // @NotBlank(message = "监听器类型不能为空")
    private String listenerType;
    /**
     * 监听器路径
     */
   // @NotBlank(message = "监听器路径不能为空")
    private String listenerPath;
    /**
     * 扩展字段，预留给业务系统使用
     */
   // @NotBlank(message = "扩展字段，预留给业务系统使用不能为空")
    private String ext;
    /**
     * 删除标志
     */
   // @NotBlank(message = "删除标志不能为空")
    private String delFlag;

}
