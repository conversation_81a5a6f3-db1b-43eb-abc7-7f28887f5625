// 测试类型修复后的效果
import { BaseSchema } from './zod-enum';

console.log('=== 测试类型修复后的效果 ===');

// 1. 测试 PHONE 字段
console.log('\n1. PHONE 字段测试:');

// 1.1 全角数字 - 应该显示全角字符错误
const phoneFullWidth = BaseSchema.PHONE.safeParse('１３８１２３４５６７８');
console.log('PHONE 全角数字结果:', phoneFullWidth);
if (!phoneFullWidth.success) {
  console.log('PHONE 全角数字错误信息:', phoneFullWidth.error.issues[0].message);
  console.log('期望: "不能包含全角字符，请使用半角字符"');
  console.log('是否正确:', phoneFullWidth.error.issues[0].message === '不能包含全角字符，请使用半角字符' ? '✅' : '❌');
}

// 1.2 半角但格式错误 - 应该显示手机号格式错误
const phoneFormat = BaseSchema.PHONE.safeParse('12345678901');
console.log('PHONE 格式错误结果:', phoneFormat);
if (!phoneFormat.success) {
  console.log('PHONE 格式错误信息:', phoneFormat.error.issues[0].message);
  console.log('期望: "请输入正确的手机号"');
  console.log('是否正确:', phoneFormat.error.issues[0].message === '请输入正确的手机号' ? '✅' : '❌');
}

// 1.3 正确的手机号
const phoneCorrect = BaseSchema.PHONE.safeParse('13812345678');
console.log('PHONE 正确结果:', phoneCorrect.success ? '✅ 通过' : '❌ 失败');

// 2. 测试 GAJGJGDM 字段
console.log('\n2. GAJGJGDM 字段测试:');

// 2.1 全角数字 - 应该显示全角字符错误
const gajgjgdmFullWidth = BaseSchema.GAJGJGDM.safeParse('１１１１１１１１１１１１');
console.log('GAJGJGDM 全角数字结果:', gajgjgdmFullWidth);
if (!gajgjgdmFullWidth.success) {
  console.log('GAJGJGDM 全角数字错误信息:', gajgjgdmFullWidth.error.issues[0].message);
  console.log('期望: "不能包含全角字符，请使用半角字符"');
  console.log('是否正确:', gajgjgdmFullWidth.error.issues[0].message === '不能包含全角字符，请使用半角字符' ? '✅' : '❌');
}

// 2.2 半角但长度错误 - 应该显示长度错误
const gajgjgdmLength = BaseSchema.GAJGJGDM.safeParse('12345');
console.log('GAJGJGDM 长度错误结果:', gajgjgdmLength);
if (!gajgjgdmLength.success) {
  console.log('GAJGJGDM 长度错误信息:', gajgjgdmLength.error.issues[0].message);
  console.log('期望: "请输入12位数字"');
  console.log('是否正确:', gajgjgdmLength.error.issues[0].message === '请输入12位数字' ? '✅' : '❌');
}

// 2.3 半角但格式错误 - 应该显示格式错误
const gajgjgdmFormat = BaseSchema.GAJGJGDM.safeParse('abcdefghijkl');
console.log('GAJGJGDM 格式错误结果:', gajgjgdmFormat);
if (!gajgjgdmFormat.success) {
  console.log('GAJGJGDM 格式错误信息:', gajgjgdmFormat.error.issues[0].message);
  console.log('期望: "请输入12位数字"');
  console.log('是否正确:', gajgjgdmFormat.error.issues[0].message === '请输入12位数字' ? '✅' : '❌');
}

// 2.4 正确的GAJGJGDM
const gajgjgdmCorrect = BaseSchema.GAJGJGDM.safeParse('123456789012');
console.log('GAJGJGDM 正确结果:', gajgjgdmCorrect.success ? '✅ 通过' : '❌ 失败');

// 3. 测试 EMAIL 字段
console.log('\n3. EMAIL 字段测试:');

// 3.1 全角字符 - 应该显示全角字符错误
const emailFullWidth = BaseSchema.EMAIL.safeParse('测试@example.com');
console.log('EMAIL 全角字符结果:', emailFullWidth);
if (!emailFullWidth.success) {
  console.log('EMAIL 全角字符错误信息:', emailFullWidth.error.issues[0].message);
  console.log('期望: "不能包含全角字符，请使用半角字符"');
  console.log('是否正确:', emailFullWidth.error.issues[0].message === '不能包含全角字符，请使用半角字符' ? '✅' : '❌');
}

// 3.2 半角但格式错误 - 应该显示邮箱格式错误
const emailFormat = BaseSchema.EMAIL.safeParse('invalid-email');
console.log('EMAIL 格式错误结果:', emailFormat);
if (!emailFormat.success) {
  console.log('EMAIL 格式错误信息:', emailFormat.error.issues[0].message);
  console.log('期望: "请输入正确的邮箱"');
  console.log('是否正确:', emailFormat.error.issues[0].message === '请输入正确的邮箱' ? '✅' : '❌');
}

// 3.3 正确的邮箱
const emailCorrect = BaseSchema.EMAIL.safeParse('<EMAIL>');
console.log('EMAIL 正确结果:', emailCorrect.success ? '✅ 通过' : '❌ 失败');

// 4. 测试 STRING 字段
console.log('\n4. STRING 字段测试:');

// 4.1 全角字符 - 应该显示全角字符错误
const stringFullWidth = BaseSchema.STRING.safeParse('你好世界');
console.log('STRING 全角字符结果:', stringFullWidth);
if (!stringFullWidth.success) {
  console.log('STRING 全角字符错误信息:', stringFullWidth.error.issues[0].message);
  console.log('期望: "不能包含全角字符，请使用半角字符"');
  console.log('是否正确:', stringFullWidth.error.issues[0].message === '不能包含全角字符，请使用半角字符' ? '✅' : '❌');
}

// 4.2 半角字符 - 应该通过
const stringHalfWidth = BaseSchema.STRING.safeParse('hello world');
console.log('STRING 半角字符结果:', stringHalfWidth.success ? '✅ 通过' : '❌ 失败');

// 5. 总结测试结果
console.log('\n=== 测试总结 ===');
const tests = [
  { name: 'PHONE 全角字符', result: phoneFullWidth.success ? false : phoneFullWidth.error.issues[0].message === '不能包含全角字符，请使用半角字符' },
  { name: 'PHONE 格式错误', result: phoneFormat.success ? false : phoneFormat.error.issues[0].message === '请输入正确的手机号' },
  { name: 'PHONE 正确输入', result: phoneCorrect.success },
  { name: 'GAJGJGDM 全角字符', result: gajgjgdmFullWidth.success ? false : gajgjgdmFullWidth.error.issues[0].message === '不能包含全角字符，请使用半角字符' },
  { name: 'GAJGJGDM 长度错误', result: gajgjgdmLength.success ? false : gajgjgdmLength.error.issues[0].message === '请输入12位数字' },
  { name: 'GAJGJGDM 格式错误', result: gajgjgdmFormat.success ? false : gajgjgdmFormat.error.issues[0].message === '请输入12位数字' },
  { name: 'GAJGJGDM 正确输入', result: gajgjgdmCorrect.success },
  { name: 'EMAIL 全角字符', result: emailFullWidth.success ? false : emailFullWidth.error.issues[0].message === '不能包含全角字符，请使用半角字符' },
  { name: 'EMAIL 格式错误', result: emailFormat.success ? false : emailFormat.error.issues[0].message === '请输入正确的邮箱' },
  { name: 'EMAIL 正确输入', result: emailCorrect.success },
  { name: 'STRING 全角字符', result: stringFullWidth.success ? false : stringFullWidth.error.issues[0].message === '不能包含全角字符，请使用半角字符' },
  { name: 'STRING 半角字符', result: stringHalfWidth.success },
];

const passedTests = tests.filter(test => test.result).length;
const totalTests = tests.length;

console.log(`通过测试: ${passedTests}/${totalTests}`);
tests.forEach(test => {
  console.log(`${test.result ? '✅' : '❌'} ${test.name}`);
});

if (passedTests === totalTests) {
  console.log('\n🎉 所有测试通过！全角字符验证功能正常工作！');
} else {
  console.log('\n⚠️ 部分测试失败，需要进一步调试。');
}

export {
  phoneFullWidth,
  phoneFormat,
  gajgjgdmFullWidth,
  gajgjgdmLength,
  emailFullWidth,
  emailFormat,
  stringFullWidth
};
