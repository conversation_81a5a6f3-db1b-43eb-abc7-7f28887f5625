package com.ruoyi.flow.service;

import java.util.Arrays;
import java.util.List;

import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.core.core.domain.PageData;
import com.ruoyi.common.core.core.domain.Result;
import com.ruoyi.common.orm.core.page.PageQuery;
import com.ruoyi.common.orm.core.page.PageResult;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.paginate.Page;
import com.ruoyi.common.core.utils.MapstructUtils;
import com.ruoyi.common.orm.core.service.impl.BaseServiceImpl;
import jakarta.annotation.Resource;
import org.dromara.warm.flow.core.entity.Definition;
import org.dromara.warm.flow.core.service.DefService;
import org.dromara.warm.flow.orm.entity.FlowDefinition;
import org.dromara.warm.flow.orm.entity.table.FlowDefinitionTableDef;
import org.dromara.warm.flow.orm.mapper.FlowDefinitionMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


import com.ruoyi.flow.domain.vo.flowdefinition.FlowDefinitionDetailVo;
import com.ruoyi.flow.domain.vo.flowdefinition.FlowDefinitionListVo;
import com.ruoyi.flow.domain.vo.flowdefinition.FlowDefinitionExcelVo;
import com.ruoyi.flow.domain.vo.flowdefinition.FlowDefinitionOptionVo;
import com.ruoyi.flow.domain.bo.flowdefinition.FlowDefinitionAddBo;
import com.ruoyi.flow.domain.bo.flowdefinition.FlowDefinitionEditBo;
import com.ruoyi.flow.domain.bo.flowdefinition.FlowDefinitionListBo;
import com.ruoyi.flow.domain.bo.flowdefinition.FlowDefinitionOptionBo;
import static com.mybatisflex.core.query.QueryMethods.case_;
import static org.dromara.warm.flow.orm.entity.table.FlowDefinitionTableDef.FLOW_DEFINITION;




/**
 * 流程定义Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-06 10:04:52
 */
@Service
public class FlowDefinitionService extends BaseServiceImpl<FlowDefinitionMapper, FlowDefinition> {
    @Resource
    private FlowDefinitionMapper flowDefinitionMapper;

    @Resource
    private DefService defService;

    public QueryWrapper query() {
        return super.query().from(FLOW_DEFINITION);
    }

    private QueryWrapper buildQueryWrapper(FlowDefinitionListBo flowDefinitionListBo) {
        QueryWrapper queryWrapper = super.buildBaseQueryWrapper();
        queryWrapper.and(FLOW_DEFINITION.FLOW_CODE.eq(flowDefinitionListBo.getFlowCode()));
        queryWrapper.and(FLOW_DEFINITION.FLOW_NAME.like(flowDefinitionListBo.getFlowName()));
        queryWrapper.and(FLOW_DEFINITION.CATEGORY.eq(flowDefinitionListBo.getCategory()));
        queryWrapper.and(FLOW_DEFINITION.IS_PUBLISH.eq(flowDefinitionListBo.getIsPublish()));
        queryWrapper.and(FLOW_DEFINITION.FORM_CUSTOM.eq(flowDefinitionListBo.getFormCustom()));
        queryWrapper.and(FLOW_DEFINITION.FORM_PATH.eq(flowDefinitionListBo.getFormPath()));
        queryWrapper.and(FLOW_DEFINITION.ACTIVITY_STATUS.eq(flowDefinitionListBo.getActivityStatus()));
        queryWrapper.and(FLOW_DEFINITION.LISTENER_TYPE.eq(flowDefinitionListBo.getListenerType()));
        queryWrapper.and(FLOW_DEFINITION.LISTENER_PATH.eq(flowDefinitionListBo.getListenerPath()));
        queryWrapper.and(FLOW_DEFINITION.EXT.eq(flowDefinitionListBo.getExt()));
        queryWrapper.and(FLOW_DEFINITION.DEL_FLAG.eq(flowDefinitionListBo.getDelFlag()));
        return queryWrapper;
    }


    /**
     * 分页查询流程定义列表
     *
     * @param flowDefinitionListBo 流程定义ListBo
     * @return 分页流程定义集合
     */
    public Result<PageData<FlowDefinitionListVo>> page(FlowDefinitionListBo flowDefinitionListBo) {
        QueryWrapper queryWrapper = buildQueryWrapper(flowDefinitionListBo);
            Page<FlowDefinitionListVo> page = this.pageAs(PageQuery.build(), queryWrapper, FlowDefinitionListVo.class);
        return PageResult.success(page);
    }


    /**
     * 查询流程定义列表
     *
     * @param flowDefinitionListBo 流程定义ListBo
     * @return 流程定义集合
     */
    public List<FlowDefinitionListVo> list(FlowDefinitionListBo flowDefinitionListBo) {
        QueryWrapper queryWrapper = buildQueryWrapper(flowDefinitionListBo);
            return this.listAs(queryWrapper, FlowDefinitionListVo.class);
    }


    /**
     * 获取流程定义选择项查询条件
     *
     * @param optionBo 流程定义表查询条件
     * @param a              流程定义表对象
     * @return 流程定义选择项查询条件
     */
    private static QueryWrapper getOptionQueryWrapper(FlowDefinitionOptionBo optionBo, FlowDefinitionTableDef a) {
        QueryWrapper queryWrapper = new QueryWrapper();
        //选项列表查询条件
        return queryWrapper;
    }

    /**
     * 查询流程定义管理选择列表
     *
     * @param optionBo 流程定义选择查询条件
     * @return 返回树形结构的流程定义信息列表
     */
    public Result<List<FlowDefinitionOptionVo>> optionSelect(FlowDefinitionOptionBo optionBo) {
        FlowDefinitionTableDef a = FLOW_DEFINITION.as("a");
        QueryWrapper queryWrapper = getOptionQueryWrapper(optionBo, a);

        List<FlowDefinitionOptionVo> optionVoList = this.listAs(queryWrapper, FlowDefinitionOptionVo.class);
        return Result.success(optionVoList);
    }


    /**
     * 导出流程定义列表
     *
     * @param flowDefinitionListBo 流程定义ListBo
     * @return 流程定义集合
     */
    public List<FlowDefinitionExcelVo> export(FlowDefinitionListBo flowDefinitionListBo) {
        QueryWrapper queryWrapper = buildQueryWrapper(flowDefinitionListBo);
            return this.listAs(queryWrapper, FlowDefinitionExcelVo.class);
    }


    /**
     * 新增流程定义

     * @param flowDefinitionAddBo 流程定义AddBo
     * @return 结果:true 操作成功，false 操作失败
     */
    @Transactional
    public Result<Void> add(FlowDefinitionAddBo flowDefinitionAddBo) {
        flowDefinitionAddBo.setIsPublish(0);
        flowDefinitionAddBo.setActivityStatus(1);
        FlowDefinition flowDefinition = MapstructUtils.convert(flowDefinitionAddBo, FlowDefinition.class);
        boolean inserted = defService.saveAndInitNode(flowDefinition);
       // boolean inserted = this.save(flowDefinition);
        if (!inserted) {
            return Result.fail("新增流程定义记录失败！");
        }
        return Result.success();
    }

    /**
     * 修改流程定义
     *
     * @param flowDefinitionEditBo 流程定义EditBo
     * @return 结果:true 更新成功，false 更新失败
     */
    public Result<Void> edit(FlowDefinitionEditBo flowDefinitionEditBo) {
        FlowDefinition flowDefinition = MapstructUtils.convert(flowDefinitionEditBo, FlowDefinition.class);
        boolean updated = false;
        if(ObjectUtil.isNotNull(flowDefinition) && ObjectUtil.isNotNull(flowDefinition.getId())) {
            //updated = defService.updateById(flowDefinition);
           updated = this.updateById(flowDefinition);
        }
        if (!updated) {
            return Result.fail("修改流程定义记录失败!");
        }
        return Result.success();
    }


    /**
     * 批量删除流程定义
     *
     * @param ids 需要删除的流程定义主键集合
     * @return 结果:true 删除成功，false 删除失败
     */
    @Transactional
    public Result<Void> removeByIds(Long[] ids) {
        boolean deleted = this.removeByIds(Arrays.asList(ids));
        if (!deleted) {
            return Result.fail("删除流程定义记录失败!");
        }
        return Result.success();
    }


    /**
     * 查询流程定义
     *
     * @param id 流程定义主键
     * @return 流程定义
     */
    public FlowDefinitionDetailVo detail(Long id) {
        return this.getOneAs(query().where(FLOW_DEFINITION.ID.eq(id)), FlowDefinitionDetailVo.class);
    }

    /**
     * 新增流程定义，前台提供主键值，一般用于导入的场合
     *
     * @param flowDefinitionAddBo 流程定义AddBo
     * @return 结果:true 操作成功，false 操作失败
     */
    public Result<Void> insertWithPk(FlowDefinitionAddBo flowDefinitionAddBo) {
        FlowDefinition flowDefinition = MapstructUtils.convert(flowDefinitionAddBo, FlowDefinition.class);
        boolean inserted;
            inserted=flowDefinitionMapper.insertWithPk(flowDefinition) > 0;//前台传来主键值
        if (!inserted) {
            return Result.fail("新增流程定义记录失败！");
        }
        return Result.success();
    }

    /**
     * 发布流程
     * @param id
     * @return
     */
    public Boolean publish(Long id){
        return defService.publish(id);
    }

    /**
     * 取消发布
     * @param id
     * @return
     */
    public Boolean unPublish(Long id){
        return defService.unPublish(id);
    }


    /**
     * 激活流程
     * @param id
     * @return
     */
    public Boolean active(Long id){
        return defService.active(id);
    }

    /**
     * 挂起流程  active unActive
     * @param id
     * @return
     */
    public Boolean unActive(Long id){
        return defService.unActive(id);
    }

}
