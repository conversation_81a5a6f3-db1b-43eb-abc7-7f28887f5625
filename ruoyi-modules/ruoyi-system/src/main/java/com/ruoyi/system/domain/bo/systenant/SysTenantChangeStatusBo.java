package com.ruoyi.system.domain.bo.systenant;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.system.domain.po.SysTenant;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 租户业务对象 sys_tenant
 *
 * <AUTHOR>
 * @since 2025-04-28 09:55:52
 */
@Data
@AutoMapper(target = SysTenant.class, reverseConvertGenerate = false)
public class SysTenantChangeStatusBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 租户编号
     */
    @NotBlank(message = "租户编号不能为空")
    private String id;

    /**
     * 租户状态（0正常 1停用）
     */
    @NotBlank(message = "租户状态（0正常 1停用）不能为空")
    private String status;


}
