package com.ruoyi.system.domain.bo.systenantpackage;

import com.ruoyi.system.domain.po.SysTenantPackage;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMapping;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 租户套餐业务对象 sys_tenant_package
 *
 * <AUTHOR>
 * @since 2025-04-27 21:19:08
 */
@Data
@AutoMapper(target = SysTenantPackage.class, reverseConvertGenerate = false)
public class SysTenantPackageAddBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 套餐名称
     */
    @NotBlank(message = "套餐名称不能为空")
    private String packageName;

    /**
     * 关联菜单id
     */
    @NotEmpty(message = "关联菜单id不能为空")
    @AutoMapping(target = "menuIds", expression = "java(com.ruoyi.common.core.utils.StringUtils.join(source.getMenuIds(), \",\"))")
    private String[] menuIds;

    /**
     * 备注
     */
    private String remark;

    /**
     * 菜单树选择项是否关联显示
     */
    @NotNull(message = "菜单树选择项是否关联显示不能为空")
    private Boolean menuCheckStrictly;

    /**
     * 状态（0正常 1停用）
     */
    @NotBlank(message = "状态（0正常 1停用）不能为空")
    private String status;


}
