import http from '@/utils/http';
import type { ProductDataOption, ProductVO, ProductForm, ProductQuery } from '@/api/mf/product/types';

/**
 * 查询产品树列表
 * @param query
 * @returns {*}
 */
export const listProduct = (query?: ProductQuery) => {
  return http.get<ProductVO[]>('/mf/product/list', query);
};

/**
 * 查询产品树选择列表
 * @param query
 * @returns {*}
 */
export const listProductOption = (query?: ProductQuery) => {
  let url = '/mf/product/listOption';
  if (query.isTree) {
    url = '/mf/product/listOptionTree';
  }
  return http.get<Array<ProductDataOption>>(url, query);
};
/**
 * 查询产品树详细
 * @param id
 */
export const getProduct = (id: string | number) => {
  return http.get<ProductVO>('/mf/product/' + id);
};

/**
 * 新增产品树
 * @param data
 */
export const addProduct = (data: ProductForm) => {
  return http.post('/mf/product', data);
};

/**
 * 修改产品树
 * @param data
 */
export const editProduct = (data: ProductForm) => {
  return http.put('/mf/product', data);
};

/**
 * 删除产品树
 * @param id
 */
export const removeProduct = (id: string | number | Array<string | number>) => {
  return http.delete('/mf/product/' + id);
};
