package com.ruoyi.mf.domain.vo.mfproduct;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 产品树选择树形结构VO
 *
 * <AUTHOR>
 * @since 2025-03-17 17:51:24
 */
@Data
public class MfProductOptionTreeVo extends MfProductOptionVo implements Serializable {

    /**
     * 父菜单ID
     */
    private String parentId;

    /**
     * 是否叶子节点
     */
    private Boolean isLeaf;


    /**
     * 子节点
     */
    private List<MfProductOptionTreeVo> children;
}
