package com.ruoyi.system.service;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import com.mybatisflex.core.mask.MaskManager;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.update.UpdateChain;
import com.ruoyi.common.core.constant.UserConstants;
import com.ruoyi.common.core.core.domain.Result;
import com.ruoyi.common.core.core.domain.PageData;
import com.ruoyi.common.core.core.domain.model.LoginUser;
import com.ruoyi.common.core.enums.CommonResponseEnum;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.StreamUtils;
import com.ruoyi.common.core.utils.file.MimeTypeUtils;
import com.ruoyi.common.encrypt.utils.EncryptUtils;
import com.ruoyi.common.orm.core.page.PageQuery;
import com.ruoyi.common.orm.core.page.PageResult;
import com.mybatisflex.core.query.QueryWrapper;
import com.ruoyi.common.core.utils.MapstructUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.orm.core.service.impl.BaseServiceImpl;
import com.ruoyi.common.satoken.utlis.LoginHelper;
import com.ruoyi.common.tenant.helper.TenantHelper;
import com.ruoyi.system.domain.bo.syspost.SysPostBo;
import com.ruoyi.system.domain.bo.sysrole.SysRoleListBo;
import com.ruoyi.system.domain.bo.sysuser.*;
import com.ruoyi.system.domain.vo.AvatarVo;
import com.ruoyi.system.domain.vo.SysOssVo;
import com.ruoyi.system.domain.vo.sysrole.SysRoleVo;
import com.ruoyi.system.domain.vo.syspost.SysPostVo;
import com.ruoyi.system.domain.vo.sysrole.SysRoleListVo;
import com.ruoyi.system.domain.vo.sysuser.*;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.system.mapper.SysUserMapper;
import com.ruoyi.system.domain.po.SysUser;

import static com.mybatisflex.core.query.QueryMethods.*;
import static com.ruoyi.system.domain.po.table.SysDeptTableDef.SYS_DEPT;
import static com.ruoyi.system.domain.po.table.SysRoleTableDef.SYS_ROLE;
import static com.ruoyi.system.domain.po.table.SysUserRoleTableDef.SYS_USER_ROLE;
import static com.ruoyi.system.domain.po.table.SysUserTableDef.SYS_USER;

import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

/**
 * 用户信息Service业务层处理
 * <p>
 * 本服务类提供用户信息地增删改查等基础操作，包括：
 * <ul>
 *     <li>分页查询用户信息列表</li>
 *     <li>查询用户信息详情</li>
 *     <li>新增用户信息</li>
 *     <li>修改用户信息</li>
 *     <li>删除用户信息</li>
 * </ul>
 *
 * <AUTHOR>
 * @since 2025-04-29 14:00:02
 * @version 1.0
 */
@Service
public class SysUserService extends BaseServiceImpl<SysUserMapper, SysUser> {
    @Resource
    private SysUserMapper sysUserMapper;
    @Resource
    private SysDataScopeService sysDataScopeService;
    @Resource
    private SysOssService sysOssService;
    @Resource
    private SysPostService sysPostService;
    @Resource
    private SysRoleService sysRoleService;

    /**
     * 获取基础查询对象
     *
     * @return 预设了表信息的查询包装器
     */
    public QueryWrapper query() {
        return super.query().from(SYS_USER);
    }

    /**
     * 构建查询条件
     * <p>
     * 根据传入的查询参数构建查询条件，MyBatis Flex会自动处理空值
     *
     * @param bo 查询参数对象
     * @return 构建好地查询条件包装器
     */
    private QueryWrapper buildQueryWrapper(SysUserListBo bo) {
        return super.buildBaseQueryWrapper()
                // 部门ID
                .and(SYS_USER.DEPT_ID.eq(bo.getDeptId()))
                // 用户账号
                .and(SYS_USER.USER_NAME.like(bo.getUserName()))
                // 手机号码
                .and(SYS_USER.PHONENUMBER.eq(bo.getPhonenumber()))
                // 用户性别（0男 1女 2未知）
                .and(SYS_USER.GENDER.eq(bo.getGender()))
                // 账号状态（0正常 1停用）
                .and(SYS_USER.STATUS.eq(bo.getStatus()))
                // 创建时间
                .and(SYS_USER.CREATE_TIME.between(bo.getParams().get("beginCreateTime"), bo.getParams().get("endCreateTime")))
                ;
    }

    private QueryWrapper buildListQueryWrapper(SysUserListBo userBo) {
        QueryWrapper queryWrapper = sysUserMapper.selectUserDeptQueryWrapper();
        queryWrapper.and(SYS_USER.ID.eq(userBo.getId()));
        queryWrapper.and(SYS_USER.USER_NAME.like(userBo.getUserName()));
        queryWrapper.and(SYS_USER.STATUS.eq(userBo.getStatus()));
        queryWrapper.and(SYS_USER.PHONENUMBER.like(userBo.getPhonenumber()));

        // 处理时间范围查询
        Map<String, Object> params = userBo.getParams();
        if (params.get("beginTime") != null && params.get("endTime") != null) {
            queryWrapper.and(SYS_USER.CREATE_TIME.between(
                params.get("beginTime"),
                params.get("endTime")
            ));
        }

        // 处理部门ID查询
        if (ObjectUtil.isNotNull(userBo.getDeptId())) {
            queryWrapper.and(
                SYS_USER.DEPT_ID.eq(userBo.getDeptId())
                    .or(SYS_USER.DEPT_ID.in(
                        select(SYS_DEPT.ID)
                            .from(SYS_DEPT.as("t"))
                            .where(findInSet(
                                string(userBo.getDeptId()),
                                SYS_DEPT.ANCESTORS
                            ).gt(0))
                    ))
            );
        }

        return queryWrapper.orderBy(SYS_USER.ID.desc());
    }


    /**
     * 分页查询用户信息列表
     * <p>
     * 根据查询条件进行分页查询，并将结果转换为前端所需的VO对象
     *
     * @param userBo 用户信息查询参数
     * @return 包含分页用户信息集合的结果对象
     */
    public Result<PageData<SysUserListVo>> page(SysUserListBo userBo) {
        QueryWrapper queryWrapper = sysDataScopeService.addCondition(buildListQueryWrapper(userBo));
        Page<SysUserListVo> page = this.pageAs(PageQuery.build(), queryWrapper, SysUserListVo.class);
        return PageResult.success(page);
    }

    /**
     * 查询用户信息列表（不分页）
     * <p>
     * 根据查询条件获取所有符合条件的用户信息
     *
     * @param bo 用户信息查询参数
     * @return 用户信息集合
     */
    public List<SysUserListVo> list(SysUserListBo bo) {
        QueryWrapper queryWrapper = buildQueryWrapper(bo);
        return this.listAs(queryWrapper, SysUserListVo.class);
    }

    /**
     * 新增用户信息
     * <p>
     * 将前端传入的数据转换为实体并保存到数据库
     *
     * @param bo 用户信息新增参数
     * @return 操作结果，成功返回{@code Result.success()}，失败返回包含错误信息的{@code Result.fail()}
     */
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> add(SysUserAddBo bo) {
        SysUser entity = MapstructUtils.convert(bo, SysUser.class);
        boolean inserted = this.save(entity);
        return inserted
            ? Result.success()
            : Result.fail("新增用户信息记录失败！");
    }

    /**
     * 修改用户信息
     * <p>
     * 更新数据库中已存在的用户信息
     *
     * @param bo 用户信息编辑参数
     * @return 操作结果，成功返回{@code Result.success()}，失败返回包含错误信息的{@code Result.fail()}
     */
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> edit(SysUserEditBo bo) {
        SysUser entity = MapstructUtils.convert(bo, SysUser.class);
        if (ObjectUtil.isNull(entity) || ObjectUtil.isNull(entity.getId())) {
            return Result.fail("用户信息ID不能为空");
        }

        boolean updated = this.updateById(entity);
        return updated
            ? Result.success()
            : Result.fail("修改用户信息记录失败!");
    }

    /**
     * 批量删除用户信息
     * <p>
     * 根据ID数组删除多条用户信息记录
     *
     * @param ids 需要删除的用户信息主键数组
     * @return 操作结果，成功返回{@code Result.success()}，失败返回包含错误信息的{@code Result.fail()}
     * @throws IllegalArgumentException 当ids为null或空数组时抛出
     */
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> removeByIds(String[] ids) {
        // 参数校验
        if (ids == null || ids.length == 0) {
            return Result.fail("删除ID不能为空");
        }

        // 执行批量删除
        return this.removeByIds(Arrays.asList(ids))
            ? Result.success()
            : Result.fail("删除用户信息记录失败!");
    }

    /**
     * 获取用户信息
     *
     * @return 用户信息
     */
    public Result<UserInfoVo> getInfo() {
        UserInfoVo userInfoVo = new UserInfoVo();
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (LoginHelper.isSuperAdmin()) {
            // 超级管理员 如果重新加载用户信息需清除动态租户
            TenantHelper.clearDynamic();
        }
        SysUserVo user = null;
        if (loginUser != null) {
            user = selectUserById(loginUser.getUserId());
        }
        if (ObjectUtil.isNull(user)) {
            return Result.fail("没有权限访问用户数据!");
        }
        userInfoVo.setUser(user);
        userInfoVo.setPermissions(loginUser.getMenuPermission());
        userInfoVo.setRoles(loginUser.getRolePermission());
        return Result.success(userInfoVo);
    }

    /**
     * 根据用户编号获取详细信息
     */
    public Result<SysUserInfoVo> getInfo(String id) {
        checkUserDataScope(id);
        SysUserInfoVo userInfoVo = new SysUserInfoVo();

        List<SysRoleListVo> roles = sysRoleService.list(SysRoleListBo.builder().status(UserConstants.ROLE_NORMAL).build());
        SysPostBo sysPost = new SysPostBo();
        sysPost.setStatus(UserConstants.POST_NORMAL);
        userInfoVo.setRoles(LoginHelper.isSuperAdmin(id) ? roles : roles.stream().filter(r -> !LoginHelper.isSuperAdminRole(r.getId())).toList());
        userInfoVo.setPosts(sysPostService.selectPostList(sysPost));
        if (ObjectUtil.isNotNull(id)) {
            //暂时取消脱敏处理
            SysUserVo sysUser;
            try {
                MaskManager.skipMask();
                sysUser = selectUserById(id);
            } finally {
                MaskManager.restoreMask();
            }
            userInfoVo.setUser(sysUser);

            userInfoVo.setRoleIds(StreamUtils.toList(sysUser.getRoles(), SysRoleVo::getId));
            userInfoVo.setPostIds(sysPostService.selectPostListByUserId(id));
        }
        return Result.success(userInfoVo);
    }

    /**
     * 新增用户信息（带主键）
     * <p>
     * 使用前端提供的主键值新增记录，通常用于数据导入场景
     *
     * @param bo 用户信息新增参数（含主键）
     * @return 操作结果，成功返回{@code Result.success()}，失败返回包含错误信息的{@code Result.fail()}
     */
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> insertWithPk(SysUserAddBo bo) {
        SysUser entity = MapstructUtils.convert(bo, SysUser.class);
        // 使用特定的mapper方法执行带主键的插入
        boolean inserted = sysUserMapper.insertWithPk(entity) > 0;
        return inserted
            ? Result.success()
            : Result.fail("新增用户信息记录失败！");
    }

    /**
     * 通过用户名查询用户
     *
     * @param userName 用户名
     * @return 用户对象信息
     */
    public SysUser selectUserByUserName(String userName) {
        return this.getOne(QueryWrapper.create()
            .where(SYS_USER.JLZT.eq(1))
            .where(SYS_USER.USER_NAME.eq(userName))
        );
    }


    /**
     * 根据角色ID分页查询用户列表
     * <p>
     * 根据isLocated参数决定查询已分配或未分配该角色的用户列表
     * 支持按用户名和手机号进行筛选
     *
     * @param userBo 用户查询参数，包含角色ID、用户名、手机号等信息
     * @param isLocated 是否查询已分配角色的用户
     *                  true: 查询已分配该角色的用户
     *                  false: 查询未分配该角色的用户
     * @return 分页的用户列表数据
     */
    public Result<PageData<SysUserListVo>> selectUserListRolePage(SysUserRoleListBo userBo, boolean isLocated) {
        // 获取基础查询条件
        QueryWrapper queryWrapper = sysUserMapper.selectUserRoleQueryWrapper();

        // 添加用户名模糊查询条件
        if (StringUtils.isNotEmpty(userBo.getUserName())) {
            queryWrapper.and(SYS_USER.USER_NAME.like(userBo.getUserName()));
        }

        // 添加手机号精确查询条件
        if (StringUtils.isNotEmpty(userBo.getPhonenumber())) {
            queryWrapper.and(SYS_USER.PHONENUMBER.eq(userBo.getPhonenumber()));
        }

        // 根据isLocated参数构建不同的查询条件
        if (isLocated) {
            // 查询已分配该角色的用户
            queryWrapper.and(SYS_ROLE.ID.eq(userBo.getRoleId()));
        } else {
            // 查询未分配该角色的用户
            queryWrapper.and(SYS_ROLE.ID.ne(userBo.getRoleId()).or(SYS_ROLE.ID.isNull()));

            // 添加数据权限过滤条件
            sysDataScopeService.addCondition(queryWrapper);

            // 排除已分配该角色的用户
            queryWrapper.and(SYS_USER.ID.notIn(
                select(SYS_USER.ID)
                    .from(SYS_USER.as("u"))
                    .innerJoin(SYS_USER_ROLE).as("ur")
                    .on(SYS_USER_ROLE.USER_ID.eq(SYS_USER.ID)
                        .and(SYS_USER_ROLE.ROLE_ID.eq(userBo.getRoleId())))
            ));
        }

        // 执行分页查询并返回结果
        Page<SysUserListVo> page = this.pageAs(PageQuery.build(), queryWrapper, SysUserListVo.class);
        return PageResult.success(page);
    }

    /**
     * 通过用户ID查询用户
     *
     * @param userId 用户ID
     * @return 用户对象信息
     */
    public SysUserVo selectUserById(String userId) {
        QueryWrapper queryWrapper = query();
        if (ObjectUtil.isNotNull(userId)) {
            queryWrapper.where(SYS_USER.ID.eq(userId));
        }
        return sysUserMapper.selectOneWithRelationsByQueryAs(queryWrapper, SysUserVo.class);
    }

    /**
     * 个人中心模块通过用户ID查询用户
     *
     * @param userId 用户ID
     * @return 用户对象信息
     */
    public SysUserVo selectProfileUserById(String userId) {
        // 构建查询条件
        QueryWrapper queryWrapper = sysUserMapper.selectProfileUserById()
            .and(SYS_USER.ID.eq(userId));

        return sysUserMapper.selectOneWithRelationsByQueryAs(queryWrapper, SysUserVo.class);
    }

    /**
     * 查询用户所属角色组
     *
     * @param userName 用户名
     * @return 结果
     */
    public String selectUserRoleGroup(String userName) {
        List<SysRoleListVo> list = sysRoleService.selectRoleList(SysRoleListBo.builder().userName(userName).build());
        if (CollectionUtils.isEmpty(list)) {
            return StringUtils.EMPTY;
        }
        return list.stream().map(SysRoleListVo::getRoleName).collect(Collectors.joining("，"));
    }

    /**
     * 查询用户所属岗位组
     *
     * @param userName 用户名
     * @return 结果
     */
    public String selectUserPostGroup(String userName) {
        List<SysPostVo> list = sysPostService.selectPostsByUserName(userName);
        if (CollectionUtils.isEmpty(list)) {
            return StringUtils.EMPTY;
        }
        return list.stream().map(SysPostVo::getName).collect(Collectors.joining("，"));
    }

    /**
     * 个人信息
     */
    public Result<ProfileVo> profile() {
        LoginUser loginUser = LoginHelper.getLoginUser();
        SysUserVo user = null;
        if (loginUser != null) {
            user = selectProfileUserById(loginUser.getUserId());
        }
        ProfileVo profileVo = new ProfileVo();
        profileVo.setUser(user);
        if (user != null) {
            profileVo.setRoleGroup(selectUserRoleGroup(user.getUserName()));
            profileVo.setPostGroup(selectUserPostGroup(user.getUserName()));
        }
        return Result.success(profileVo);
    }

    /**
     * 修改用户基本信息
     *
     * @param userBo 用户信息
     * @return 结果
     */
    public boolean updateUserProfile(SysUserEditBo userBo) {
        SysUser user = MapstructUtils.convert(userBo, SysUser.class);
        return this.updateById(user);
    }

    /**
     * 修改用户信息
     */
    public Result<Void> updateProfile(SysUserProfileBo profile) {
        SysUserEditBo user = BeanUtil.toBean(profile, SysUserEditBo.class);
        checkUserUnique(user.getUserName(), user.getPhonenumber(), user.getEmail(), user.getId());
        LoginUser loginUser = LoginHelper.getLoginUser();
        SysUserVo sysUser = null;
        if (loginUser != null) {
            sysUser = selectUserById(loginUser.getUserId());
        }
        if (sysUser != null) {
            user.setUserName(sysUser.getUserName());
            user.setId(sysUser.getId());
            user.setVersion(sysUser.getVersion());
            user.setPassword(null);
            user.setDeptId(null);
        }
        if (updateUserProfile(user)) {
            return Result.success();
        }
        return Result.fail("修改个人信息异常，请联系管理员");
    }

    /**
     * 修改用户头像
     *
     * @param userId 用户ID
     * @param avatar 头像地址
     * @return 结果:true 更新成功，false 更新失败
     */
    public boolean updateUserAvatar(String userId, String avatar) {
        QueryWrapper queryWrapper = query().where(SYS_USER.ID.eq(userId));
        SysUser sysUser = new SysUser();
        sysUser.setId(userId);
        sysUser.setAvatar(avatar);
        return this.update(sysUser, queryWrapper);
    }

    /**
     * 头像上传
     */
    public Result<AvatarVo> avatar(MultipartFile file) throws Exception {
        if (!file.isEmpty()) {
            String extension = FileUtil.extName(file.getOriginalFilename());
            if (!StringUtils.equalsAnyIgnoreCase(extension, MimeTypeUtils.IMAGE_EXTENSION)) {
                return Result.fail("文件格式不正确，请上传" + Arrays.toString(MimeTypeUtils.IMAGE_EXTENSION) + "格式");
            }
            SysOssVo oss = sysOssService.upload(file);
            String avatar = oss.getUrl();
            if (updateUserAvatar(LoginHelper.getUserId(), oss.getId())) {
                AvatarVo avatarVo = new AvatarVo();
                avatarVo.setImgUrl(avatar);
                return Result.success(avatarVo);
            }
        }
        return Result.fail("上传图片异常，请联系管理员");
    }

    /**
     * 根据条件分页查询用户列表
     *
     * @param userBo 用户信息
     * @return 用户信息集合信息
     */
    public List<SysUserListVo> selectUserList(SysUserListBo userBo) {
        QueryWrapper queryWrapper = sysDataScopeService.addCondition(buildListQueryWrapper(userBo));
        return this.listAs(queryWrapper, SysUserListVo.class);
    }

    /**
     * 校验属性值是否唯一
     *
     * @param column 列引用
     * @param value  属性值
     * @param id     记录ID（修改时使用，新增时为null）
     * @param message 错误消息
     */
    private void checkUnique(QueryColumn column, Object value, String id, String message) {
        QueryWrapper queryWrapper = QueryWrapper.create()
                .where(column.eq(value));
        if (id != null) {
            queryWrapper.and(SYS_USER.ID.ne(id));
        }
        CommonResponseEnum.EXISTS
                .msg(message)
                .assertTrue(count(queryWrapper) > 0);
    }

    /**
     * 检查用户信息的唯一性，包括用户名、手机号和邮箱
     * 此方法旨在确保提供的用户名、手机号和邮箱在系统中是唯一的，避免重复注册或录入
     *
     * @param userName 用户名，用于检查用户名的唯一性
     * @param phonenumber 手机号，用于检查手机号的唯一性
     * @param email 邮箱，用于检查邮箱的唯一性
     * @param id 用户ID，用于更新用户信息时排除自身造成的唯一性校验失败
     */
    private void checkUserUnique(String userName, String phonenumber, String email,String id) {
        // 检查用户名是否已存在
        checkUnique(SYS_USER.USER_NAME, userName, id, "用户账号已存在");
        // 检查手机号是否已存在
        checkUnique(SYS_USER.PHONENUMBER, phonenumber, id, "手机号码已存在");
        // 检查邮箱是否已存在
        checkUnique(SYS_USER.EMAIL, email, id, "邮箱账号已存在");
    }

    /**
     * 校验用户是否允许操作
     *
     * @param userId 用户Id
     */
    public void checkUserAllowed(String userId) {
        if (ObjectUtil.isNotNull(userId) && LoginHelper.isSuperAdmin(userId)) {
            throw new ServiceException("不允许操作超级管理员用户");
        }
    }

    /**
     * 校验用户是否有数据权限
     *
     * @param userId 用户id
     */
    public void checkUserDataScope(String userId) {
        if (ObjectUtil.isNull(userId)) {
            return;
        }
        if (LoginHelper.isSuperAdmin()) {
            return;
        }

        SysUserListBo user = new SysUserListBo();
        user.setId(userId);
        List<SysUserListVo> users = selectUserList(user);
        if (StringUtils.isEmpty(users)) {
            throw new ServiceException("没有权限访问用户数据！");
        }
    }

    /**
     * 重置用户密码
     *
     * @param user 用户信息
     * @return 结果：true 操作成功，false 操作失败。
     */
    public Result<Void> resetPwd(SysUserEditBo user) {
        checkUserAllowed(user.getId());
        checkUserDataScope(user.getId());
        user.setPassword(EncryptUtils.encryptBySm4Ynrk(user.getPassword(), EncryptUtils.SM4_KEY));
        return getUpdatePwdResult(user);
    }

    /**
     * 个人更新密码
     *
     * @param bo 用户信息
     * @return 结果：true 操作成功，false 操作失败。
     */
    public Result<Void> updatePwd(SysUserPasswordBo bo) {
        SysUserVo sysUser = selectUserById(LoginHelper.getUserId());
        String password = sysUser.getPassword();
        if (!EncryptUtils.checkPassword(bo.getOldPassword(), password)) {
            return Result.fail("修改密码失败，旧密码错误");
        }
        if (EncryptUtils.checkPassword(bo.getNewPassword(), password)) {
            return Result.fail("新密码不能与旧密码相同");
        }
        SysUserEditBo userEditBo = new SysUserEditBo();
        userEditBo.setId(sysUser.getId());
        userEditBo.setPassword(EncryptUtils.encryptBySm4Ynrk(bo.getNewPassword(), EncryptUtils.SM4_KEY));

        userEditBo.setVersion(sysUser.getVersion());
        return getUpdatePwdResult(userEditBo);
    }

    /**
     * 重置密码
     *
     * @param userEditBo sysUserBo
     * @return Result
     */
    private Result<Void> getUpdatePwdResult(SysUserEditBo userEditBo) {
        boolean rested = UpdateChain.of(SysUser.class)
                .set(SysUser::getPassword, userEditBo.getPassword())
                .where(SysUser::getId).eq(userEditBo.getId())
                .and(SysUser::getVersion).eq(userEditBo.getVersion())  //手动添加乐观锁条件
                .update();
        if (!rested) {
            return Result.fail("重置密码失败！");
        }
        return Result.success();
    }


    /**
     * 登录时通过租户编号查询用户信息
     *
     * @param tenantUserBo tenantUserBo
     * @return 用户对象信息
     */
    public SysUserVo selectTenantUser(TenantUserBo tenantUserBo) {
        return TenantHelper.ignore(() -> {
            QueryWrapper queryWrapper = sysUserMapper.buildOneQueryWrapper()
                .and(SYS_USER.EMAIL.eq(tenantUserBo.getEmail()))
                .and(SYS_USER.USER_NAME.eq(tenantUserBo.getUserName()))
                .and(SYS_USER.PHONENUMBER.eq(tenantUserBo.getPhonenumber()))
                .and(SYS_USER.ID.eq(tenantUserBo.getId()));
            return sysUserMapper.selectOneWithRelationsByQueryAs(queryWrapper, SysUserVo.class);
        });
    }

    /**
     * 注册用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    public boolean registerUser(SysUserAddBo user, String tenantId) {
        SysUser sysUser = MapstructUtils.convert(user, SysUser.class);
        if (sysUser != null) {
            sysUser.setTenantId(tenantId);
            checkUserUnique(sysUser.getUserName(), sysUser.getPhonenumber(), sysUser.getEmail(), null);
        }
        return this.save(sysUser);
    }

    /**
     * 修改用户状态
     * update sys_user set status = #{status},version=version+1 where user_id = #{userId} and version=#{version}
     *
     * @param user 用户信息
     * @return 结果：true 操作成功，false 操作失败。
     */
    public boolean updateUserStatus(SysUserEditBo user) {
        return UpdateChain.of(SysUser.class)
                .set(SysUser::getStatus, user.getStatus())
                .where(SysUser::getId).eq(user.getId())
                .and(SysUser::getVersion).eq(user.getVersion())  //手动添加乐观锁条件
                .update();
    }

    /**
     * 状态修改
     */
    public Result<Void> changeStatus(SysUserEditBo user) {
        checkUserAllowed(user.getId());
        checkUserDataScope(user.getId());
        boolean updated = updateUserStatus(user);
        if (!updated) {
            return Result.fail("状态修改失败！");
        }
        return Result.success();
    }

}
