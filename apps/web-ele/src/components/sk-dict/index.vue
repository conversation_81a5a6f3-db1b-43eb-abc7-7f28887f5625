<script setup lang="ts">
import type { Ref } from 'vue';

import type {
  DictQuery,
  FieldNamesProps,
  SkDictProps,
} from '#/api/system/dict/types';

import { isArray, isFunction } from 'lodash-es';

import { useDictStore } from '#/store/dict';

defineOptions({
  name: 'SkDict',
});

/** 组件属性定义，设置默认值 */
const props = withDefaults(defineProps<SkDictProps>(), {
  type: 'select',
  isTree: false,
  multiple: false,
});

/** 组件引用 */
const dictRef = ref();
/** 组件值双向绑定 */
const vModel = defineModel<Array<number | string> | number | string>({
  default: undefined,
});
const dictStore = useDictStore();

/** 字典数据列表 */
const processData: Ref<DataOptionTree[]> = ref([]);
/** 搜索结果列表 */
const searchData: Ref<DataOptionTree[]> = ref([]);

/**
 * 字段名称映射配置
 * @returns {FieldNames} 返回字段映射对象
 */
const fieldNames = computed<FieldNamesProps>(() => {
  return {
    id: props.fieldNames?.id ?? 'id',
    label: props.fieldNames?.label ?? 'label',
    value: props.fieldNames?.value ?? 'value',
    children: props.fieldNames?.children ?? 'children',
    isLeaf: props.fieldNames?.isLeaf ?? 'isLeaf',
  };
});

/**
 * 是否为树形选择
 * @returns {boolean} 是否为树形选择模式
 */
const processIsTree = computed<boolean>(() => {
  return props.type === 'tree-select';
});

/**
 * 检查是否为字符串类型
 */
const isString = (value: unknown): value is string => {
  return typeof value === 'string';
};

/** 处理后的值 */
const processedValue = computed({
  get: () => {
    if (props.multiple) {
      if (typeof vModel.value === 'string' && vModel.value) {
        const values = vModel.value.split(',').map((item) => item.trim());
        return values.filter(Boolean);
      }
      return Array.isArray(vModel.value) ? vModel.value : [];
    }
    return vModel.value;
  },
  set: (val) => {
    if (props.multiple) {
      vModel.value = Array.isArray(val) ? val.join(',') : val;
    } else {
      vModel.value = val;
    }
  },
});

/**
 * 初始化字典数据
 * @description 根据不同的数据来源初始化字典数据
 */
onMounted(async () => {
  const data = props.data;
  // 将初始化请求封装为一个函数，在 onMounted 和 watch 中复用
  const loadDictData = async () => {
    const query: DictQuery = {
      isTree: processIsTree.value,
      ...(isString(props.data) && { lx: props.data }),
      ...(props.sjlx && { sjlx: props.sjlx }),
      ...(props.parentId && { parentId: props.parentId }),
      ...(props.dm && { dm: props.dm }),
      ...(props.likeLeft && { likeLeft: props.likeLeft }),
      ...(props.in && { in: props.in }),
      ...(props.notIn && { notIn: props.notIn }),
    };

    processData.value = await dictStore.fetchDictData(query);
    searchData.value = processData.value;
  };

  onMounted(async () => {
    await loadDictData();
  });
  watch(
    () => props.type,
    () => {
      clearData(); // 清空搜索结果
    },
  );

  watch(
    () => props.multiple,
    () => {
      vModel.value = props.multiple ? [] : '';
    },
  );

  // 监听多个 props 属性变化
  watch(
    () => ({
      dm: props.dm,
      sjlx: props.sjlx,
      parentId: props.parentId,
      likeLeft: props.likeLeft,
      data: props.data,
      in: props.in,
      notIn: props.notIn,
      isTree: processIsTree.value, // computed 值也一并监听
    }),
    async () => {
      await loadDictData();
    },
    { deep: true },
  );
  if (isArray(data)) {
    processData.value = data as DataOptionTree[];
    searchData.value = processData.value;
  } else if (isFunction(data)) {
    processData.value = await data();
    searchData.value = processData.value;
  } else {
    const query: DictQuery = {
      isTree: processIsTree.value,
      ...(isString(data) && { lx: data }),
      ...(props.sjlx && { sjlx: props.sjlx }),
      ...(props.parentId && { parentId: props.parentId }),
      ...(props.dm && { dm: props.dm }),
      ...(props.likeLeft && { likeLeft: props.likeLeft }),
      ...(props.in && { in: props.in }),
      ...(props.notIn && { notIn: props.notIn }),
    };
    processData.value = await dictStore.fetchDictData(query);
    searchData.value = processData.value;
  }
});

/**
 * 格式化值为字符串
 * @param {string | number} val - 需要格式化的值
 * @returns {string} 格式化后的字符串
 */
const formatValue = (val: number | string): string => {
  return Number.isNaN(Number(val)) ? String(val) : val.toString();
};

/**
 * 处理选择器输入搜索
 * @param {string} keyword - 搜索关键字
 * @param {string} type - 组件类型
 * @returns {Promise<void>}
 */
const outTruckInput = async (keyword: string, type: string): Promise<void> => {
  if (!keyword) {
    searchData.value = processData.value;
    return;
  }

  if (type === 'tree-select') {
    searchData.value = [];
    const item = await getTreeValue(processData.value, keyword);
    if (item) {
      vModel.value = item.value;
      searchData.value.push(item);
      await nextTick();
      dictRef.value?.blur();
    } else {
      vModel.value = '';
    }
  }
};

/**
 * 递归查询树形数据
 * @param {DictDataOptionTree[]} list - 树形数据列表
 * @param {string} keyword - 搜索关键字
 * @returns {Promise<DictDataOptionTree | undefined>} 匹配的节点
 */
const getTreeValue = async (
  list: DataOptionTree[],
  keyword: string,
): Promise<DataOptionTree | undefined> => {
  if (!list?.length) return undefined;

  for (const item of list) {
    const value = formatValue(item[fieldNames.value.value] || '');
    const label = formatValue(item[fieldNames.value.label] || '');
    const searchRegex = new RegExp(keyword);
    if (searchRegex.test(value) || searchRegex.test(label)) {
      return item;
    }

    if (item.children?.length) {
      const result = await getTreeValue(item.children, keyword);
      if (result) return result;
    }
  }

  return undefined;
};

/**
 * 清空搜索结果，恢复原始数据
 * @returns {void}
 */
const clearData = (): void => {
  if (searchData.value.length !== processData.value.length) {
    searchData.value = processData.value;
  }
};

// 暴露组件接口
defineExpose({
  /** 组件元素引用 */
  element: dictRef,
  /** 字典数据 */
  dictData: processData,
});
</script>

<template>
  <sk-select
    v-if="props.type === 'select'"
    ref="dictRef"
    v-model="processedValue"
    v-bind="$attrs"
    :options="searchData"
    :value-key="fieldNames.id"
    clearable
    filterable
    @clear="clearData"
    :teleported="true"
    :multiple="props.multiple"
  />

  <el-tree-select
    v-else-if="props.type === 'tree-select'"
    ref="dictRef"
    v-model="processedValue"
    v-bind="$attrs"
    :data="searchData"
    :node-key="fieldNames.id"
    :props="{
      label: fieldNames.label,
      value: fieldNames.value,
      children: fieldNames.children,
      isLeaf: fieldNames.isLeaf,
    }"
    :filter-method="(val: string) => outTruckInput(val, props.type)"
    clearable
    filterable
    @clear="clearData"
  />

  <el-radio-group
    v-else-if="props.type === 'radio-group'"
    ref="dictRef"
    v-model="processedValue"
    v-bind="$attrs"
  >
    <el-radio
      v-for="(col, index) in searchData"
      :key="index"
      :label="col[fieldNames.label]"
      :value="col[fieldNames.value]"
      size="large"
    />
  </el-radio-group>

  <el-radio-group
    v-else-if="props.type === 'radio-group-button'"
    ref="dictRef"
    v-model="processedValue"
    v-bind="$attrs"
  >
    <el-radio-button
      v-for="(col, index) in searchData"
      :key="index"
      :label="col[fieldNames.label]"
      :value="col[fieldNames.value]"
      size="large"
    />
  </el-radio-group>
</template>
