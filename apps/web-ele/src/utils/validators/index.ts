/**
 * 验证器模块统一导出
 */

// 常量
export {
  EMAIL_VALIDATION,
  ID_CARD_AREA_CODES,
  ID_CARD_CHECK_CODES,
  ID_CARD_ERROR_MESSAGES,
  ID_CARD_WEIGHTS,
  NAME_VALIDATION,
  PHONE_VALIDATION,
  VALID_AREA_CODES,
} from './constants';

// 身份证验证器
export {
  generateIdCardChecksum,
  parseIdCardInfo,
  validateIdCard,
  validateIdCardAreaCode,
  validateIdCardBasicFormat,
  validateIdCardBirthDate,
  validateIdCardChecksum,
} from './id-card-validator';

// 姓名验证器
export {
  validateChineseName,
  validateChineseChars,
  validateChineseDot,
  validateForbiddenChars,
  validateNameLength,
  parseNameInfo,
  formatName,
} from './name-validator';

// 类型定义
export type {
  IdCardInfo,
  IdCardValidationResult,
  NameInfo,
  NameValidationResult,
  NameValidationOptions,
  ValidationErrorType,
  ValidatorFunction,
  ValidatorWithErrorFunction,
} from './types';
