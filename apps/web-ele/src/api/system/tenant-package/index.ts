import type {
  TenantPackageDataOption,
  TenantPackageForm,
  TenantPackageQuery,
  TenantPackageVO,
} from '#/api/system/tenant-package/types';

import { requestClient } from '#/api/request';

/**
 * 查询租户套餐列表
 * @param query
 */
export const listTenantPackage = (query?: TenantPackageQuery) => {
  return requestClient.get<PageData<TenantPackageVO>>(
    '/system/tenantPackage/list',
    query,
  );
};

/**
 * 查询租户套餐选择列表
 * @param query
 */
export const listTenantPackageOption = (query?: TenantPackageQuery) => {
  const url = '/system/tenantPackage/listOption';
  return requestClient.get<Array<TenantPackageDataOption>>(url, query);
};

/**
 * 查询租户套餐详细
 * @param id
 */
export const getTenantPackage = (id: number | string) => {
  return requestClient.get<TenantPackageVO>(`/system/tenantPackage/${id}`);
};

/**
 * 新增租户套餐
 * @param data
 */
export const addTenantPackage = (data: TenantPackageForm) => {
  return requestClient.post('/system/tenantPackage', data);
};

/**
 * 修改租户套餐
 * @param data
 */
export const editTenantPackage = (data: TenantPackageForm) => {
  return requestClient.put('/system/tenantPackage', data);
};

/**
 * 删除租户套餐权限
 * @param id
 */
export const removeTenantPackage = (
  id: Array<number | string> | number | string,
) => {
  return requestClient.delete(`/system/tenantPackage/${id}`);
};

// 租户套餐状态修改
export const changePackageStatus = (data: TenantPackageVO) => {
  const requestData = {
    id: data.id,
    version: data.version,
    status: data.status,
  };
  return requestClient.put('/system/tenant/package/changeStatus', requestData);
};
