/**
 * Schema 相关的类型定义
 */

import { z } from '#/adapter/form';

/**
 * 表单验证 Schema 类型
 */
export type FormValidationSchema<T = any> = z.ZodSchema<T>;

/**
 * 验证规则类型
 */
export interface ValidationRule {
  /** 验证器函数 */
  validator: (
    rule: unknown,
    value: unknown,
    callback: (error?: Error) => void,
  ) => void;
  /** 触发时机 */
  trigger?: 'blur' | 'change' | 'submit';
  /** 是否必填 */
  required?: boolean;
  /** 错误信息 */
  message?: string;
}

/**
 * 验证错误类型
 */
export type ValidationErrorType =
  | 'areaCode'
  | 'birthDate'
  | 'checksum'
  | 'email'
  | 'format'
  | 'fullWidth'
  | 'length'
  | 'phone';

/**
 * 通用验证器函数类型
 */
export type ValidatorFunction<T = string> = (value: T) => boolean;

/**
 * 带错误信息的验证器函数类型
 */
export type ValidatorWithErrorFunction<T = string> = (value: T) => {
  errorMessage?: string;
  isValid: boolean;
};
