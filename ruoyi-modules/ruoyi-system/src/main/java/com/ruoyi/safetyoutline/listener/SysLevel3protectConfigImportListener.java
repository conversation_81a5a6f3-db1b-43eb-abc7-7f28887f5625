package com.ruoyi.safetyoutline.listener;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.idev.excel.context.AnalysisContext;
import cn.idev.excel.event.AnalysisEventListener;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.SpringUtils;
import com.ruoyi.common.core.utils.ValidatorUtils;
import com.ruoyi.common.excel.core.ExcelListener;
import com.ruoyi.common.excel.core.ExcelResult;
import com.ruoyi.common.core.core.domain.Result;
import com.ruoyi.safetyoutline.domain.vo.syslevel3protectconfig.*;
import com.ruoyi.safetyoutline.domain.bo.syslevel3protectconfig.*;
import com.ruoyi.safetyoutline.domain.po.SysLevel3protectConfig;
import com.ruoyi.safetyoutline.service.*;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 三级等保配置信息自定义导入
 *
 * <AUTHOR>
 * @date 2025-01-13 15:56:35
 */
@Slf4j
public class SysLevel3protectConfigImportListener extends AnalysisEventListener<SysLevel3protectConfigImportBo> implements ExcelListener<SysLevel3protectConfigImportBo> {
    private final SysLevel3protectConfigService sysLevel3protectConfigService;

    private final Boolean isUpdateSupport;
    private int successNum = 0;
    private int failureNum = 0;
    private final StringBuilder successMsg = new StringBuilder();
    private final StringBuilder failureMsg = new StringBuilder();

    public SysLevel3protectConfigImportListener(Boolean isUpdateSupport) {
        this.sysLevel3protectConfigService = SpringUtils.getBean(SysLevel3protectConfigService.class);
        this.isUpdateSupport = isUpdateSupport;
    }

    @Override
    public void invoke(SysLevel3protectConfigImportBo sysLevel3protectConfigImportBo, AnalysisContext context) {
        try {
            SysLevel3protectConfig sysLevel3protectConfig= sysLevel3protectConfigService.getById(sysLevel3protectConfigImportBo);
            if (ObjectUtil.isNull(sysLevel3protectConfig)) {
                //不存在就新增
                SysLevel3protectConfigAddBo sysLevel3protectConfigAddBo = BeanUtil.toBean(sysLevel3protectConfigImportBo, SysLevel3protectConfigAddBo.class);
                ValidatorUtils.validate(sysLevel3protectConfigAddBo);
                  Result<Void> result= sysLevel3protectConfigService.add(sysLevel3protectConfigAddBo);
                if (result.isSuccess()) {
                    successNum++;
                    successMsg.append("<br/>").append(successNum).append("、三级等保配置信息 记录导入成功");
                    return;
                } else {
                    failureNum++;
                    failureMsg.append("<br/>").append(failureNum).append("、三级等保配置信息 记录导入失败");
                    return;
                }
            } else if (isUpdateSupport) {
                //存在就更新
                SysLevel3protectConfigEditBo sysLevel3protectConfigEditBo = BeanUtil.toBean(sysLevel3protectConfigImportBo, SysLevel3protectConfigEditBo.class);
                sysLevel3protectConfigEditBo.setId(sysLevel3protectConfigImportBo.getId());
                Result<Void> result= sysLevel3protectConfigService.edit(sysLevel3protectConfigEditBo);
                if (result.isSuccess()) {
                    successNum++;
                    successMsg.append("<br/>").append(successNum).append("、三级等保配置信息 记录更新成功");
                    return;
                } else {
                    failureNum++;
                    failureMsg.append("<br/>").append(failureNum).append("、三级等保配置信息 记录更新失败");
                    return;
                }
            }
        } catch (Exception e) {
            failureNum++;
            String msg = "<br/>" + failureNum + "、三级等保配置信息 记录导入失败：";
            failureMsg.append(msg).append(e.getMessage());
            log.error(msg, e);
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {

    }

    @Override
    public ExcelResult<SysLevel3protectConfigImportBo> getExcelResult() {
        return new ExcelResult<>() {

            @Override
            public String getAnalysis() {
                if (failureNum > 0) {
                    failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据没有成功导入，错误如下：");
                    throw new ServiceException(failureMsg.toString());
                } else {
                    successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
                }
                return successMsg.toString();
            }

            @Override
            public List<SysLevel3protectConfigImportBo> getList() {
                return null;
            }

            @Override
            public List<String> getErrorList() {
                return null;
            }
        };
    }
}
