import type { ColumnProps, SkTableInstance } from '@/components/SkUi/SkTable/interface';
import type { StudentForm, StudentVO } from '@/api/mf/student/types';
import mfStudentDialog from '@/views/mf/student/components/MfStudentDialog.vue';
import { addStudent, editStudent, getStudent, removeStudent } from '@/api/mf/student';
import { addDialog } from '@/components/SkUi/SkReDialog';
import { h, reactive, ref, type Ref } from 'vue';
import { useValidator } from '@/hooks/skUi/web/useValidator';

/**
 * 学生信息单管理Hook
 * @returns 学生信息单管理相关的状态和方法
 */
export function useStudent() {
  const skTable = ref<SkTableInstance>();
  const { required } = useValidator();

  const columns = reactive<ColumnProps<StudentVO>[]>([
    { type: 'index', label: '#', width: 80 },
    {
      prop: 'id',
      align: 'center',
      label: '编号'
    },
    {
      prop: 'studentName',
      search: {
        el: 'input'
      },
      align: 'center',
      label: '学生名称'
    },
    {
      prop: 'studentAge',
      search: {
        el: 'input'
      },
      align: 'center',
      label: '年龄'
    },
    {
      prop: 'studentHobby',
      search: {
        el: 'input'
      },
      align: 'center',
      label: '爱好'
    },
    {
      prop: 'studentGender',
      search: {
        el: 'sk-dict',
        props: {
          data: 'xb'
        }
      },
      align: 'center',
      label: '性别'
    },
    {
      prop: 'studentStatus',
      search: {
        el: 'sk-dict',
        props: {
          data: 'xtkg'
        }
      },
      align: 'center',
      label: '状态'
    },
    {
      prop: 'studentBirthday',
      search: {
        el: 'date-picker',
        span: 1,
        props: {
          type: 'date',
          valueFormat: 'YYYY-MM-DD',
          placeholder: '请选择生日'
        }
      },
      align: 'center',
      label: '生日'
    },
    { prop: 'operation', slot: 'operation', label: '操作', width: 300, fixed: 'right' }
  ]);

  const rules = reactive<Record<string, any>>({
    studentName: [required('学生名称')],
    studentAge: [required('年龄')],
    studentHobby: [required('爱好')],
    studentGender: [required('性别')],
    studentStatus: [required('状态')],
    studentBirthday: [required('生日')],
  });

  /**
   * 初始化表单数据
   * @param {Partial<StudentVO>} studentVo - 学生信息单数据
   * @returns {Promise<Ref<StudentForm>>} 表单响应式对象
   */
  const initFormProps = async (studentVo: Partial<StudentVO>): Promise<Ref<StudentForm>> => {
    const formInline = ref<StudentForm>({
      id: '',
      studentName: '',
      studentAge: undefined,
      studentHobby: '',
      studentGender: '',
      studentStatus: '',
      studentBirthday: undefined,
    });

    if (studentVo.id) {
      const res = await getStudent(studentVo.id);
      formInline.value = structuredClone(res.data);
    }

    return formInline;
  };

  /**
   * 打开编辑/新增对话框
   * @param {string} title - 对话框标题
   * @param {Partial<StudentVO>} [row={}] - 行数据
   * @returns {Promise<void>}
   */
  const openDialog = async (title: string, row: Partial<StudentVO> = {}): Promise<void> => {
    addDialog({
      title: `${title}学生信息单`,
      width: '1024px',
      props: {
        model: await initFormProps(row),
        labelWidth: '140px',
        rules
      },
      contentRenderer: () => h(mfStudentDialog),
      beforeSure: async (done, { options }, formRef) => {
        const formData = options.props.model as StudentForm;

        formRef.validate(async (valid: boolean) => {
          if (!valid) return;

          const api = title === '新增' ? addStudent : editStudent;
          const res = await api(formData);

          if (res.code === 200) {
            window.skynet.alert(`${title}成功！`);
            done();
            await skTable.value?.getTableList();
          }
        });
      }
    });
  };

  /**
   * 导出数据
   * @returns {void}
   */
  const handleExport = (): void => {
    window.skynet
      .download(
        'mf/student/export',
        {
          ...skTable.value.searchParam
        },
        `student_${new Date().getTime()}.xlsx`
      )
      .then(() => {});
  };

  /**
   * 删除学生信息单
   * @param {StudentVO} [row] - 要删除的数据
   * @returns {Promise<void>}
   * @throws {Error} 删除失败时抛出错误
   */
  const handleDelete = async (row?: StudentVO): Promise<void> => {
    if (!row?.id) return;

    const _ids = row?.id;
    await window.skynet.confirm('是否确认删除学生信息单编号为"' + _ids + '"的数据项？');

    try {
      await removeStudent(row.id);
      window.skynet.alert('删除成功');
      await skTable.value?.getTableList();
    } catch (error) {
      console.error('删除失败:', error);
    }
  };

  return {
    skTable,
    columns,
    rules,
    openDialog,
    handleExport,
    handleDelete
  };
}
