package com.ruoyi.system.service;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;

import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.core.core.domain.Result;
import com.ruoyi.common.core.core.domain.PageData;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.file.FileUtils;
import com.ruoyi.common.orm.core.page.PageQuery;
import com.ruoyi.common.orm.core.page.PageResult;
import com.mybatisflex.core.query.QueryWrapper;
import com.ruoyi.common.core.utils.MapstructUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.orm.core.service.impl.BaseServiceImpl;
import com.ruoyi.common.oss.core.OssClient;
import com.ruoyi.common.oss.entity.UploadResult;
import com.ruoyi.common.oss.enumd.AccessPolicyType;
import com.ruoyi.common.oss.factory.OssFactory;
import com.ruoyi.system.domain.vo.SysOssVo;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.system.mapper.SysOssMapper;
import com.ruoyi.system.domain.po.SysOss;
import com.ruoyi.system.domain.vo.sysoss.SysOssDetailVo;
import com.ruoyi.system.domain.vo.sysoss.SysOssListVo;
import com.ruoyi.system.domain.bo.sysoss.SysOssListBo;
import org.springframework.web.multipart.MultipartFile;

import static com.ruoyi.system.domain.po.table.SysOssTableDef.SYS_OSS;

/**
 * OSS对象存储Service业务层处理
 * <p>
 * 本服务类提供OSS对象存储地增删改查等基础操作，包括：
 * <ul>
 *     <li>分页查询OSS对象存储列表</li>
 *     <li>查询OSS对象存储详情</li>
 *     <li>新增OSS对象存储</li>
 *     <li>修改OSS对象存储</li>
 *     <li>删除OSS对象存储</li>
 * </ul>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-05-06 15:53:19
 */
@Service
public class SysOssService extends BaseServiceImpl<SysOssMapper, SysOss> {
    /**
     * 获取基础查询对象
     *
     * @return 预设了表信息的查询包装器
     */
    public QueryWrapper query() {
        return super.query().from(SYS_OSS);
    }

    /**
     * 构建查询条件
     * <p>
     * 根据传入的查询参数构建查询条件，MyBatis Flex会自动处理空值
     *
     * @param bo 查询参数对象
     * @return 构建好地查询条件包装器
     */
    private QueryWrapper buildQueryWrapper(SysOssListBo bo) {
        return super.buildBaseQueryWrapper()
            // 文件名
            .and(SYS_OSS.FILE_NAME.like(bo.getFileName()))
            // 原名
            .and(SYS_OSS.ORIGINAL_NAME.like(bo.getOriginalName()))
            // 扩展名
            .and(SYS_OSS.FILE_SUFFIX.eq(bo.getFileSuffix()))
            // 服务商
            .and(SYS_OSS.SERVICE.eq(bo.getService()))
            // 创建时间
            .and(SYS_OSS.CREATE_TIME.between(bo.getParams().get("beginCreateTime"), bo.getParams().get("endCreateTime")))
            ;
    }

    /**
     * 分页查询OSS对象存储列表
     * <p>
     * 根据查询条件进行分页查询，并将结果转换为前端所需的VO对象
     *
     * @param bo OSS对象存储查询参数
     * @return 包含分页OSS对象存储集合的结果对象
     */
    public Result<PageData<SysOssListVo>> page(SysOssListBo bo) {
        return PageResult.success(this.pageAs(
            PageQuery.build(),
            buildQueryWrapper(bo),
            SysOssListVo.class
        ));
    }

    /**
     * 查询OSS对象存储列表（不分页）
     * <p>
     * 根据查询条件获取所有符合条件的OSS对象存储
     *
     * @param bo OSS对象存储查询参数
     * @return OSS对象存储集合
     */
    public List<SysOssListVo> list(SysOssListBo bo) {
        QueryWrapper queryWrapper = buildQueryWrapper(bo);
        return this.listAs(queryWrapper, SysOssListVo.class);
    }

    /**
     * 批量删除OSS对象存储
     * <p>
     * 根据ID数组删除多条OSS对象存储记录
     *
     * @param ids 需要删除的OSS对象存储主键数组
     * @return 操作结果，成功返回{@code Result.success()}，失败返回包含错误信息的{@code Result.fail()}
     * @throws IllegalArgumentException 当ids为null或空数组时抛出
     */
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> removeByIds(String[] ids) {
        // 参数校验
        if (ids == null || ids.length == 0) {
            return Result.fail("删除ID不能为空");
        }

        // 执行批量删除
        return this.removeByIds(Arrays.asList(ids))
            ? Result.success()
            : Result.fail("删除OSS对象存储记录失败!");
    }


    /**
     * 查询OSS对象存储详情
     * <p>
     * 根据ID获取单个OSS对象存储的详细信息
     *
     * @param id OSS对象存储主键
     * @return OSS对象存储详细信息，如果id为空则返回null
     */
    public SysOssDetailVo detail(String id) {
        if (StringUtils.isEmpty(id)) {
            return null;
        }

        return this.getOneAs(
            query().where(SYS_OSS.ID.eq(id)),
            SysOssDetailVo.class
        );
    }


    /**
     * 查询OSS对象存储详情
     * <p>
     * 根据ID获取单个OSS对象存储的详细信息
     *
     * @param ids OSS对象存储主键
     * @return OSS对象存储详细信息，如果id为空则返回null
     */
    public List<SysOssDetailVo> listSysOssByIds(String[] ids) {
        if (StringUtils.isEmpty(ids)) {
            return null;
        }

        return this.listAs(
            query().where(SYS_OSS.ID.in((Object) ids)),
            SysOssDetailVo.class
        );
    }

    /**
     * 文件下载方法，支持一次性下载完整文件
     *
     * @param id       OSS对象ID
     * @param response HttpServletResponse对象，用于设置响应头和向客户端发送文件内容
     */
    public void download(String id, HttpServletResponse response) throws IOException {
        SysOssVo sysOss = this.getOneAs(query().where(SYS_OSS.ID.eq(id)), SysOssVo.class);
        if (ObjectUtil.isNull(sysOss)) {
            throw new ServiceException("文件数据不存在!");
        }
        FileUtils.setAttachmentResponseHeader(response, sysOss.getOriginalName());
        response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE + "; charset=UTF-8");
        OssClient storage = OssFactory.instance(sysOss.getService());
        long contentLength = storage.download(sysOss.getFileName(), response.getOutputStream());
        response.setContentLengthLong(contentLength);
    }

    /**
     * 上传 MultipartFile 到对象存储服务，并保存文件信息到数据库
     *
     * @param file 要上传的 MultipartFile 对象
     * @return 上传成功后的 SysOssVo 对象，包含文件信息
     * @throws ServiceException 如果上传过程中发生异常，则抛出 ServiceException 异常
     */
    public SysOssVo upload(MultipartFile file) {
        String originalName = file.getOriginalFilename();
        String suffix = null;
        if (originalName != null) {
            suffix = StringUtils.substring(originalName, originalName.lastIndexOf("."), originalName.length());
        }
        OssClient storage = OssFactory.instance();
        UploadResult uploadResult;
        try {
            uploadResult = storage.uploadSuffix(file.getBytes(), suffix, file.getContentType());
        } catch (IOException e) {
            throw new ServiceException(e.getMessage());
        }
        // 保存文件信息
        return buildResultEntity(originalName, suffix, storage.getConfigKey(), uploadResult);
    }

    private SysOssVo buildResultEntity(String originalName, String suffix, String configKey, UploadResult uploadResult) {
        SysOss oss = new SysOss();
        oss.setUrl(uploadResult.getUrl());
        oss.setFileSuffix(suffix);
        oss.setFileName(uploadResult.getFilename());
        oss.setOriginalName(originalName);
        oss.setService(configKey);
        this.save(oss);
        SysOssVo sysOssVo = MapstructUtils.convert(oss, SysOssVo.class);
        if (sysOssVo != null) {
            return this.matchingUrl(sysOssVo);
        }
        return null;
    }

    /**
     * 桶类型为 private 的URL 修改为临时URL时长为120s
     *
     * @param oss OSS对象
     * @return oss 匹配Url的OSS对象
     */
    private SysOssVo matchingUrl(SysOssVo oss) {
        OssClient storage = OssFactory.instance(oss.getService());
        // 仅修改桶类型为 private 的URL，临时URL时长为120s
        if (AccessPolicyType.PRIVATE == storage.getAccessPolicy()) {
            oss.setUrl(storage.getPrivateUrl(oss.getFileName(), 120));
        }
        return oss;
    }


}
