# noFullWidthString 函数使用说明

## 概述

`noFullWidthString` 是一个用于创建不能输入全角字符串的 ZodString 的函数。它可以直接替换 BaseSchema 中的 `z.string()`，为字符串验证添加全角字符检测功能。

## 功能特性

- 🚫 **禁止全角字符**：自动检测并阻止输入全角字符
- 🔄 **直接替换**：可以直接替换 `z.string()` 的使用
- 📝 **自定义错误消息**：支持自定义验证失败时的错误提示
- 🔗 **链式调用**：支持 Zod 的所有链式方法（如 `.email()`, `.regex()` 等）
- 🌐 **全面覆盖**：检测中日韩字符、全角标点符号、全角空格等

## 检测范围

函数会检测以下全角字符：
- 全角空格 (`\u3000`)
- 全角标点符号 (`\uFF00-\uFFEF`)
- 中日韩统一表意文字 (`\u4E00-\u9FFF`)
- 平假名 (`\u3040-\u309F`)
- 片假名 (`\u30A0-\u30FF`)

## 基本用法

### 1. 替换基础 z.string()

```typescript
import { noFullWidthString } from '@/constants/zod-enum';

// 原来的写法
const oldSchema = z.string();

// 新的写法
const newSchema = noFullWidthString();
```

### 2. 替换 BaseSchema 中的用法

```typescript
// 原来的 BaseSchema
export const BaseSchema = {
  STRING: z.string(),
  EMAIL: z.string().email('请输入正确的邮箱'),
  PHONE: z.string().regex(/^1[3-9]\d{9}$/, '请输入正确的手机号'),
};

// 使用 noFullWidthString 替换后
export const BaseSchema = {
  STRING: noFullWidthString(),
  EMAIL: noFullWidthString().email('请输入正确的邮箱'),
  PHONE: noFullWidthString().regex(/^1[3-9]\d{9}$/, '请输入正确的手机号'),
};
```

### 3. 自定义错误消息

```typescript
const schema = noFullWidthString('用户名不能包含中文字符');

// 验证示例
schema.parse('username'); // ✅ 通过
schema.parse('用户名'); // ❌ 失败：用户名不能包含中文字符
```

### 4. 链式调用

```typescript
// 邮箱验证 + 全角字符检测
const emailSchema = noFullWidthString().email('请输入正确的邮箱');

// 手机号验证 + 全角字符检测
const phoneSchema = noFullWidthString().regex(/^1[3-9]\d{9}$/, '请输入正确的手机号');

// 长度限制 + 全角字符检测
const usernameSchema = noFullWidthString().min(3).max(20);
```

### 5. 与 optional 包装器组合

```typescript
import { optional, noFullWidthString } from '@/constants/zod-enum';

// 可选的半角字符串
const optionalSchema = optional(noFullWidthString());

optionalSchema.parse(''); // ✅ 通过（空值）
optionalSchema.parse('hello'); // ✅ 通过
optionalSchema.parse('你好'); // ❌ 失败
```

## 完整表单示例

```typescript
import { z } from '#/adapter/form';
import { noFullWidthString, optional } from '@/constants/zod-enum';

const userFormSchema = z.object({
  username: noFullWidthString('用户名不能包含中文字符')
    .min(3, '用户名至少3个字符')
    .max(20, '用户名最多20个字符'),
  
  email: noFullWidthString()
    .email('请输入正确的邮箱格式'),
  
  phone: optional(
    noFullWidthString()
      .regex(/^1[3-9]\d{9}$/, '请输入正确的手机号')
  ),
  
  description: optional(noFullWidthString()),
});

// 使用示例
const result = userFormSchema.safeParse({
  username: 'testuser',
  email: '<EMAIL>',
  phone: '13812345678',
  description: 'This is a description'
});

if (result.success) {
  console.log('验证通过:', result.data);
} else {
  console.log('验证失败:', result.error.issues);
}
```

## 验证示例

```typescript
const schema = noFullWidthString();

// ✅ 通过验证的输入
schema.parse('hello');           // 半角字母
schema.parse('Hello123');        // 半角字母和数字
schema.parse('');                // 空字符串
schema.parse('<EMAIL>'); // 半角邮箱
schema.parse('!@#$%^&*()');      // 半角符号

// ❌ 失败的输入
schema.parse('你好');             // 中文字符
schema.parse('hello世界');        // 混合字符
schema.parse('　');               // 全角空格
schema.parse('！');               // 全角感叹号
schema.parse('１２３');           // 全角数字
schema.parse('ＡＢＣ');           // 全角字母
```

## 注意事项

1. **空值处理**：函数允许空字符串通过验证，如需禁止空值请结合其他验证规则
2. **性能考虑**：正则表达式检测对性能影响很小，可以放心使用
3. **兼容性**：与所有 Zod 方法完全兼容，支持链式调用
4. **错误消息**：默认错误消息为中文，可根据需要自定义

## 迁移指南

如果您想将现有的 BaseSchema 迁移到使用 `noFullWidthString`：

1. 导入函数：`import { noFullWidthString } from '@/constants/zod-enum';`
2. 替换 `z.string()` 为 `noFullWidthString()`
3. 保持其他链式方法不变
4. 根据需要添加自定义错误消息

这样就可以确保所有字符串字段都不能输入全角字符了。
