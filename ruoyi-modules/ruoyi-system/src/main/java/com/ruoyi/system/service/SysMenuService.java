package com.ruoyi.system.service;

import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.util.ObjectUtil;
import com.mybatisflex.core.query.QueryMethods;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.update.UpdateChain;
import com.ruoyi.common.core.constant.Constants;
import com.ruoyi.common.core.constant.UserConstants;
import com.ruoyi.common.core.core.domain.Result;
import com.ruoyi.common.core.utils.MapstructUtils;
import com.ruoyi.common.core.utils.StreamUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.utils.TreeBuildUtils;
import com.ruoyi.common.core.utils.reflect.ObjectUtils;
import com.ruoyi.common.orm.core.service.impl.BaseServiceImpl;
import com.ruoyi.common.satoken.utlis.LoginHelper;
import com.ruoyi.system.domain.bo.sysmenu.SysMenuAddBo;
import com.ruoyi.system.domain.bo.sysmenu.SysMenuEditBo;
import com.ruoyi.system.domain.bo.sysmenu.SysMenuListBo;
import com.ruoyi.system.domain.bo.sysmenu.SysMenuOptionBo;
import com.ruoyi.system.domain.po.SysMenu;
import com.ruoyi.system.domain.po.table.SysMenuTableDef;
import com.ruoyi.system.domain.vo.sysmenu.*;
import com.ruoyi.system.enums.AdminResponseEnum;
import com.ruoyi.system.mapper.SysMenuMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

import static com.mybatisflex.core.query.QueryMethods.*;
import static com.ruoyi.system.domain.po.table.SysMenuTableDef.SYS_MENU;
import static com.ruoyi.system.domain.po.table.SysRoleMenuTableDef.SYS_ROLE_MENU;
import static com.ruoyi.system.domain.po.table.SysRoleTableDef.SYS_ROLE;
import static com.ruoyi.system.domain.po.table.SysUserRoleTableDef.SYS_USER_ROLE;
import static com.ruoyi.system.domain.po.table.SysUserTableDef.SYS_USER;

/**
 * 菜单权限Service业务层处理
 * <p>
 * 本服务类提供菜单权限地增删改查等基础操作，包括：
 * <ul>
 *     <li>分页查询菜单权限列表</li>
 *     <li>查询菜单权限详情</li>
 *     <li>新增菜单权限</li>
 *     <li>修改菜单权限</li>
 *     <li>删除菜单权限</li>
 * </ul>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-14 13:57:20
 */
@Service
public class SysMenuService extends BaseServiceImpl<SysMenuMapper, SysMenu> {
    @Resource
    private SysMenuMapper sysMenuMapper;

    @Resource
    private SysRoleService sysRoleService;
    @Resource
    private SysRoleMenuService sysRoleMenuService;

    /**
     * 获取基础查询对象
     *
     * @return 预设了表信息的查询包装器
     */
    public QueryWrapper query() {
        return super.query().from(SYS_MENU);
    }

    /**
     * 构建查询条件
     * <p>
     * 根据传入的查询参数构建查询条件，MyBatis Flex会自动处理空值
     *
     * @param bo 查询参数对象
     * @return 构建好地查询条件包装器
     */
    private QueryWrapper buildQueryWrapper(SysMenuListBo bo) {
        return super.buildBaseQueryWrapper()
            // 菜单名称
            .and(SYS_MENU.MENU_NAME.like(bo.getMenuName()))
            // 菜单类型（M目录 C菜单 F按钮）
            .and(SYS_MENU.MENU_TYPE.eq(bo.getMenuType()))
            // 显示状态（0显示 1隐藏）
            .and(SYS_MENU.VISIBLE.eq(bo.getVisible()))
            // 菜单图标
            .and(SYS_MENU.ICON.eq(bo.getIcon()))
            // 排序
            .orderBy(SYS_MENU.ORDER_NUM.asc());
    }

    /**
     * 查询菜单权限列表（不分页）
     * <p>
     * 根据查询条件获取所有符合条件的菜单权限
     *
     * @param bo 菜单权限查询参数
     * @return 菜单权限集合
     */
    public List<SysMenuListVo> list(SysMenuListBo bo) {
        QueryWrapper queryWrapper = buildQueryWrapper(bo);
        String parentId = bo.getParentId();

        // 只有当bo为null或者bo的所有业务属性都为空时，才设置默认的parentId = "0"
        if (ObjectUtils.isAllPropertiesEmpty(bo)) {
            parentId = "0";
        }

        queryWrapper.and(SYS_MENU.PARENT_ID.eq(parentId));
        queryWrapper.select(
            SYS_MENU.MENU_NAME,
            SYS_MENU.ID,
            SYS_MENU.PARENT_ID,
            SYS_MENU.ORDER_NUM,
            SYS_MENU.MENU_TYPE,
            SYS_MENU.VISIBLE,
            SYS_MENU.ICON,
            case_(SYS_MENU.MENU_TYPE)
                .when("F").then(false)
                .else_(true)
                .end()
                .as("hasChildren")
        );
        return this.listAs(queryWrapper, SysMenuListVo.class);
    }

    /**
     * 获取菜单权限选择项查询条件
     * <p>
     * 用于下拉选择等场景的查询条件构建
     *
     * @param optionBo 选择项查询参数
     * @param tableDef 菜单权限表定义对象
     * @return 选择项查询条件
     */
    private static QueryWrapper getOptionQueryWrapper(SysMenuOptionBo optionBo, SysMenuTableDef tableDef) {
        // TODO: 根据实际需求添加选项列表查询条件
        // 例如：按状态筛选、按特定字段筛选等
        return new QueryWrapper();
    }

    /**
     * 查询菜单权限管理选择树形列表
     * <p>
     * 用于前端树形选择控件，返回树形结构的菜单权限信息
     *
     * @param optionBo 选择项查询参数
     * @return 树形结构的菜单权限信息列表
     */
    public Result<List<Tree<String>>> optionTree(SysMenuOptionBo optionBo) {
        SysMenuTableDef tableDef = SYS_MENU.as("a");
        QueryWrapper queryWrapper = getOptionQueryWrapper(optionBo, tableDef);
        queryWrapper.select(
                tableDef.ID.as("ID"),
                tableDef.ID.as("value"),
                tableDef.PARENT_ID,
                tableDef.ORDER_NUM,
                tableDef.MENU_NAME.as("label"),
                tableDef.MENU_TYPE,
                tableDef.ICON,
                case_(tableDef.PARENT_ID)
                    .when("0").then(false)
                    .else_(true)
                    .end()
                    .as("isLeaf")
            )
            .from(tableDef)
            .orderBy(tableDef.ORDER_NUM, true);

        List<SysMenuOptionTreeVo> optionVoList = this.listAs(queryWrapper, SysMenuOptionTreeVo.class);
        List<Tree<String>> build = TreeBuildUtils.buildOptionTree(optionVoList, (menu, tree) -> {
            tree.put("menuType", menu.getMenuType());
            tree.put("icon", menu.getIcon());
        });

        return Result.success(build);
    }


    /**
     * 查询菜单权限管理选择树形列表
     * <p>
     * 用于前端树形选择控件，返回树形结构的菜单权限信息
     *
     * @param optionBo 选择项查询参数
     * @return 树形结构的菜单权限信息列表
     */
    public Result<MenuTreeSelectVo> listMenuTreeSelectVo(SysMenuOptionBo optionBo) {
        // 创建一个MenuTreeSelectVo对象，用于封装菜单树结构数据
        MenuTreeSelectVo selectVo = new MenuTreeSelectVo();
        // 调用optionTree方法获取处理后的菜单数据，并设置到selectVo中
        selectVo.setMenus(optionTree(optionBo).getData());
        // 如果角色ID不为空，则根据角色ID获取选中的菜单项，并设置到selectVo中
        if (StringUtils.isNotEmpty(optionBo.getRoleId())) {
            selectVo.setCheckedKeys(selectMenuListByRoleId(optionBo.getRoleId()));
        }
        // 返回成功结果，包含selectVo对象
        return Result.success(selectVo);
    }


    /**
     * 新增菜单权限
     * <p>
     * 将前端传入的数据转换为实体并保存到数据库
     *
     * @param bo 菜单权限新增参数
     * @return 操作结果，成功返回{@code Result.success()}，失败返回包含错误信息的{@code Result.fail()}
     */
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> add(SysMenuAddBo bo) {
        QueryWrapper wrapper = QueryWrapper.create()
            .eq(SysMenu::getMenuName, bo.getMenuName())
            .eq(SysMenu::getParentId, bo.getParentId());
        AdminResponseEnum.MENU_NAME_EXISTS.assertTrue(count(wrapper) > 0);
        SysMenu entity = MapstructUtils.convert(bo, SysMenu.class);
        // 设置祖级列表
        setAncestors(entity, bo.getParentId());
        boolean inserted = this.save(entity);
        return inserted
            ? Result.success()
            : Result.fail("新增菜单权限记录失败！");
    }

    /**
     * 修改菜单权限
     * <p>
     * 更新数据库中已存在的菜单权限
     *
     * @param bo 菜单权限编辑参数
     * @return 操作结果，成功返回{@code Result.success()}，失败返回包含错误信息的{@code Result.fail()}
     */
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> edit(SysMenuEditBo bo) {
        QueryWrapper wrapper = QueryWrapper.create()
            .eq(SysMenu::getMenuName, bo.getMenuName())
            .eq(SysMenu::getParentId, bo.getParentId())
            .ne(SysMenu::getId, bo.getId());
        AdminResponseEnum.MENU_NAME_EXISTS.assertTrue(count(wrapper) > 0);

        SysMenu entity = MapstructUtils.convert(bo, SysMenu.class);
        if (ObjectUtil.isNull(entity) || ObjectUtil.isNull(entity.getId())) {
            return Result.fail("菜单权限ID不能为空");
        }

        // 更新祖级列表字段
        SysMenu newParentEntity = getById(entity.getParentId());
        SysMenu oldEntity = getById(entity.getId());
        if (ObjectUtil.isNotNull(newParentEntity) && ObjectUtil.isNotNull(oldEntity)) {
            String newAncestors = newParentEntity.getAncestors() + "," + newParentEntity.getId();
            String oldAncestors = oldEntity.getAncestors();
            entity.setAncestors(newAncestors);
            editSysMenuChildren(entity.getId(), newAncestors, oldAncestors);
        }
        boolean updated = this.updateById(entity);
        return updated
            ? Result.success()
            : Result.fail("修改菜单权限记录失败!");
    }

    /**
     * 修改子元素关系
     * <p>
     * 更新所有子节点的祖级列表字段
     *
     * @param id           主键ID
     * @param newAncestors 新的父ID集合
     * @param oldAncestors 旧的父ID集合
     */
    private void editSysMenuChildren(String id, String newAncestors, String oldAncestors) {
        // 查询所有子节点
        QueryWrapper queryWrapper = QueryWrapper.create()
            .from(SYS_MENU)
            .where(QueryMethods.findInSet(QueryMethods.string(id), SYS_MENU.ANCESTORS).gt(0));

        List<SysMenu> children = list(queryWrapper);

        // 更新子节点的祖级列表
        for (SysMenu child : children) {
            child.setAncestors(child.getAncestors().replaceFirst(oldAncestors, newAncestors));

            UpdateChain.of(SysMenu.class)
                .set(SysMenu::getAncestors, child.getAncestors())
                .where(SysMenu::getId).eq(child.getId())
                .update();
        }
    }

    /**
     * 设置实体的祖级列表
     * <p>
     * 根据父ID设置实体的祖级列表字段
     *
     * @param entity   需要设置祖级列表的实体
     * @param parentId 父ID
     */
    private void setAncestors(SysMenu entity, String parentId) {
        if (StringUtils.equals(parentId, "0")) {
            entity.setAncestors("0");
        } else {
            SysMenu parentEntity = getById(parentId);
            if (ObjectUtil.isNotNull(parentEntity)) {
                entity.setAncestors(parentEntity.getAncestors() + "," + parentId);
            } else {
                entity.setAncestors("0");
            }
        }
    }

    /**
     * 查询菜单使用数量
     *
     * @param menuId 菜单ID
     * @return 结果
     */
    public boolean checkMenuExistRole(String menuId) {
        int result = sysRoleMenuService.checkMenuExistRole(menuId);
        return result > 0;
    }

    /**
     * 批量删除菜单权限
     * <p>
     * 根据ID数组删除多条菜单权限记录
     *
     * @param ids 需要删除的菜单权限主键数组
     * @return 操作结果，成功返回{@code Result.success()}，失败返回包含错误信息的{@code Result.fail()}
     * @throws IllegalArgumentException 当ids为null或空数组时抛出
     */
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> removeByIds(String[] ids) {
        // 参数校验
        if (ids == null || ids.length == 0) {
            return Result.fail("删除ID不能为空");
        }
        for (String id : ids) {
            if (checkMenuExistRole(id)) {
                return  Result.fail("菜单已分配,不允许删除");
            }
        }

        // 执行批量删除
        return this.removeByIds(Arrays.asList(ids))
            ? Result.success()
            : Result.fail("删除菜单权限记录失败!");
    }

    /**
     * 查询菜单权限详情
     * <p>
     * 根据ID获取单个菜单权限的详细信息
     *
     * @param id 菜单权限主键
     * @return 菜单权限详细信息，如果id为空则返回null
     */
    public SysMenuDetailVo detail(String id) {
        if (StringUtils.isEmpty(id)) {
            return null;
        }

        return this.getOneAs(
            query().where(SYS_MENU.ID.eq(id)),
            SysMenuDetailVo.class
        );
    }

    /**
     * 新增菜单权限（带主键）
     * <p>
     * 使用前端提供的主键值新增记录，通常用于数据导入场景
     *
     * @param bo 菜单权限新增参数（含主键）
     * @return 操作结果，成功返回{@code Result.success()}，失败返回包含错误信息的{@code Result.fail()}
     */
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> insertWithPk(SysMenuAddBo bo) {
        SysMenu entity = MapstructUtils.convert(bo, SysMenu.class);
        // 设置祖级列表
        setAncestors(entity, bo.getParentId());
        // 使用特定的mapper方法执行带主键的插入
        boolean inserted = sysMenuMapper.insertWithPk(entity) > 0;
        return inserted
            ? Result.success()
            : Result.fail("新增菜单权限记录失败！");
    }

    /**
     * 根据用户ID查询权限
     * <p>
     *
     * @param userId 用户ID
     * @return 权限列表
     */
    public Set<String> selectMenuPermsByUserId(String userId) {
        QueryWrapper queryWrapper = QueryWrapper.create()
            .select(distinct(SYS_MENU.PERMS))
            .from(SYS_MENU)
            .leftJoin(SYS_ROLE_MENU).on(SYS_MENU.ID.eq(SYS_ROLE_MENU.MENU_ID))
            .leftJoin(SYS_USER_ROLE).on(SYS_ROLE_MENU.ROLE_ID.eq(SYS_USER_ROLE.ROLE_ID))
            .leftJoin(SYS_ROLE).on(SYS_ROLE.ID.eq(SYS_USER_ROLE.ROLE_ID))
            .and(SYS_ROLE.STATUS.eq("0"))
            .and(SYS_USER_ROLE.USER_ID.eq(userId));

        List<String> perms = this.listAs(queryWrapper, String.class);

        Set<String> permsSet = new HashSet<>();
        for (String perm : perms) {
            if (StringUtils.isNotEmpty(perm)) {
                permsSet.addAll(Arrays.asList(perm.trim().split(",")));
            }
        }
        return permsSet;
    }

    /**
     * 根据用户ID查询菜单
     *
     * @param userId 用户名称
     * @return 菜单列表
     */
    public List<SysMenuVo> selectMenuTreeByUserId(String userId) {
        List<SysMenuVo> menuRouterVos = selectMenuList(userId);
        return getChildPerms(menuRouterVos, "0");
    }


    /**
     * 根据用户ID选择菜单列表
     *
     * @param userId 用户ID，用于判断用户权限并获取相应的菜单列表
     * @return 返回一个SysMenuVo对象的列表，包含用户可访问的菜单信息
     */
    public List<SysMenuVo> selectMenuList(String userId) {
        // 构建基础查询条件，选择distinct的菜单相关字段
        QueryWrapper queryWrapper = super.buildBaseQueryWrapper()
            .select(
                QueryMethods.distinct(
                    SYS_MENU.ID,
                    SYS_MENU.PARENT_ID,
                    SYS_MENU.MENU_NAME,
                    SYS_MENU.PATH,
                    SYS_MENU.COMPONENT,
                    SYS_MENU.QUERY_PARAM,
                    SYS_MENU.VISIBLE,
                    SYS_MENU.PERMS,
                    SYS_MENU.IS_FRAME,
                    SYS_MENU.IS_CACHE,
                    SYS_MENU.MENU_TYPE,
                    SYS_MENU.ICON,
                    SYS_MENU.ORDER_NUM,
                    SYS_MENU.CREATE_TIME
                ));

        // 判断用户是否为超级管理员
        if (LoginHelper.isSuperAdmin(userId)) {
            // 超级管理员查询所有目录和菜单类型，并按父ID和顺序号排序
            queryWrapper.and(SYS_MENU.MENU_TYPE.in(UserConstants.TYPE_DIR, UserConstants.TYPE_MENU))
                .orderBy(SYS_MENU.PARENT_ID.asc(), SYS_MENU.ORDER_NUM.asc());
        } else {
            // 非超级管理员查询其角色权限内的菜单，并按父ID和顺序号排序
            queryWrapper.from(SYS_MENU)
                .leftJoin(SYS_ROLE_MENU).on(SYS_MENU.ID.eq(SYS_ROLE_MENU.MENU_ID))
                .leftJoin(SYS_USER_ROLE).on(SYS_ROLE_MENU.ROLE_ID.eq(SYS_USER_ROLE.ROLE_ID))
                .leftJoin(SYS_ROLE).on(SYS_ROLE.ID.eq(SYS_USER_ROLE.ROLE_ID))
                .leftJoin(SYS_USER).on(SYS_USER.ID.eq(SYS_USER_ROLE.USER_ID))
                .where(SYS_USER.ID.eq(userId))
                .and(SYS_MENU.MENU_TYPE.in("M", "C"))
                .and(SYS_ROLE.STATUS.eq("0"))
                .orderBy(SYS_MENU.PARENT_ID.asc(), SYS_MENU.ORDER_NUM.asc());
        }

        // 执行查询并转换结果为SysMenuVo对象列表
        return this.listAs(queryWrapper, SysMenuVo.class);
    }

    /**
     * 根据角色ID查询菜单树信息
     *
     * @param roleId 角色ID
     * @return 选中菜单列表
     */
    public List<String> selectMenuListByRoleId(String roleId) {
        Boolean menuCheckStrictly = sysRoleService.getById(roleId).getMenuCheckStrictly();

        QueryWrapper queryWrapper = QueryWrapper.create()
            .select(SYS_MENU.ID)
            .from(SYS_MENU)
            .leftJoin(SYS_ROLE_MENU).on(SYS_ROLE_MENU.MENU_ID.eq(SYS_MENU.ID))
            .where(SYS_ROLE_MENU.ROLE_ID.eq(roleId));
        if (menuCheckStrictly) {
            queryWrapper.and(SYS_MENU.ID.notIn(select(SYS_MENU.PARENT_ID).from(SYS_MENU).innerJoin(SYS_ROLE_MENU).on(SYS_MENU.ID.eq(SYS_ROLE_MENU.MENU_ID)).and(SYS_ROLE_MENU.ROLE_ID.eq(roleId))));
        }
        queryWrapper.orderBy(SYS_MENU.PARENT_ID.asc(), SYS_MENU.ORDER_NUM.asc());
        return this.listAs(queryWrapper, String.class);
    }

    /**
     * 构建前端路由所需要的菜单
     *
     * @param menus 菜单列表
     * @return 路由列表
     */
    public List<RouterVo> buildMenus(List<SysMenuVo> menus) {
        List<RouterVo> routers = new LinkedList<>();
        for (SysMenuVo menu : menus) {
            RouterVo router = new RouterVo();
            router.setHidden("1".equals(menu.getVisible()));
            router.setName(getRouteName(menu));
            router.setPath(getRouterPath(menu));
            router.setComponent(getComponent(menu));
            router.setQuery(menu.getQueryParam());
            router.setMeta(new MetaVo(menu.getMenuName(), menu.getIcon(), StringUtils.equals("1", menu.getIsCache()), menu.getPath()));
            List<SysMenuVo> cMenus = menu.getChildren();
            if (StringUtils.isNotEmpty(cMenus) && UserConstants.TYPE_DIR.equals(menu.getMenuType())) {
                router.setAlwaysShow(true);
                router.setRedirect("noRedirect");
                router.setChildren(buildMenus(cMenus));
            } else if (isMenuFrame(menu)) {
                router.setMeta(null);
                List<RouterVo> childrenList = new ArrayList<>();
                RouterVo children = new RouterVo();
                children.setPath(menu.getPath());
                children.setComponent(menu.getComponent());
                children.setName(StringUtils.capitalize(menu.getPath()));
                children.setMeta(new MetaVo(menu.getMenuName(), menu.getIcon(), StringUtils.equals("1", menu.getIsCache()), menu.getPath()));
                children.setQuery(menu.getQueryParam());
                childrenList.add(children);
                router.setChildren(childrenList);
            } else if (StringUtils.equals("0", menu.getParentId()) && isInnerLink(menu)) {
                router.setMeta(new MetaVo(menu.getMenuName(), menu.getIcon()));
                router.setPath("/");
                List<RouterVo> childrenList = new ArrayList<>();
                RouterVo children = new RouterVo();
                String routerPath = innerLinkReplaceEach(menu.getPath());
                children.setPath(routerPath);
                children.setComponent(UserConstants.INNER_LINK);
                children.setName(StringUtils.capitalize(routerPath));
                children.setMeta(new MetaVo(menu.getMenuName(), menu.getIcon(), menu.getPath()));
                childrenList.add(children);
                router.setChildren(childrenList);
            }
            routers.add(router);
        }
        return routers;
    }

    /**
     * 获取路由名称
     *
     * @param menu 菜单信息
     * @return 路由名称
     */
    public String getRouteName(SysMenuVo menu) {
        String routerName = StringUtils.capitalize(menu.getPath());
        // 非外链并且是一级目录（类型为目录）
        if (isMenuFrame(menu)) {
            routerName = StringUtils.EMPTY;
        }
        return routerName;
    }

    /**
     * 获取路由地址
     *
     * @param menu 菜单信息
     * @return 路由地址
     */
    public String getRouterPath(SysMenuVo menu) {
        String routerPath = menu.getPath();
        // 内链打开外网方式
        if (!StringUtils.equals("0", menu.getParentId()) && isInnerLink(menu)) {
            routerPath = innerLinkReplaceEach(routerPath);
        }
        // 非外链并且是一级目录（类型为目录）
        if (StringUtils.equals("0", menu.getParentId()) && UserConstants.TYPE_DIR.equals(menu.getMenuType())
            && UserConstants.NO_FRAME.equals(menu.getIsFrame())) {
            routerPath = "/" + menu.getPath();
        }
        // 非外链并且是一级目录（类型为菜单）
        else if (isMenuFrame(menu)) {
            routerPath = "/";
        }
        return routerPath;
    }

    /**
     * 获取组件信息
     *
     * @param menu 菜单信息
     * @return 组件信息
     */
    public String getComponent(SysMenuVo menu) {
        String component = UserConstants.LAYOUT;
        if (StringUtils.isNotEmpty(menu.getComponent()) && !isMenuFrame(menu)) {
            component = menu.getComponent();
        } else if (StringUtils.isEmpty(menu.getComponent()) && !StringUtils.equals("0", menu.getParentId()) && isInnerLink(menu)) {
            component = UserConstants.INNER_LINK;
        } else if (StringUtils.isEmpty(menu.getComponent()) && isParentView(menu)) {
            component = UserConstants.PARENT_VIEW;
        }
        return component;
    }

    /**
     * 是否为菜单内部跳转
     *
     * @param menu 菜单信息
     * @return 结果
     */
    public boolean isMenuFrame(SysMenuVo menu) {
        return StringUtils.equals("0", menu.getParentId()) && UserConstants.TYPE_MENU.equals(menu.getMenuType())
            && menu.getIsFrame().equals(UserConstants.NO_FRAME);
    }

    /**
     * 是否为内链组件
     *
     * @param menu 菜单信息
     * @return 结果
     */
    public boolean isInnerLink(SysMenuVo menu) {
        return menu.getIsFrame().equals(UserConstants.NO_FRAME) && StringUtils.ishttp(menu.getPath());
    }

    /**
     * 是否为parent_view组件
     *
     * @param menu 菜单信息
     * @return 结果
     */
    public boolean isParentView(SysMenuVo menu) {
        return !StringUtils.equals("0", menu.getParentId()) && UserConstants.TYPE_DIR.equals(menu.getMenuType());
    }

    /**
     * 根据父节点的ID获取所有子节点
     *
     * @param list     分类表
     * @param parentId 传入的父节点ID
     * @return String
     */
    private List<SysMenuVo> getChildPerms(List<SysMenuVo> list, String parentId) {
        List<SysMenuVo> returnList = new ArrayList<>();
        for (SysMenuVo t : list) {
            // 一、根据传入的某个父节点ID,遍历该父节点的所有子节点
            if (StringUtils.equals(t.getParentId(), parentId)) {
                recursionFn(list, t);
                returnList.add(t);
            }
        }
        return returnList;
    }

    /**
     * 递归列表
     */
    private void recursionFn(List<SysMenuVo> list, SysMenuVo t) {
        // 得到子节点列表
        List<SysMenuVo> childList = StreamUtils.filter(list, n -> StringUtils.equals(n.getParentId(), t.getId()));
        t.setChildren(childList);
        for (SysMenuVo tChild : childList) {
            // 判断是否有子节点
            if (list.stream().anyMatch(n -> StringUtils.equals(n.getParentId(), tChild.getId()))) {
                recursionFn(list, tChild);
            }
        }
    }

    /**
     * 内链域名特殊字符替换
     *
     * @return 替换后的内链域名
     */
    public String innerLinkReplaceEach(String path) {
        return StringUtils.replaceEach(path,
            new String[]{Constants.HTTP, Constants.HTTPS, Constants.WWW, ".", ":"},
            new String[]{"", "", "", "/", "/"});
    }

}
