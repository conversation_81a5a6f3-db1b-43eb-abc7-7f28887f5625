<template>
  <!-- 模板部分 -->
  <el-dialog
    v-bind="props.options.attrs"
    v-model="dialogVisible"
    :fullscreen="fullscreen"
    draggable
    :show-close="false"
    :close-on-click-modal="false"
    :destroy-on-close="true"
  >
    <!-- 弹窗头部 -->
    <template #header>
      <div class="dialog-body-title">
        <span class="title-span-div">{{ `${props.options.title}` }}</span>
        <div class="title-action-div">
          <svg-icon v-if="fullscreen" class="button-full" icon-class="exit-fullscreen" @click="toggleFullscreen"></svg-icon>
          <svg-icon v-else class="button-full" icon-class="fullscreen" @click="toggleFullscreen"></svg-icon>
          <svg-icon style="font-size: 20px" class="button-full" icon-class="close" @click="handleClose"></svg-icon>
        </div>
      </div>
    </template>
    <!-- 弹窗中间body -->
    <template #default>
      <div class="dialog-body-div">
        <ContentComponent v-if="!$slots.default" @close="handleClose" @confirm="handleConfirm"></ContentComponent>
        <slot v-else />
      </div>
    </template>
    <!-- 弹窗底部 -->
    <template v-if="props.options.isAction" #footer>
      <!-- 如果没有提供其他footer插槽，就使用默认的 -->
      <div v-if="!$slots.footer" class="dialog-footer">
        <el-button type="primary" @click="handleConfirm">
          {{ props.options.confirmText == null ? '提 交' : props.options.confirmText }}
        </el-button>
        <el-button @click="handleClose">{{ props.options.cancelText == null ? '取 消' : props.options.cancelText }}</el-button>
      </div>
      <!-- 使用传入进来的插槽 -->
      <div v-else class="dialog-footer">
        <slot name="footer"> </slot>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="tsx">
import type { DialogOption } from './type';
import { PropType } from 'vue';

const attrs = useAttrs();
let props = defineProps({
  options: {
    type: Object as PropType<DialogOption>,
    required: true
  }
});
const emits = defineEmits<{
  (e: 'confirm', value: any): void;
  (e: 'close'): void;
}>();
const dialogVisible = defineModel<boolean>(); //显示弹窗
const fullscreen = ref(false); //是否全屏
const toggleFullscreen = () => {
  fullscreen.value = !fullscreen.value;
};
const ContentComponent = () => {
  const content = props.options.content;
  if (content == null) {
    return null;
  } else if (typeof content === 'string') {
    return content;
  } else {
    return h(content, props.options.data);
  }
};
//销毁
const handleClose = () => {
  if (Reflect.has(attrs, 'before-close') && typeof attrs['before-close'] === 'function') {
    attrs['before-close']();
  }
  emits('close');
  dialogVisible.value = false;
};
//保存表单方法
const handleConfirm = (value: any) => {
  emits('confirm', value);
  dialogVisible.value = false;
};
</script>

<style lang="scss">
.dialog-body-title {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .title-action-div {
    display: flex;
    align-items: center;
    .button-full {
      cursor: pointer;
      margin: 7px 30px 0 0;
      color: var(--el-color-white);
      font-size: 14px;
    }
  }

  .title-span-div {
    font-size: 20px;
    font-weight: 500;
    padding-left: 15px;
  }
}
.dialog-body-div {
  max-height: 800px !important;
  display: flex;
  flex-direction: column;
  .el-card.is-always-shadow {
    overflow-y: auto;
  }
}
.action-div {
  display: flex;
  flex-direction: row;
  justify-content: end;
  padding: 10px;
}
.el-dialog__footer {
  padding: 0;
}
.dialog-footer {
  padding: 0 10px 10px 20px;
}
</style>
