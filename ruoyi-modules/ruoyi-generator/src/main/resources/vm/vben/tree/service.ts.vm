import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { ${BusinessName}Form, ${BusinessName}VO } from '#/api/${moduleName}/${kebabCaseBusinessName}/types';
import type { ButtonConfig } from '#/components/ActionButtonGroup/types';

#if($isDialog)
import { useVbenForm, useVbenModal } from '@vben/common-ui';
#elseif($isDrawer)
import { useVbenDrawer, useVbenForm } from '@vben/common-ui';
#end
import { Delete, Edit, Fold, Plus } from '@vben/icons';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  add${BusinessName},
  edit${BusinessName},
  get${BusinessName},
  list${BusinessName},
  remove${BusinessName},
} from '#/api/${moduleName}/${kebabCaseBusinessName}';

import { useFormSchema, useGridColumns, useGridFormSchema } from './data';

export const use${BusinessName}Service = () => {
  // 查询表单和表格配置
  const [BasicTable, gridApi] = useVbenVxeGrid({
    formOptions: {
      schema: useGridFormSchema(),
      commonConfig: {
        labelWidth: 120,
        componentProps: {
          allowClear: true,
        },
      },
    },
    gridOptions: {
      columns: useGridColumns(),
      pagerConfig: { enabled: false },
      proxyConfig: {
        ajax: {
          query: async (_, formValues = {}) => {
            return await list${BusinessName}({
              ...formValues,
            });
          },
        },
      },
      treeConfig: {
        rowField: '${treeCode}',
        parentField: '${treeParentCode}',
        lazy: true,
        hasChildField: 'hasChildren',
        loadMethod({ row }) {
          return load(row);
        },
      },
    } as VxeTableGridOptions,
  });

  // 编辑表单配置
  const [Form, formApi] = useVbenForm({
    schema: useFormSchema(),
    commonConfig: {
      labelWidth: 100,
    },
    showDefaultActions: false,
    wrapperClass: 'md:grid-cols-${editColumns}',
  });

  // 编辑抽屉配置
  #if($isDialog)
  const [FormDialog, formDialogApi] = useVbenModal({
  #elseif($isDrawer)
  const [FormDialog, formDialogApi] = useVbenDrawer({
  #end
    async onConfirm() {
      const { valid } = await formApi.validate();
      if (!valid) return;
      const data = await formApi.getValues<${BusinessName}Form>();
      formDialogApi.lock();
      (data?.${pkColumn.javaField} ? edit${BusinessName}(data) : add${BusinessName}(data))
        .then(() => {
          handleQuery();
          formDialogApi.close();
        })
        .catch(() => {
          formDialogApi.unlock();
        });
    },
    onOpenChange(isOpen) {
      if (isOpen) {
        const data = formDialogApi.getData<${BusinessName}Form>();
        if (data) {
          formApi.setValues(data).then(() => {});
        } else {
          formApi.resetForm().then(() => {});
        }
      }
    },
    destroyOnClose: true,
  });

  // 异步加载方法
  const load = async (row: ${BusinessName}VO) => {
    const values = await gridApi.formApi.getValues();
    const query: DzysQuery = {
      ...structuredClone(values),
      ${treeParentCode}: row.${treeCode},
    };
    return await list${BusinessName}(query);
  };

  /**
   * 刷新表格
   */
  const handleQuery = async () => {
    await gridApi.query();
  };

  /**
   * 创建${functionName}
   */
  const handleCreate = () => {
    formDialogApi.setData(null).open();
  };

  /**
   * 折叠全部
   */
  const collapseAll = () => {
    gridApi.grid?.setAllTreeExpand(false).then(() => {});
  };

  /**
   * 新增子级
   */
  const handleAppend = (row: ${BusinessName}VO) => {
    formDialogApi.setData({ parentId: row.${treeCode} }).open();
  };

  /**
   * 编辑${functionName}
   */
  const handleEdit = async (row: ${BusinessName}VO) => {
    const data = await get${BusinessName}(row.${pkColumn.javaField});
    formDialogApi.setData(data).open();
  };

  /**
   * 删除${functionName}
   */
  const handleDelete = async (row: ${BusinessName}VO) => {
    await window.skynet
      .confirm('确认要删除选中的${functionName}吗?')
      .then(async () => {
        await remove${BusinessName}(row.${pkColumn.javaField}).then(() => {
          handleQuery();
        });
      })
      .catch(() => {});
  };

  /**
   * 工具栏按钮配置
   */
  const useToolbarButtons = (): ButtonConfig[] => {
    return [
      {
        label: '新增${functionName}',
        type: 'primary',
        icon: Plus,
        onClick: handleCreate,
        auth: ['${moduleName}:${businessName}:add'],
      },
      {
        label: '折叠全部',
        icon: Fold,
        onClick: collapseAll,
      },
    ];
  };

  /**
   * 行操作按钮配置
   */
  const useRowButtons = (): ButtonConfig[] => {
    return [
      {
        label: '新增子级',
        icon: Plus,
        link: true,
        onClick: handleAppend,
        auth: ['${moduleName}:${businessName}:add'],
      },
      {
        label: '编辑',
        icon: Edit,
        link: true,
        onClick: handleEdit,
        auth: ['${moduleName}:${businessName}:edit'],
      },
      {
        label: '删除',
        icon: Delete,
        link: true,
        onClick: handleDelete,
        auth: ['${moduleName}:${businessName}:remove'],
      },
    ];
  };

  return {
    BasicTable,
    Form,
    FormDialog,
    collapseAll,
    useToolbarButtons,
    useRowButtons,
  };
};
