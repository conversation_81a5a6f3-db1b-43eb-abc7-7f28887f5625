import type { TenantForm, TenantQuery, TenantVO } from './types';
import http from '@/utils/http';
import type { PageData } from '@/utils/http/interface';

// 查询租户列表
export const listTenant = (query: TenantQuery) => {
  return http.get<PageData<TenantVO>>('/system/tenant/list', query);
};

// 查询租户详细
export const getTenant = (id: string | number) => {
  return http.get<TenantVO>('/system/tenant/' + id);
};

// 新增租户
export const addTenant = (data: TenantForm) => {
  return http.post('/system/tenant', data, {
    headers: {
      isEncrypt: true
    }
  });
};

// 修改租户
export const updateTenant = (data: TenantForm) => {
  return http.put('/system/tenant', data);
};

// 租户状态修改
export const changeTenantStatus = (id: string | number, version: number, status: string) => {
  const data = {
    id,
    version,
    status
  };
  return http.put('/system/tenant/changeStatus', data);
};

// 删除租户
export const delTenant = (id: string | number | Array<string | number>) => {
  return http.delete('/system/tenant/' + id);
};
// 动态切换租户
export const dynamicTenant = (id: string | number) => {
  return http.get('/system/tenant/dynamic/' + id);
};

// 清除动态租户
export const dynamicClear = () => {
  return http.get('/system/tenant/dynamic/clear');
};

// 同步租户套餐
export const syncTenantPackage = (id: string | number, packageId: string | number) => {
  const data = {
    id,
    packageId
  };
  return http.get('/system/tenant/syncTenantPackage', data);
};
