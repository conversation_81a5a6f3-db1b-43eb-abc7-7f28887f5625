// 最终错误信息测试
import { noFullWidthString, withNoFullWidth, BaseSchema, ZodEnum } from './zod-enum';
import { z } from '#/adapter/form';

console.log('=== 最终错误信息测试 ===');

// 1. 测试 noFullWidthString 函数
console.log('\n1. noFullWidthString 函数测试:');
const basicSchema = noFullWidthString();
const basicResult = basicSchema.safeParse('你好');
console.log('基础测试结果:', basicResult);
if (!basicResult.success) {
  console.log('基础错误信息:', basicResult.error.issues[0].message);
}

// 2. 测试自定义错误消息
console.log('\n2. 自定义错误消息测试:');
const customSchema = noFullWidthString('用户名不能包含中文字符');
const customResult = customSchema.safeParse('用户名');
console.log('自定义测试结果:', customResult);
if (!customResult.success) {
  console.log('自定义错误信息:', customResult.error.issues[0].message);
}

// 3. 测试 withNoFullWidth 包装器
console.log('\n3. withNoFullWidth 包装器测试:');
const wrapperSchema = withNoFullWidth(z.string(), '包装器自定义消息');
const wrapperResult = wrapperSchema.safeParse('测试');
console.log('包装器测试结果:', wrapperResult);
if (!wrapperResult.success) {
  console.log('包装器错误信息:', wrapperResult.error.issues[0].message);
}

// 4. 测试 BaseSchema
console.log('\n4. BaseSchema 测试:');

// STRING 测试
const stringResult = BaseSchema.STRING.safeParse('你好');
console.log('BaseSchema.STRING 结果:', stringResult);
if (!stringResult.success) {
  console.log('STRING 错误信息:', stringResult.error.issues[0].message);
}

// EMAIL 测试 - 全角字符
const emailFullWidthResult = BaseSchema.EMAIL.safeParse('测试@example.com');
console.log('EMAIL 全角字符结果:', emailFullWidthResult);
if (!emailFullWidthResult.success) {
  console.log('EMAIL 全角字符错误信息:', emailFullWidthResult.error.issues.map(issue => issue.message));
}

// EMAIL 测试 - 格式错误
const emailFormatResult = BaseSchema.EMAIL.safeParse('invalid-email');
console.log('EMAIL 格式错误结果:', emailFormatResult);
if (!emailFormatResult.success) {
  console.log('EMAIL 格式错误信息:', emailFormatResult.error.issues.map(issue => issue.message));
}

// PHONE 测试 - 全角字符
const phoneFullWidthResult = BaseSchema.PHONE.safeParse('１３８１２３４５６７８');
console.log('PHONE 全角字符结果:', phoneFullWidthResult);
if (!phoneFullWidthResult.success) {
  console.log('PHONE 全角字符错误信息:', phoneFullWidthResult.error.issues.map(issue => issue.message));
}

// PHONE 测试 - 格式错误
const phoneFormatResult = BaseSchema.PHONE.safeParse('12345678901');
console.log('PHONE 格式错误结果:', phoneFormatResult);
if (!phoneFormatResult.success) {
  console.log('PHONE 格式错误信息:', phoneFormatResult.error.issues.map(issue => issue.message));
}

// 5. 测试 ZodEnum
console.log('\n5. ZodEnum 测试:');
const zodStringResult = ZodEnum.STRING.safeParse('你好');
console.log('ZodEnum.STRING 结果:', zodStringResult);
if (!zodStringResult.success) {
  console.log('ZodEnum.STRING 错误信息:', zodStringResult.error.issues[0].message);
}

// 6. 测试正常情况
console.log('\n6. 正常情况测试:');
console.log('正常 STRING:', BaseSchema.STRING.safeParse('hello'));
console.log('正常 EMAIL:', BaseSchema.EMAIL.safeParse('<EMAIL>'));
console.log('正常 PHONE:', BaseSchema.PHONE.safeParse('13812345678'));

// 7. 测试边界情况
console.log('\n7. 边界情况测试:');
console.log('空字符串:', BaseSchema.STRING.safeParse(''));
console.log('纯数字:', BaseSchema.STRING.safeParse('123456'));
console.log('纯字母:', BaseSchema.STRING.safeParse('abcdef'));
console.log('半角符号:', BaseSchema.STRING.safeParse('!@#$%^&*()'));

export {
  basicSchema,
  customSchema,
  wrapperSchema
};
