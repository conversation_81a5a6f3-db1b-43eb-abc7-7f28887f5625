import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { VbenFormSchema } from '#/adapter/form';

import { list${BusinessName}Option } from '#/api/${moduleName}/${businessName}';
import { renderDictTag } from '#/utils/dict';

/**
 * 获取编辑表单的字段配置
 * @returns VbenFormSchema[] 表单配置
 */
export function useFormSchema(): VbenFormSchema[] {
  return [
    #foreach($column in $columns)
      #set($field=$column.javaField)
      #if(($column.insert || $column.edit) && ($column.pk || $field == 'version' ||
        "" != $treeParentCode && ${field} == $treeParentCode))
        #set($parentheseIndex=$column.columnComment.indexOf("（"))
        #if($parentheseIndex != -1)
          #set($comment=$column.columnComment.substring(0, $parentheseIndex))
        #else
          #set($comment=$column.columnComment)
        #end
        #if($column.pk)
    {
      component: 'Input',
      dependencies: {
        show: () => false,
        triggerFields: [''],
      },
      fieldName: '${column.javaField}',
      label: '${column.columnComment}',
    },
        #end
        #if($field == 'version')
    {
      component: 'Input',
      dependencies: {
        show: () => false,
        triggerFields: [''],
      },
      fieldName: '${column.javaField}',
      label: '${column.columnComment}',
    },
        #end
        #if("" != $treeParentCode && ${field} == $treeParentCode)
    {
      component: 'ApiTreeSelect',
      fieldName: '${treeParentCode}',
      label: '${comment}',
      defaultValue: '0',
      rules: 'selectRequired',
      componentProps: {
        placeholder: '请选择父结点ID',
        checkStrictly: true,
        api: async () => {
          const res = await list${BusinessName}Option({ isTree: true });
          const options: DataOptionTree[] = [];
          const data: DataOptionTree = {
            id: '0',
            value: '0',
            label: '根目录',
            isLeaf: false,
            children: [],
          };
          data.children = res;
          options.push(data);
          return options;
        },
        childrenField: 'children',
        labelField: 'label',
        valueField: 'id',
      },
    },
        #end
      #end
    #end
    #foreach($column in $columns)
      #set($field=$column.javaField)
      #if(($column.insert || $column.edit) && !$column.pk && $field != 'version' && ${field} != $treeParentCode)
        #set($parentheseIndex=$column.columnComment.indexOf("（"))
        #if($parentheseIndex != -1)
          #set($comment=$column.columnComment.substring(0, $parentheseIndex))
        #else
          #set($comment=$column.columnComment)
        #end
        #set($dictType=$column.dictType)
        #if($column.htmlType == "input")
    {
      component: 'Input',
      fieldName: '${field}',
      label: '${comment}',
      #if($column.required)
      rules: 'required',
      #end
      componentProps: {
        placeholder: '请输入${comment}',
        clearable: true,
        #if($column.javaType == 'String' && $column.maxLength)
        maxlength: $column.maxLength,
        #end
      },
    },
        #elseif($column.htmlType == "inputNumber")
    {
      component: 'InputNumber',
      fieldName: '${field}',
      label: '${comment}',
      #if($column.required)
      rules: 'required',
      #end
      componentProps: {
        placeholder: '请输入${comment}',
        clearable: true,
        style: {
          width: '100%',
        },
      },
    },
        #elseif($column.htmlType == "textarea")
    {
      component: 'Input',
      fieldName: '${field}',
      label: '${comment}',
      #if($column.required)
      rules: 'required',
      #end
      componentProps: {
        type: 'textarea',
        rows: 2,
        placeholder: '请输入${comment}',
        clearable: true,
        #if($column.javaType == 'String' && $column.maxLength)
        maxlength: $column.maxLength,
        #end
      },
    },
        #elseif($column.htmlType == "select")
    {
      component: 'SkDict',
      fieldName: '${field}',
      label: '${comment}',
      #if($column.required)
      rules: 'required',
      #end
      #if($column.dictType)
      componentProps: {
        data: '${column.dictType}',
        placeholder: '请选择${comment}',
        clearable: true,
      },
      #end
    },
        #elseif($column.htmlType == "radio-group")
    {
      component: 'SkDict',
      fieldName: '${field}',
      label: '${comment}',
      #if($column.required)
      rules: 'required',
      #end
      #if($column.dictType)
      componentProps: {
        data: '${column.dictType}',
        type: 'radio-group',
        placeholder: '请选择${comment}',
        clearable: true,
      },
      #end
    },
        #elseif($column.htmlType == "radio-group-button")
    {
      component: 'SkDict',
      fieldName: '${field}',
      label: '${comment}',
      #if($column.required)
      rules: 'required',
      #end
      #if($column.dictType)
      componentProps: {
        data: '${column.dictType}',
        type: 'radio-group-button',
        placeholder: '请选择${comment}',
        clearable: true,
      },
      #end
    },
        #elseif($column.htmlType == "checkbox")
    {
      component: 'SkDict',
      fieldName: '${field}',
      label: '${comment}',
      #if($column.required)
      rules: 'required',
      #end
      #if($column.dictType)
      componentProps: {
        data: '${column.dictType}',
        type: 'checkbox',
        placeholder: '请选择${comment}',
        clearable: true,
      },
      #end
    },
        #elseif($column.htmlType == "datetime")
    {
      component: 'DatePicker',
      fieldName: '${field}',
      label: '${comment}',
      #if($column.required)
      rules: 'required',
      #end
      componentProps: {
        type: 'date',
        valueFormat: 'YYYY-MM-DD',
        placeholder: '请选择${comment}',
        clearable: true,
      },
    },
        #elseif($column.htmlType == "imageUpload")
    {
      component: 'ImageUpload',
      fieldName: '${field}',
      label: '${comment}',
      #if($column.required)
      rules: 'required',
      #end
    },
        #elseif($column.htmlType == "fileUpload")
    {
      component: 'FileUpload',
      fieldName: '${field}',
      label: '${comment}',
      #if($column.required)
      rules: 'required',
      #end
    },
        #elseif($column.htmlType == "editor")
    {
      component: 'Editor',
      fieldName: '${field}',
      label: '${comment}',
      #if($column.required)
      rules: 'required',
      #end
      componentProps: {
        minHeight: 192,
      },
    },
        #elseif($column.htmlType == "sk-xzqh")
    {
      component: 'SkXzqh',
      fieldName: '${field}',
      label: '${comment}',
      #if($column.required)
        rules: 'required',
      #end
    },
        #elseif($column.htmlType == "sk-dzys")
    {
      component: 'SkDzys',
      fieldName: '${field}',
      label: '${comment}',
      #if($column.required)
        rules: 'required',
      #end
    },
        #elseif($column.htmlType == "sk-dept")
    {
      component: 'SkDept',
      fieldName: '${field}',
      label: '${comment}',
      #if($column.required)
      rules: 'required',
      #end
    },
        #end
      #end
    #end
  ];
}

/**
 * 获取查询表单的字段配置
 * @returns VbenFormSchema[] 查询表单配置
 */
export function useGridFormSchema(): VbenFormSchema[] {
  return [
    #foreach ($column in $columns)
      #set($field=$column.javaField)
      #set($parentheseIndex=$column.columnComment.indexOf("（"))
      #if($parentheseIndex != -1)
        #set($comment=$column.columnComment.substring(0, $parentheseIndex))
      #else
        #set($comment=$column.columnComment)
      #end
      #if($column.query)
        #if($column.htmlType == "input" || $column.htmlType == "textarea")
    {
      component: 'Input',
      fieldName: '${field}',
      label: '${comment}',
      componentProps: {
        placeholder: '请输入${comment}',
        clearable: true,
      },
    },
        #elseif($column.htmlType == "select")
    {
      component: 'SkDict',
      fieldName: '${field}',
      label: '${comment}',
      #if($column.dictType)
      componentProps: {
        data: '${column.dictType}',
        placeholder: '请选择${comment}',
        clearable: true,
      },
      #end
    },
        #elseif($column.htmlType == "radio-group")
    {
      component: 'SkDict',
      fieldName: '${field}',
      label: '${comment}',
      #if($column.dictType)
      componentProps: {
        data: '${column.dictType}',
        type: 'radio-group',
        placeholder: '请选择${comment}',
        clearable: true,
      },
      #end
    },
        #elseif($column.htmlType == "radio-group-button")
    {
      component: 'SkDict',
      fieldName: '${field}',
      label: '${comment}',
      #if($column.dictType)
      componentProps: {
        data: '${column.dictType}',
        type: 'radio-group-button',
        placeholder: '请选择${comment}',
        clearable: true,
      },
      #end
    },
        #elseif($column.htmlType == "checkbox")
    {
      component: 'SkDict',
      fieldName: '${field}',
      label: '${comment}',
      #if($column.dictType)
      componentProps: {
        data: '${column.dictType}',
        type: 'checkbox',
        placeholder: '请选择${comment}',
        clearable: true,
      },
      #end
    },
        #elseif($column.htmlType == "datetime")
          #if($column.queryType == "BETWEEN")
    {
      component: 'DatePicker',
      fieldName: '${field}',
      label: '${comment}',
      componentProps: {
        type: 'daterange',
        valueFormat: 'YYYY-MM-DD HH:mm:ss',
        rangeSeparator: '-',
        startPlaceholder: '开始日期',
        endPlaceholder: '结束日期',
        clearable: true,
      },
    },
          #else
    {
      component: 'DatePicker',
      fieldName: '${field}',
      label: '${comment}',
      componentProps: {
        type: 'date',
        valueFormat: 'YYYY-MM-DD',
        placeholder: '请选择${comment}',
        clearable: true,
      },
    },
          #end
        #elseif($column.htmlType == "sk-xzqh")
    {
      component: 'SkXzqh',
      fieldName: '${field}',
      label: '${comment}',
    },
        #elseif($column.htmlType == "sk-dzys")
    {
      component: 'SkDzys',
      fieldName: '${field}',
      label: '${comment}',
    },
        #elseif($column.htmlType == "sk-dept")
    {
      component: 'SkDept',
      fieldName: '${field}',
      label: '${comment}',
    },
        #end
      #end
    #end
  ];
}

/**
 * 获取表格列配置
 * @returns VxeTableGridOptions['columns'] 表格列配置
 * @description 使用函数的形式返回列数据而不是直接export一个Array常量，是为了响应语言切换时重新翻译表头
 */
export function useGridColumns(): VxeTableGridOptions['columns'] {
  return [
    {
      title: '#',
      type: 'seq',
      width: 60,
      fixed: 'left',
    },
    #foreach ($column in $columns)
      #set($field=$column.javaField)
      #set($htmlType=$column.htmlType)
      #set($parentheseIndex=$column.columnComment.indexOf("（"))
      #if($parentheseIndex != -1)
        #set($comment=$column.columnComment.substring(0, $parentheseIndex))
      #else
        #set($comment=$column.columnComment)
      #end
      #if($column.list && !$column.pk && ${field} != $treeParentCode)
    {
      field: '${field}',
      title: '${comment}',
      #if($field == $treeName)
      fixed: 'left',
      align: 'left',
      treeNode: true,
      #else
      width: 140,
      minWidth: 200,
      align: 'center',
        #if($htmlType == "select" || $htmlType == "radio-group" || $htmlType == "radio-group-button" || $htmlType == "checkbox")
      slots: {
        default: ({ row }) => {
          return renderDictTag(row.${field}, '$column.dictType');
        },
      },
        #end
      #end
    },
      #end
    #end
    {
      title: '操作',
      field: 'action',
      fixed: 'right',
      slots: { default: 'action' },
      width: 240,
    },
  ];
}
