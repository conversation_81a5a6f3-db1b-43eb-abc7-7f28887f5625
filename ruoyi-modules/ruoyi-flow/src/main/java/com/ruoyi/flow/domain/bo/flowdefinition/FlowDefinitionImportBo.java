package com.ruoyi.flow.domain.bo.flowdefinition;

import cn.idev.excel.annotation.ExcelProperty;
import com.ruoyi.common.excel.annotation.ExcelDictFormat;
import com.ruoyi.common.excel.convert.ExcelDictConvert;
import lombok.Data;
import java.io.Serial;
import java.io.Serializable;
import lombok.NoArgsConstructor;

/**
 * 流程定义导入视图对象 flow_definition
 *
 * <AUTHOR>
 * @date 2025-01-06 10:04:52
 */
@Data
@NoArgsConstructor
public class FlowDefinitionImportBo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /** 主键id */
    @ExcelProperty(value = "主键id")
    private Long id;
    /** 流程编码 */
    @ExcelProperty(value = "流程编码")
    private String flowCode;
    /** 流程名称 */
    @ExcelProperty(value = "流程名称")
    private String flowName;
    /** 流程类别 */
    @ExcelProperty(value = "流程类别")
    private String category;
    /** 流程版本 */
    @ExcelProperty(value = "流程版本")
    private String version;
    /** 是否发布（0未发布 1已发布 9失效） */
    @ExcelProperty(value = "是否发布", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0=未发布,1=已发布,9=失效")
    private Integer isPublish;
    /** 审批表单是否自定义（Y是 N否） */
    @ExcelProperty(value = "审批表单是否自定义", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "Y=是,N=否")
    private String formCustom;
    /** 审批表单路径 */
    @ExcelProperty(value = "审批表单路径")
    private String formPath;
    /** 流程激活状态（0挂起 1激活） */
    @ExcelProperty(value = "流程激活状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0=挂起,1=激活")
    private Integer activityStatus;
    /** 监听器类型 */
    @ExcelProperty(value = "监听器类型")
    private String listenerType;
    /** 监听器路径 */
    @ExcelProperty(value = "监听器路径")
    private String listenerPath;
    /** 扩展字段，预留给业务系统使用 */
    @ExcelProperty(value = "扩展字段，预留给业务系统使用")
    private String ext;
    /** 删除标志 */
    @ExcelProperty(value = "删除标志")
    private String delFlag;
}
