<template>
  <section class="app-main">
    <el-main>
      <el-config-provider :message="{ max: 1 }">
        <router-view v-slot="{ Component, route }">
          <transition :enter-active-class="animante" mode="out-in">
            <keep-alive :include="tagsViewStore.cachedViews">
              <component :is="Component" v-if="!route.meta.link" :key="route.path" />
            </keep-alive>
          </transition>
        </router-view>
      </el-config-provider>
    </el-main>
    <iframe-toggle />
  </section>
</template>

<script setup name="AppMain" lang="ts">
import useSettingsStore from '@/store/modules/settings';
import useTagsViewStore from '@/store/modules/tagsView';

import IframeToggle from './IframeToggle/index.vue';

const tagsViewStore = useTagsViewStore();

// 随机动画集合
const animante = ref<string>('');
const animationEnable = ref(useSettingsStore().animationEnable);
watch(
  () => useSettingsStore().animationEnable,
  (val) => {
    animationEnable.value = val;
    if (val) {
      animante.value = window.skynet?.animate.animateList[
        Math.round(Math.random() * window.skynet?.animate.animateList.length)
      ] as string;
    } else {
      animante.value = window.skynet?.animate.defaultAnimate as string;
    }
  },
  { immediate: true }
);
</script>

<style lang="scss" scoped>
.app-main {
  /* 50= navbar  50  */
  flex: 1;
  height: 0;
  //min-height: calc(100vh - 50px);
  width: 100%;
  position: relative;
  overflow: auto;
}
.el-main {
  box-sizing: border-box;
  padding: 10px 12px;
  overflow-x: hidden;
  background-color: var(--el-bg-color-page);
}
.fixed-header + .app-main {
  padding-top: 50px;
}

.hasTagsView {
  .app-main {
    /* 84 = navbar + tags-view = 50 + 34 */
    //min-height: calc(100vh - 84px);
    flex: 1;
    height: 0;
  }

  .fixed-header + .app-main {
    padding-top: 84px;
  }
}
</style>
<style lang="scss">
// fix css style bug in open el-dialog
.el-popup-parent--hidden {
  .fixed-header {
    padding-right: 6px;
  }
}

::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background-color: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background-color: #c0c0c0;
  border-radius: 3px;
}
</style>
