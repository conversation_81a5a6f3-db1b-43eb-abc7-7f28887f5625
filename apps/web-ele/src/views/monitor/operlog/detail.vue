<script setup lang="ts">
import type { OperLogVO } from '#/api/monitor/operlog/types';

import { computed, shallowRef } from 'vue';

import { useVbenDrawer } from '@vben/common-ui';

import { renderHttpMethodTag, renderJsonPreview } from '#/utils/render';

const [FormDialog, formDialogApi] = useVbenDrawer({
  async onOpenChange(open: boolean) {
    if (open) {
      currentLog.value = formDialogApi.getData() as OperLogVO;
    }
  },
  onClosed() {
    currentLog.value = null;
  },
});

const currentLog = shallowRef<null | OperLogVO>(null);
const actionInfo = computed(() => {
  if (!currentLog.value) {
    return '-';
  }
  const data = currentLog.value;
  return `账号: ${data.operName} / ${data.deptName} / ${data.operIp} / ${data.operLocation}`;
});
</script>

<template>
  <FormDialog :footer="false" class="w-[600px]" title="查看日志">
    <el-descriptions
      v-if="currentLog"
      size="small"
      border
      :column="1"
      :label-style="{ minWidth: '130px', width: '130px' }"
    >
      <el-descriptions-item label="日志编号">
        {{ currentLog.id }}
      </el-descriptions-item>
      <el-descriptions-item label="操作结果">
        <DictTag data="xtzt" :value="currentLog.status" />
      </el-descriptions-item>
      <el-descriptions-item label="操作类别">
        <div class="flex gap-2">
          <el-tag>{{ currentLog.title }}</el-tag>
          <DictTag data="czlx" :value="currentLog.businessType" />
        </div>
      </el-descriptions-item>
      <el-descriptions-item label="操作信息">
        {{ actionInfo }}
      </el-descriptions-item>
      <el-descriptions-item label="请求信息">
        <div class="max-w-[495px] overflow-y-auto">
          <component :is="renderHttpMethodTag(currentLog.requestMethod)" />
          {{ currentLog.operUrl }}
        </div>
      </el-descriptions-item>
      <el-descriptions-item v-if="currentLog.errorMsg" label="异常信息">
        <div class="max-w-[495px] overflow-y-auto">
          <span class="font-semibold text-red-600">
            {{ currentLog.errorMsg }}
          </span>
        </div>
      </el-descriptions-item>
      <el-descriptions-item label="方法">
        {{ currentLog.method }}
      </el-descriptions-item>
      <el-descriptions-item label="请求参数">
        <div class="max-w-[495px] overflow-y-auto">
          <component :is="renderJsonPreview(currentLog.operParam)" />
        </div>
      </el-descriptions-item>
      <el-descriptions-item v-if="currentLog.jsonResult" label="响应参数">
        <div class="max-h-[300px] overflow-y-auto">
          <component :is="renderJsonPreview(currentLog.jsonResult)" />
        </div>
      </el-descriptions-item>
      <el-descriptions-item label="请求耗时">
        {{ `${currentLog.costTime} ms` }}
      </el-descriptions-item>
      <el-descriptions-item label="操作时间">
        {{ `${currentLog.operTime}` }}
      </el-descriptions-item>
    </el-descriptions>
  </FormDialog>
</template>

<style scoped></style>
