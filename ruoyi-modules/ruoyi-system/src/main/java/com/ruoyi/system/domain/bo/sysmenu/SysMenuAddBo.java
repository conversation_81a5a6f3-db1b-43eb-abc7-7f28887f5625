package com.ruoyi.system.domain.bo.sysmenu;

import com.ruoyi.system.domain.po.SysMenu;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import jakarta.validation.constraints.*;
import java.io.Serial;
import java.io.Serializable;

/**
 * 菜单权限业务对象 sys_menu
 *
 * <AUTHOR>
 * @since 2025-04-16 14:23:25
 */
@Data
@AutoMapper(target = SysMenu.class, reverseConvertGenerate = false)
public class SysMenuAddBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 菜单名称
     */
    @NotBlank(message = "菜单名称不能为空")
    private String menuName;

    /**
     * 父菜单ID
     */
    @NotBlank(message = "父菜单ID不能为空")
    private String parentId;

    /**
     * 显示顺序
     */
    @NotNull(message = "显示顺序不能为空")
    private Integer orderNum;

    /**
     * 路由地址
     */
    @NotBlank(message = "路由地址不能为空")
    private String path;

    /**
     * 组件路径
     */
    private String component;

    /**
     * 路由参数
     */
    private String queryParam;

    /**
     * 是否为外链（0是 1否）
     */
    @NotBlank(message = "是否为外链（0是 1否）不能为空")
    private String isFrame;

    /**
     * 是否缓存（0缓存 1不缓存）
     */
    @NotBlank(message = "是否缓存（0缓存 1不缓存）不能为空")
    private String isCache;

    /**
     * 菜单类型（M目录 C菜单 F按钮）
     */
    @NotBlank(message = "菜单类型（M目录 C菜单 F按钮）不能为空")
    private String menuType;

    /**
     * 显示状态（0显示 1隐藏）
     */
    @NotBlank(message = "显示状态（0显示 1隐藏）不能为空")
    private String visible;

    /**
     * 菜单状态（0正常 1停用）
     */
    @NotBlank(message = "菜单状态（0正常 1停用）不能为空")
    private String status;

    /**
     * 权限标识
     */
    private String perms;

    /**
     * 菜单图标
     */
    private String icon;

    /**
     * 备注
     */
    private String remark;

}
