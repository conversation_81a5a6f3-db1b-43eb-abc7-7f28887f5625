import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';
import type { Recordable } from '@vben/types';

import { Checkbox, Input } from '@vben-core/shadcn-ui';

import { ElInputNumber, ElSelectV2 } from 'element-plus';

import SkDict from '#/components/sk-dict/index.vue';

const JavaTypes: string[] = [
  'Long',
  'String',
  'Integer',
  'Double',
  'BigDecimal',
  'Boolean',
  'LocalDate',
  'LocalDateTime',
];

const queryTypeOptions = [
  { label: '=', value: 'EQ' },
  { label: '!=', value: 'NE' },
  { label: '>', value: 'GT' },
  { label: '>=', value: 'GE' },
  { label: '<', value: 'LT' },
  { label: '<=', value: 'LE' },
  { label: 'LIKE', value: 'LIKE' },
  { label: 'BETWEEN', value: 'BETWEEN' },
];

const componentsOptions = [
  { label: '文本框', value: 'input' },
  { label: '文本域', value: 'textarea' },
  { label: '数字输入框', value: 'inputNumber' },
  { label: '下拉框', value: 'select' },
  { label: '单选框', value: 'radio-group' },
  { label: '单选按钮框', value: 'radio-group-button' },
  { label: '复选框', value: 'checkbox' },
  { label: '复选框', value: 'checkbox' },
  { label: '日期控件', value: 'datetime' },
  { label: '图片上传', value: 'imageUpload' },
  { label: '文件上传', value: 'fileUpload' },
  { label: '富文本', value: 'editor' },
  { label: '行政区划', value: 'sk-xzqh' },
  { label: '地址元素', value: 'sk-dzys' },
  { label: '机构信息', value: 'sk-dept' },
];

const validateTypeOptions = [
  { label: '手机号', value: 'mobile' },
  { label: '邮箱', value: 'email' },
  { label: '身份证', value: 'idcard' },
  { label: '数字', value: 'number' },
  { label: '字母', value: 'letter' },
  { label: '字母数字', value: 'alphanumeric' },
  { label: '网址', value: 'url' },
  { label: '正整数', value: 'positiveInteger' },
];

// 验证类型与正则表达式的映射
const dictOptions = reactive<{ label: string; value: string }[]>([
  { label: '未设置', value: '' },
]);

function renderBooleanTag(row: Recordable<any>, field: string) {
  const value = row[field] ? '是' : '否';
  const className = row[field] ? 'text-green-500' : 'text-red-500';
  return <span class={className}>{value}</span>;
}

function renderBooleanCheckbox(row: Recordable<any>, field: string) {
  return <Checkbox v-model:checked={row[field]}></Checkbox>;
}

export const validRules: VxeTableGridOptions['editRules'] = {
  columnComment: [{ required: true, content: '请输入' }],
  javaField: [{ required: true, content: '请输入' }],
  maxLength: [
    {
      validator: ({ cellValue, row }) => {
        if (row.javaType === 'String' && !cellValue) {
          return new Error('String类型字段最大长度不能为空');
        }
        if (cellValue && cellValue <= 0) {
          return new Error('最大长度必须大于0');
        }
      },
    },
  ],
};

export const vxeTableColumns: VxeTableGridOptions['columns'] = [
  {
    title: '序号',
    type: 'seq',
    fixed: 'left',
    width: '50',
    align: 'center',
  },
  {
    title: '字段列名',
    field: 'columnName',
    showOverflow: 'tooltip',
    width: '180',
    fixed: 'left',
  },
  {
    title: '字段描述',
    field: 'columnComment',
    fixed: 'left',
    width: '180',
    slots: {
      edit: ({ row }) => {
        return <Input v-model={row.columnComment}></Input>;
      },
    },
    editRender: {},
  },
  {
    title: 'db类型',
    width: '180',
    field: 'columnType',
    showOverflow: 'tooltip',
  },
  {
    title: 'Java类型',
    field: 'javaType',
    width: '130',
    slots: {
      edit: ({ row }) => {
        const javaTypeOptions = JavaTypes.map((type) => ({
          label: type,
          value: type,
        }));
        return (
          <ElSelectV2
            class="w-full"
            options={javaTypeOptions}
            v-model={row.javaType}
          ></ElSelectV2>
        );
      },
    },
    editRender: {},
  },
  {
    title: 'Java属性名',
    field: 'javaField',
    showOverflow: 'tooltip',
    width: '130',
    slots: {
      edit: ({ row }) => {
        return <Input v-model={row.javaField}></Input>;
      },
    },
    editRender: {},
  },
  {
    title: '最大长度',
    field: 'maxLength',
    showOverflow: 'tooltip',
    align: 'center',
    width: 100,
    slots: {
      edit: ({ row }) => {
        const disabled = row.javaType !== 'String';
        return (
          <ElInputNumber
            controls-position="right"
            disabled={disabled}
            v-model={row.maxLength}
          ></ElInputNumber>
        );
      },
    },
    editRender: {},
  },
  {
    title: '插入',
    field: 'insert',
    showOverflow: 'tooltip',
    align: 'center',
    width: 70,
    slots: {
      default: ({ row }) => {
        return renderBooleanTag(row, 'insert');
      },
      edit: ({ row }) => {
        return renderBooleanCheckbox(row, 'insert');
      },
    },
    editRender: {},
  },
  {
    title: '编辑',
    field: 'edit',
    showOverflow: 'tooltip',
    align: 'center',
    width: 70,
    slots: {
      default: ({ row }) => {
        return renderBooleanTag(row, 'edit');
      },
      edit: ({ row }) => {
        return renderBooleanCheckbox(row, 'edit');
      },
    },
    editRender: {},
  },
  {
    title: '列表',
    field: 'list',
    showOverflow: 'tooltip',
    align: 'center',
    width: 70,
    slots: {
      default: ({ row }) => {
        return renderBooleanTag(row, 'list');
      },
      edit: ({ row }) => {
        return renderBooleanCheckbox(row, 'list');
      },
    },
    editRender: {},
  },
  {
    title: '查询',
    field: 'query',
    showOverflow: 'tooltip',
    align: 'center',
    width: 70,
    slots: {
      default: ({ row }) => {
        return renderBooleanTag(row, 'query');
      },
      edit: ({ row }) => {
        return renderBooleanCheckbox(row, 'query');
      },
    },
    editRender: {},
  },
  {
    title: '查询方式',
    field: 'queryType',
    showOverflow: 'tooltip',
    align: 'center',
    width: 100,
    slots: {
      default: ({ row }) => {
        const queryType = row.queryType;
        const found = queryTypeOptions.find((item) => item.value === queryType);
        if (found) {
          return found.label;
        }
        return queryType;
      },
      edit: ({ row }) => {
        return (
          <ElSelectV2
            class="w-full"
            options={queryTypeOptions}
            v-model={row.queryType}
          ></ElSelectV2>
        );
      },
    },
    editRender: {},
  },
  {
    title: '必填',
    field: 'required',
    showOverflow: 'tooltip',
    align: 'center',
    width: 70,
    slots: {
      default: ({ row }) => {
        return renderBooleanTag(row, 'required');
      },
      edit: ({ row }) => {
        return renderBooleanCheckbox(row, 'required');
      },
    },
    editRender: {},
  },
  {
    title: '唯一',
    field: 'unique',
    showOverflow: 'tooltip',
    align: 'center',
    width: 70,
    slots: {
      default: ({ row }) => {
        return renderBooleanTag(row, 'unique');
      },
      edit: ({ row }) => {
        return renderBooleanCheckbox(row, 'unique');
      },
    },
    editRender: {},
  },
  {
    title: '验证类型',
    field: 'validateType',
    showOverflow: 'tooltip',
    align: 'center',
    width: 120,
    slots: {
      default: ({ row }) => {
        const validateType = row.validateType;
        const found = validateTypeOptions.find(
          (item) => item.value === validateType,
        );
        if (found) {
          return found.label;
        }
        return validateType;
      },
      edit: ({ row }) => {
        return (
          <ElSelectV2
            class="w-full"
            clearable
            options={validateTypeOptions}
            placeholder="请选择验证类型"
            v-model={row.validateType}
          ></ElSelectV2>
        );
      },
    },
    editRender: {},
  },
  {
    title: '显示类型',
    field: 'htmlType',
    showOverflow: 'tooltip',
    fixed: 'right',
    align: 'center',
    width: 100,
    slots: {
      default: ({ row }) => {
        const htmlType = row.htmlType;
        const found = componentsOptions.find((item) => item.value === htmlType);
        if (found) {
          return found.label;
        }
        return htmlType;
      },
      edit: ({ row }) => {
        return (
          <ElSelectV2
            class="w-full"
            clearable
            options={componentsOptions}
            v-model={row.htmlType}
          ></ElSelectV2>
        );
      },
    },
    editRender: {},
  },
  {
    title: '字典类型',
    field: 'dictType',
    showOverflow: 'tooltip',
    align: 'center',
    fixed: 'right',
    titlePrefix: {
      message: `仅'下拉框', '单选框', '复选框'支持字典类型`,
    },
    minWidth: 220,
    slots: {
      default: ({ row }) => {
        const dictType = row.dictType;
        const found = dictOptions.find((item) => item.value === dictType);
        if (found) {
          return found.label;
        }
        return dictType;
      },
      edit: ({ row }) => {
        const disabled =
          row.htmlType !== 'select' &&
          row.htmlType !== 'radio-group' &&
          row.htmlType !== 'checkbox' &&
          row.htmlType !== 'radio-group-button';
        return (
          <SkDict
            clearable
            disabled={disabled}
            filterable
            placeholder="请选择"
            sjlx={'1'}
            v-model={row.dictType}
          ></SkDict>
        );
      },
    },
    editRender: {},
  },
];
