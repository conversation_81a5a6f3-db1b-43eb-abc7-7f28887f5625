import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { VbenFormSchema } from '#/adapter/form';

import { getPopupContainer } from '@vben/utils';

import dayjs from 'dayjs';

import { z } from '#/adapter/form';
import { listTenantPackageOption } from '#/api/system/tenant-package';

/**
 * 获取编辑表单的字段配置
 * @returns VbenFormSchema[] 表单配置
 */
const defaultExpireTime = dayjs()
  .add(365, 'days')
  .startOf('day')
  .format('YYYY-MM-DD HH:mm:ss');

export function useFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      dependencies: {
        show: () => false,
        triggerFields: [''],
      },
      fieldName: 'id',
      label: 'id',
    },
    {
      component: 'Input',
      dependencies: {
        show: () => false,
        triggerFields: [''],
      },
      fieldName: 'version',
      label: '乐观锁',
    },
    {
      component: 'Input',
      dependencies: {
        show: () => false,
        triggerFields: [''],
      },
      fieldName: 'tenantId',
      label: 'tenantId',
    },
    {
      component: 'SkDivider',
      componentProps: {
        content: '基本信息',
      },
      fieldName: 'divider1',
      hideLabel: true,
    },
    {
      component: 'Input',
      fieldName: 'companyName',
      label: '企业名称',
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'contactUserName',
      label: '联系人',
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'contactPhone',
      label: '联系电话',
      rules: z
        .string()
        .regex(/^1[3-9]\d{9}$/, { message: '请输入正确的联系电话' }),
    },
    {
      component: 'SkDivider',
      componentProps: {
        content: '管理员信息',
      },
      fieldName: 'divider2',
      hideLabel: true,
      dependencies: {
        if: (values) => !values?.id,
        triggerFields: ['id'],
      },
    },
    {
      component: 'Input',
      fieldName: 'username',
      label: '用户账号',
      rules: 'required',
      dependencies: {
        if: (values) => !values?.id,
        triggerFields: ['id'],
      },
    },
    {
      component: 'Input',
      fieldName: 'password',
      label: '用户密码',
      componentProps: {
        maxlength: 20,
        type: 'password',
      },
      dependencies: {
        if: (values) => !values?.id,
        triggerFields: ['id'],
      },
      rules: z
        .string()
        .min(5, { message: '密码长度为5 - 20' })
        .max(20, { message: '密码长度为5 - 20' }),
    },
    {
      component: 'SkDivider',
      componentProps: {
        content: '租户设置',
      },
      fieldName: 'divider3',
      hideLabel: true,
    },
    {
      component: 'ApiSelect',
      componentProps: {
        getPopupContainer,
        api: listTenantPackageOption,
        filterable: true,
        clearable: true,
      },
      fieldName: 'packageId',
      label: '租户套餐',
      rules: 'selectRequired',
    },
    {
      component: 'DatePicker',
      componentProps: {
        format: 'YYYY-MM-DD HH:mm:ss',
        showTime: true,
        valueFormat: 'YYYY-MM-DD HH:mm:ss',
      },
      defaultValue: defaultExpireTime,
      fieldName: 'expireTime',
      help: `已经设置过期时间不允许重置为'无期限'\n即在开通时未设置无期限 以后都不允许设置`,
      label: '过期时间',
    },
    {
      component: 'InputNumber',
      componentProps: {
        min: -1,
      },
      defaultValue: -1,
      fieldName: 'accountCount',
      help: '-1不限制用户数量',
      label: '用户数量',
      renderComponentContent(model) {
        return {
          prefix: () => (model.accountCount === -1 ? '不限制数量' : '输入数量'),
        };
      },
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'domain',
      help: '可填写域名/端口 填写域名如: www.test.com 或者 www.test.com:8080 填写ip:端口如: 127.0.0.1:8080',
      label: '绑定域名',
      renderComponentContent() {
        return {
          prepend: () => 'http(s)://',
        };
      },
      rules: z
        .string()
        .refine(
          (domain) =>
            !(domain.startsWith('http://') || domain.startsWith('https://')),
          { message: '请输入正确的域名, 不需要http(s)' },
        )
        .optional(),
    },
    {
      component: 'SkDict',
      fieldName: 'status',
      label: '状态',
      rules: 'required',
      defaultValue: '0',
      componentProps: {
        data: 'xtkg',
        type: 'radio-group-button',
        placeholder: '请选择状态',
        clearable: true,
      },
    },
    {
      component: 'SkDivider',
      componentProps: {
        content: '企业信息',
      },
      fieldName: 'divider4',
      hideLabel: true,
    },
    {
      component: 'Input',
      fieldName: 'address',
      label: '企业地址',
    },
    {
      component: 'Input',
      fieldName: 'licenseNumber',
      label: '企业代码',
    },
    {
      component: 'Input',
      fieldName: 'intro',
      componentProps: {
        rows: 3,
        type: 'textarea',
      },
      formItemClass: 'items-start',
      label: '企业介绍',
    },
    {
      component: 'Input',
      componentProps: {
        rows: 3,
        type: 'textarea',
      },
      fieldName: 'remark',
      formItemClass: 'items-start',
      label: '备注',
    },
  ];
}

/**
 * 获取查询表单的字段配置
 * @returns VbenFormSchema[] 查询表单配置
 */
export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'contactUserName',
      label: '联系人',
      componentProps: {
        placeholder: '请输入联系人',
        clearable: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'contactPhone',
      label: '联系电话',
      componentProps: {
        placeholder: '请输入联系电话',
        clearable: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'companyName',
      label: '企业名称',
      componentProps: {
        placeholder: '请输入企业名称',
        clearable: true,
      },
    },
    {
      component: 'DatePicker',
      fieldName: 'expireTime',
      label: '过期时间',
      componentProps: {
        type: 'date',
        valueFormat: 'YYYY-MM-DD',
        placeholder: '请选择过期时间',
        clearable: true,
      },
    },
  ];
}

/**
 * 获取表格列配置
 * @returns VxeTableGridOptions['columns'] 表格列配置
 * @description 使用函数的形式返回列数据而不是直接export一个Array常量，是为了响应语言切换时重新翻译表头
 */
export function useGridColumns(): VxeTableGridOptions['columns'] {
  return [
    {
      title: '#',
      type: 'seq',
      width: 60,
      fixed: 'left',
    },
    {
      field: 'contactUserName',
      title: '联系人',
      align: 'center',
    },
    {
      field: 'contactPhone',
      title: '联系电话',
      align: 'center',
    },
    {
      field: 'companyName',
      title: '企业名称',
      align: 'center',
    },
    {
      field: 'expireTime',
      title: '过期时间',
      align: 'center',
    },
    {
      field: 'status',
      title: '租户状态',
      align: 'center',
      slots: { default: 'status' },
      minWidth: 80,
    },
    {
      title: '操作',
      field: 'action',
      fixed: 'right',
      slots: { default: 'action' },
      width: 240,
    },
  ];
}
