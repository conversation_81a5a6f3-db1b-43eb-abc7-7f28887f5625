<script setup name="Leave" lang="ts">
import { listLeave } from '@/api/flow/leave';
import { useLeave } from '@/views/flow/leave/utils';
const { skTable, columns, openDialog, handleExport, handleDelete,showflowChart ,pageSet} = useLeave();
</script>

<template>
  <div class="table-box">
    <SkTable
      ref="skTable"
      row-key="${treeCode}"
      :columns="columns"
      :request-api="listLeave"
      :search-col="{ xs: 1, sm: 1, md: 2, lg: 3, xl: 3 }"
    >
      <!-- 表格 header 按钮 -->
      <template #tableHeader>
        <el-button v-hasPermi="['flow:leave:add']" type="primary" icon="CirclePlus" @click="openDialog('新增')">
          新增
        </el-button>
        <el-button v-hasPermi="['flow:leave:export']" type="primary" plain icon="Download" @click="handleExport">
          导出
        </el-button>
      </template>
      <!-- 表格操作 -->
      <template #operation="scope">
        <div>
<!--          <el-button v-hasPermi="['flow:leave:edit']" type="primary" link icon="EditPen" @click="tb = true" >
            查看流程图
          </el-button>-->
          <el-button v-hasPermi="['flow:leave:edit']" type="primary" link icon="EditPen" @click="showflowChart(scope.row)" >
            查看流程图
          </el-button>
          <el-button v-hasPermi="['flow:leave:edit']" type="primary" link icon="EditPen" @click="openDialog('编辑', scope.row)">
            审批
          </el-button>
          <el-button v-hasPermi="['flow:leave']" type="danger" link icon="Delete" @click="handleDelete(scope.row)">
            删除
          </el-button>
        </div>
      </template>
    </SkTable>
    <el-dialog  v-model="pageSet.flowChart" width="60%">
      <img :src="pageSet.imgUrl" width="100%" style="margin:0 auto"/>
    </el-dialog>
  </div>
</template>

