# IDEA相关配置
- IDEA最低版本2024.1
- 必须配置

![img.png](images/img.png)
![img_1.png](images/img_1.png)

# 安装依赖
npm config set registry "http://***********:4873"

# 全局安装依赖
## 方法一
npm install
# 方法二
### 安装pnpm
npm install pnpm -g
### 安装依赖
pnpm i

# 启动服务
pnpm dev

前端浏览器访问 http://localhost:80

# 发布

## 构建生产环境
pnpm build:prod

# 常见问题



pnpm : 无法加载文件 C:\Users\<USER>\AppData\Roaming\npm\pnpm.ps1，因为在此系统上禁止运行脚本。有关详细信息，请参阅 https:/go.microsoft.com/fwlink/?LinkID=135170 中的 about_Execution_Policies
管理员打开Windows PowerShell 执行
set-ExecutionPolicy RemoteSigned