/**
 * 完全按照您提供的规则实现的中文姓名验证
 * 
 * 原规则：
 * $.validator.addMethod("isChinese", function(value, element) {
 *     var flag = /^[\u0391-\uFFE5]+$|^[\u0391-\uFFE5]+·?[\u0391-\uFFE5]+$/.test(value);
 *     if (flag){
 *         flag = /^[^【】、﹒▪•]+$/.test(value);
 *     }
 *     return this.optional(element) || flag;
 * }, "只能输入中文字符");
 */

/**
 * 第一步验证：中文字符格式验证
 * 正则：/^[\u0391-\uFFE5]+$|^[\u0391-\uFFE5]+·?[\u0391-\uFFE5]+$/
 * 说明：
 * - [\u0391-\uFFE5] 中文字符范围
 * - 第一部分：^[\u0391-\uFFE5]+$ 纯中文字符
 * - 第二部分：^[\u0391-\uFFE5]+·?[\u0391-\uFFE5]+$ 中文字符+可选中文点+中文字符
 */
const CHINESE_FORMAT_REGEX = /^[\u0391-\uFFE5]+$|^[\u0391-\uFFE5]+·?[\u0391-\uFFE5]+$/;

/**
 * 第二步验证：禁用字符检测
 * 正则：/^[^【】、﹒▪•]+$/
 * 说明：不能包含 【】、﹒▪• 这些字符
 */
const FORBIDDEN_CHARS_REGEX = /^[^【】、﹒▪•]+$/;

/**
 * 完全按照您的规则进行中文姓名验证
 * @param value 要验证的值
 * @param allowEmpty 是否允许空值（对应原规则中的 this.optional(element)）
 * @returns 验证结果
 */
export const isChinese = (value: string, allowEmpty: boolean = false): boolean => {
  // 对应 this.optional(element) - 如果允许空值且为空，则返回 true
  if (allowEmpty && (!value || value.trim() === '')) {
    return true;
  }

  // 如果不允许空值但为空，则返回 false
  if (!value || value.trim() === '') {
    return false;
  }

  const trimmedValue = value.trim();

  // 第一步：检查中文字符格式
  // var flag = /^[\u0391-\uFFE5]+$|^[\u0391-\uFFE5]+·?[\u0391-\uFFE5]+$/.test(value);
  let flag = CHINESE_FORMAT_REGEX.test(trimmedValue);

  // 第二步：如果第一步通过，再检查禁用字符
  // if (flag){ flag = /^[^【】、﹒▪•]+$/.test(value); }
  if (flag) {
    flag = FORBIDDEN_CHARS_REGEX.test(trimmedValue);
  }

  // return this.optional(element) || flag;
  return flag;
};

/**
 * 带详细错误信息的中文姓名验证
 * @param value 要验证的值
 * @param allowEmpty 是否允许空值
 * @returns 验证结果对象
 */
export const validateChineseNameWithDetails = (
  value: string,
  allowEmpty: boolean = false
): {
  isValid: boolean;
  errorType?: 'empty' | 'format' | 'forbiddenChars';
  errorMessage?: string;
} => {
  // 空值检查
  if (!value || value.trim() === '') {
    if (allowEmpty) {
      return { isValid: true };
    }
    return {
      isValid: false,
      errorType: 'empty',
      errorMessage: '姓名不能为空',
    };
  }

  const trimmedValue = value.trim();

  // 第一步：检查中文字符格式
  if (!CHINESE_FORMAT_REGEX.test(trimmedValue)) {
    return {
      isValid: false,
      errorType: 'format',
      errorMessage: '只能输入中文字符',
    };
  }

  // 第二步：检查禁用字符
  if (!FORBIDDEN_CHARS_REGEX.test(trimmedValue)) {
    return {
      isValid: false,
      errorType: 'forbiddenChars',
      errorMessage: '姓名包含不允许的字符',
    };
  }

  return { isValid: true };
};

/**
 * 用于 Zod Schema 的验证函数
 * @param value 要验证的值
 * @returns 是否有效
 */
export const zodChineseNameValidator = (value: string): boolean => {
  return isChinese(value, false);
};

/**
 * 用于表单验证的验证函数
 * @param rule 验证规则
 * @param value 要验证的值
 * @param callback 回调函数
 */
export const formChineseNameValidator = (
  rule: any,
  value: string,
  callback: (error?: Error) => void
): void => {
  const result = validateChineseNameWithDetails(value, false);
  
  if (result.isValid) {
    callback();
  } else {
    callback(new Error(result.errorMessage || '只能输入中文字符'));
  }
};

/**
 * 正则表达式常量导出（供其他地方使用）
 */
export const CHINESE_NAME_REGEXES = {
  /** 中文字符格式正则 */
  FORMAT: CHINESE_FORMAT_REGEX,
  /** 禁用字符正则 */
  FORBIDDEN_CHARS: FORBIDDEN_CHARS_REGEX,
} as const;

/**
 * 错误信息常量
 */
export const CHINESE_NAME_ERROR_MESSAGES = {
  EMPTY: '姓名不能为空',
  FORMAT: '只能输入中文字符',
  FORBIDDEN_CHARS: '姓名包含不允许的字符',
} as const;
