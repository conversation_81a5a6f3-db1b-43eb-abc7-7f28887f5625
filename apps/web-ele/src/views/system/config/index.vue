<script lang="ts" setup>
import { Page } from '@vben/common-ui';

import { useConfigService } from './service';

defineOptions({
  name: 'ConfigIndex',
});

const { Form, BasicTable, FormDialog, useToolbarButtons, useRowButtons } =
  useConfigService();
</script>

<template>
  <Page auto-content-height>
    <FormDialog class="w-[565px]" title="参数配置管理">
      <Form />
    </FormDialog>

    <BasicTable>
      <template #toolbar-actions>
        <ActionButtonGroup :buttons="useToolbarButtons()" />
      </template>

      <template #action="{ row }">
        <ActionButtonGroup :buttons="useRowButtons()" :row="row" />
      </template>
    </BasicTable>
  </Page>
</template>
