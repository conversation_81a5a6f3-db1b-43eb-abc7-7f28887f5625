package com.ruoyi.mf.domain.bo.mfproduct;

import com.ruoyi.mf.domain.po.MfProduct;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import jakarta.validation.constraints.*;
import java.io.Serial;
import java.io.Serializable;

/**
 * 产品树业务对象 mf_product
 *
 * <AUTHOR>
 * @since 2025-03-17 17:51:24
 */
@Data
@AutoMapper(target = MfProduct.class, reverseConvertGenerate = false)
public class MfProductEditBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 产品id
     */
    @NotBlank(message = "产品id不能为空")
    private String id;
    /**
     * 父产品id
     */
    @NotBlank(message = "父产品id不能为空")
    private String parentId;
    /**
     * 产品名称
     */
    @NotBlank(message = "产品名称不能为空")
    private String productName;
    /**
     * 显示顺序
     */
    @NotNull(message = "显示顺序不能为空")
    private Integer orderNum;
    /**
     * 产品状态（0正常 1停用）
     */
    @NotBlank(message = "产品状态（0正常 1停用）不能为空")
    private String status;
    /**
     * 乐观锁
     */
    private Integer version;

}
