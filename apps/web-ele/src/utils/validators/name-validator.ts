/**
 * 姓名验证工具
 * 支持中文姓名验证，包括中文点（·）的处理
 */

/**
 * 中文字符范围正则表达式
 * 包括：中文汉字、中文标点符号等
 */
const CHINESE_CHAR_REGEX = /^[\u0391-\uFFE5]+$/;

/**
 * 中文姓名正则表达式
 * 支持纯中文或中文+中文点的组合
 */
const CHINESE_NAME_REGEX =
  /^[\u0391-\uFFE5]+$|^(?:[\u0391-\uFFE5]+·[\u0391-\uFFE5]+|[\u0391-\uFFE5]{2,})$/;

/**
 * 禁用字符正则表达式
 * 不允许的特殊符号
 */
const FORBIDDEN_CHARS_REGEX = /^[^【】、﹒▪•]+$/;

/**
 * 验证是否为有效的中文字符
 * @param name 姓名字符串
 * @returns 是否为有效的中文字符
 */
export const validateChineseChars = (name: string): boolean => {
  if (!name || name.trim() === '') return false;
  return CHINESE_CHAR_REGEX.test(name);
};

/**
 * 验证中文点的使用是否正确
 * 中文点（·）只能在姓名中间，不能在开头或结尾
 * @param name 姓名字符串
 * @returns 是否正确使用中文点
 */
export const validateChineseDot = (name: string): boolean => {
  if (!name || name.trim() === '') return false;

  // 如果不包含中文点，直接返回 true
  if (!name.includes('·')) return true;

  // 检查中文点的位置
  const dotIndex = name.indexOf('·');

  // 中文点不能在开头或结尾
  if (dotIndex === 0 || dotIndex === name.length - 1) return false;

  // 只能有一个中文点
  if (name.includes('·', dotIndex + 1)) return false;

  // 中文点前后都必须有中文字符
  const beforeDot = name.slice(0, Math.max(0, dotIndex));
  const afterDot = name.slice(Math.max(0, dotIndex + 1));

  return (
    beforeDot.length > 0 &&
    afterDot.length > 0 &&
    CHINESE_CHAR_REGEX.test(beforeDot) &&
    CHINESE_CHAR_REGEX.test(afterDot)
  );
};

/**
 * 验证是否包含禁用字符
 * @param name 姓名字符串
 * @returns 是否不包含禁用字符
 */
export const validateForbiddenChars = (name: string): boolean => {
  if (!name || name.trim() === '') return false;
  return FORBIDDEN_CHARS_REGEX.test(name);
};

/**
 * 验证姓名长度
 * @param name 姓名字符串
 * @param minLength 最小长度，默认为 2
 * @param maxLength 最大长度，默认为 10
 * @returns 是否符合长度要求
 */
export const validateNameLength = (
  name: string,
  minLength: number = 2,
  maxLength: number = 10,
): boolean => {
  if (!name || name.trim() === '') return false;
  const trimmedName = name.trim();
  return trimmedName.length >= minLength && trimmedName.length <= maxLength;
};

/**
 * 完整的中文姓名验证
 * 综合验证中文字符、中文点使用、禁用字符和长度
 * @param name 姓名字符串
 * @param options 验证选项
 * @returns 验证结果对象
 */
export const validateChineseName = (
  name: string,
  options: {
    allowEmpty?: boolean;
    maxLength?: number;
    minLength?: number;
  } = {},
): {
  errorMessage?: string;
  errorType?: 'empty' | 'forbiddenChars' | 'format' | 'invalidDot' | 'length';
  isValid: boolean;
} => {
  const { minLength = 2, maxLength = 10, allowEmpty = false } = options;

  // 空值检查
  if (!name || name.trim() === '') {
    if (allowEmpty) {
      return { isValid: true };
    }
    return {
      isValid: false,
      errorType: 'empty',
      errorMessage: '姓名不能为空',
    };
  }

  const trimmedName = name.trim();

  // 长度检查
  if (!validateNameLength(trimmedName, minLength, maxLength)) {
    return {
      isValid: false,
      errorType: 'length',
      errorMessage: `姓名长度应在${minLength}-${maxLength}个字符之间`,
    };
  }

  // 基础中文字符格式检查
  if (!CHINESE_NAME_REGEX.test(trimmedName)) {
    return {
      isValid: false,
      errorType: 'format',
      errorMessage: '只能输入中文字符',
    };
  }

  // 禁用字符检查
  if (!validateForbiddenChars(trimmedName)) {
    return {
      isValid: false,
      errorType: 'forbiddenChars',
      errorMessage: '姓名包含不允许的字符',
    };
  }

  // 中文点使用检查
  if (!validateChineseDot(trimmedName)) {
    return {
      isValid: false,
      errorType: 'invalidDot',
      errorMessage: '中文点（·）只能在姓名中间使用，且只能有一个',
    };
  }

  return { isValid: true };
};

/**
 * 获取姓名信息
 * @param name 姓名字符串
 * @returns 姓名信息对象
 */
export const parseNameInfo = (
  name: string,
): null | {
  firstName?: string;
  fullName: string;
  hasChineseDot: boolean;
  lastName?: string;
  length: number;
} => {
  if (!name || !validateChineseName(name).isValid) return null;

  const trimmedName = name.trim();
  const hasChineseDot = trimmedName.includes('·');

  if (hasChineseDot) {
    const parts = trimmedName.split('·');
    return {
      fullName: trimmedName,
      firstName: parts[0],
      lastName: parts[1],
      hasChineseDot: true,
      length: trimmedName.length,
    };
  }

  // 对于没有中文点的姓名，简单按长度分割
  if (trimmedName.length >= 2) {
    return {
      fullName: trimmedName,
      firstName: trimmedName.slice(0, 1),
      lastName: trimmedName.slice(1),
      hasChineseDot: false,
      length: trimmedName.length,
    };
  }

  return {
    fullName: trimmedName,
    hasChineseDot: false,
    length: trimmedName.length,
  };
};

/**
 * 格式化姓名
 * 去除多余空格，标准化格式
 * @param name 姓名字符串
 * @returns 格式化后的姓名
 */
export const formatName = (name: string): string => {
  if (!name) return '';

  // 去除首尾空格和中间多余空格
  return name.trim().replaceAll(/\s+/g, '');
};
