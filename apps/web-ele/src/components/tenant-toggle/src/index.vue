<script setup lang="ts">
import type { TenantOption } from '#/api';

import { computed, onMounted, ref, unref } from 'vue';
import { useRoute } from 'vue-router';

import { useAccess } from '@vben/access';
import { useTabs } from '@vben/hooks';
import { $t } from '@vben/locales';

import { storeToRefs } from 'pinia';

import { tenantDynamicClear, tenantDynamicToggle } from '#/api/system/tenant';
import { useDictStore } from '#/store/dict';
import { useTenantStore } from '#/store/tenant';

const { hasAccessByRoles } = useAccess();

// 上一次选择的租户
const lastSelected = ref<string>();
// 当前选择租户的id
const selected = ref<string>();

const tenantStore = useTenantStore();
const { initTenant, setChecked } = tenantStore;
const { tenantEnable, tenantList } = storeToRefs(tenantStore);

const showToggle = computed<boolean>(() => {
  // 超级管理员 && 启用租户
  return hasAccessByRoles(['superadmin']) && unref(tenantEnable);
});

onMounted(async () => {
  // 没有超级管理员权限 不会调用接口
  if (!hasAccessByRoles(['superadmin'])) {
    return;
  }
  await initTenant();
});

const route = useRoute();
const { closeOtherTabs, refreshTab, closeAllTabs } = useTabs();

async function close(checked: boolean) {
  // store设置状态
  await setChecked(checked);

  /**
   * 切换租户需要回到首页的页面 一般为带id的页面
   * 其他则直接刷新页面
   */
  if (route.meta.requireHomeRedirect) {
    await closeAllTabs();
  } else {
    // 先关闭再刷新 这里不用Promise.all()
    await closeOtherTabs();
    await refreshTab();
  }
}

const dictStore = useDictStore();
// loading加载中效果
const loading = ref(false);

/**
 * 选中租户的处理
 * @param tenantId tenantId
 */
const onSelected = async (tenantId: string) => {
  if (unref(lastSelected) === tenantId) {
    // createMessage.info('选择一致');
    return;
  }
  try {
    loading.value = true;

    await tenantDynamicToggle(tenantId);
    lastSelected.value = tenantId;

    ElMessage.success(
      `${$t('component.tenantToggle.switch')} ${tenantList.value.find((item) => item.id === tenantId)?.companyName}`,
    );

    await close(true);
    // 需要放在宏队列处理 直接清空页面由于没有字典会有样式问题(标签变成unknown)
    setTimeout(() => dictStore.resetDictCache());
  } catch (error) {
    console.error(error);
  } finally {
    loading.value = false;
  }
};

async function onDeselect() {
  try {
    loading.value = true;

    await tenantDynamicClear();
    ElMessage.success($t('component.tenantToggle.reset'));

    lastSelected.value = '';
    await close(false);
    // 需要放在宏队列处理 直接清空页面由于没有字典会有样式问题(标签变成unknown)
    setTimeout(() => dictStore.resetCache());
  } catch (error) {
    console.error(error);
  } finally {
    loading.value = false;
  }
}

/**
 * select搜索使用
 * @param input 输入内容
 * @param option 选项
 */
function filterOption(input: string, option: TenantOption) {
  return option.companyName.toLowerCase().includes(input.toLowerCase());
}
</script>

<template>
  <div v-if="showToggle" class="mr-[8px] hidden md:block">
    <ElSelect
      v-model:value="selected"
      :field-names="{ label: 'companyName', value: 'tenantId' }"
      :filter-option="filterOption"
      :options="tenantList"
      :placeholder="$t('component.tenantToggle.placeholder')"
      allow-clear
      class="w-60"
      show-search
      @deselect="onDeselect"
      @select="onSelected"
    />
  </div>
</template>

<style lang="scss" scoped>
// 当选中时 添加border样式
:deep(.ant-select-selector) {
  &:has(.ant-select-selection-item) {
    box-shadow: 0 0 10px hsl(var(--primary));
  }
}
</style>
