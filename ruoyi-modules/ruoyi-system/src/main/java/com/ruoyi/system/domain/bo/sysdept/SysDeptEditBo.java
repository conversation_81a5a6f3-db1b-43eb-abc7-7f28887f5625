package com.ruoyi.system.domain.bo.sysdept;

import com.ruoyi.system.domain.po.SysDept;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 机构管理业务对象 sys_dept
 *
 * <AUTHOR>
 * @since 2025-05-29 16:59:45
 */
@Data
@AutoMapper(target = SysDept.class, reverseConvertGenerate = false)
public class SysDeptEditBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 部门ID
     */
    @NotBlank(message = "部门ID不能为空")
    private String id;
    /**
     * 父部门ID
     */
    @NotBlank(message = "父部门ID不能为空")
    private String parentId;
    /**
     * 显示顺序
     */
    @NotNull(message = "显示顺序不能为空")
    private Integer orderNum;
    /**
     * 乐观锁
     */
    private Integer version;
    /**
     * 部门全称
     */
    @NotBlank(message = "部门全称不能为空")
    private String qc;
    /**
     * 部门简称
     */
    @NotBlank(message = "部门简称不能为空")
    private String jc;
    /**
     * 单位级别代码
     */
    @NotBlank(message = "单位级别代码不能为空")
    private String dwjbdm;
    /**
     * 单位联系电话
     */
    @NotBlank(message = "单位联系电话不能为空")
    private String dwlxdh;
    /**
     * 负责人姓名
     */
    private String fzrXm;
    /**
     * 负责人联系电话
     */
    private String fzrLxdh;
    /**
     * 警种分类代码
     */
    @NotBlank(message = "警种分类代码不能为空")
    private String jzfldm;
    /**
     * 行政区划ID
     */
    private String xzqhId;
    /**
     * 使用状态
     */
    @NotBlank(message = "使用状态不能为空")
    private String syztdm;
    /**
     * 公安机关机构代码
     */
    @NotBlank(message = "公安机关机构代码不能为空")
    private String gajgjgdm;
    /**
     * 签发机关部门ID
     */
    private String qfjgDeptId;

}
