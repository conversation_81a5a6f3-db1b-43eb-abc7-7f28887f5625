package com.ruoyi.system.domain.bo.sysmenu;

import com.ruoyi.system.domain.po.SysMenu;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import jakarta.validation.constraints.*;
import java.io.Serial;
import java.io.Serializable;

/**
 * 菜单权限业务对象 sys_menu
 *
 * <AUTHOR>
 * @since 2025-04-14 13:57:20
 */
@Data
@AutoMapper(target = SysMenu.class, reverseConvertGenerate = false)
public class SysMenuListBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 父菜单ID
     */
    @NotBlank(message = "父菜单ID不能为空")
    private String parentId;

    /**
     * 菜单名称
     */
    @NotBlank(message = "菜单名称不能为空")
    private String menuName;

    /**
     * 菜单类型（M目录 C菜单 F按钮）
     */
    @NotBlank(message = "菜单类型（M目录 C菜单 F按钮）不能为空")
    private String menuType;

    /**
     * 显示状态（0显示 1隐藏）
     */
    @NotBlank(message = "显示状态（0显示 1隐藏）不能为空")
    private String visible;

    /**
     * 菜单图标
     */
    @NotBlank(message = "菜单图标不能为空")
    private String icon;


}
