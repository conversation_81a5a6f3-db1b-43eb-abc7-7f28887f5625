import type { ColumnProps, SkTableInstance } from '@/components/SkUi/SkTable/interface';
import { h, reactive, ref } from 'vue';
import { addDialog } from '@/components/SkUi/SkReDialog';
import { cleanOperlog, delOperlog } from '@/api/monitor/operlog';
import type { OperLogVO } from '@/api/monitor/operlog/types';
import OperlogDialog from '@/views/monitor/operlog/components/OperlogDialog.vue';

export function operlogOptions() {
  const skTable = ref<SkTableInstance>();
  // 表格配置项
  const columns = reactive<ColumnProps[]>([
    { prop: 'id', label: '日志编号', width: 120 },
    { prop: 'title', label: '系统模块', align: 'center', search: { el: 'input' } },
    {
      prop: 'businessType',
      label: '操作类型',
      width: 100,
      align: 'center',
      search: { el: 'sk-dict', props: { data: 'czlx' } } //search为Form表单
    },
    { prop: 'operName', label: '操作人员', align: 'center', search: { el: 'input' } },
    { prop: 'deptName', label: '部门', align: 'center', search: { el: 'input' } },
    { prop: 'operIp', label: '操作地址', align: 'center', search: { el: 'input' } },
    {
      prop: 'status',
      label: '操作状态',
      width: 100,
      align: 'center',
      search: { el: 'sk-dict', props: { data: 'xtzt' } }
    },
    {
      prop: 'operTime',
      label: '操作日期',
      width: 180,
      search: {
        el: 'date-picker',
        span: 2,
        props: {
          type: 'daterange',
          valueFormat: 'YYYY-MM-DD',
          rangeSeparator: '-',
          startPlaceholder: '开始日期',
          endPlaceholder: '结束日期'
        }
      }
    },
    {
      prop: 'costTime',
      label: '消耗时间',
      width: 180
    },
    { prop: 'operation', label: '操作', width: 160, fixed: 'right' }
  ]);
  /**
   * 编辑，新增,数据交换
   */
  const openDialog = async (row: Partial<OperLogVO> = {}) => {
    addDialog({
      title: '操作日志详细',
      width: '70%',
      props: {
        model: row,
        labelWidth: '100px'
      },
      closeOnClickModal: false,
      hideFooter: true,
      contentRenderer: () => h(OperlogDialog)
    });
  };
  /** 导出按钮操作 */
  const handleExport = () => {
    window.skynet?.download(
      'monitor/operlog/export',
      {
        ...skTable.value.searchParam
      },
      `dict_${new Date().getTime()}.xlsx`
    );
  };
  /** 删除按钮操作 */
  const handleDelete = async (row?: OperLogVO) => {
    const ids = row?.id;
    await window.skynet.confirm('是否确认删除日志编号为"' + ids + '"的数据项?');
    await delOperlog(ids);
    await skTable.value?.getTableList();
    window.skynet.alert('删除成功');
  };
  /** 清空按钮操作 */
  const handleClean = async () => {
    await window.skynet.confirm('是否确认清空所有操作日志数据项?');
    await cleanOperlog();
    await skTable.value?.getTableList();
    window.skynet.alert('清空成功');
  };
  return {
    skTable,
    columns,
    openDialog,
    handleDelete,
    handleClean,
    handleExport
  };
}
