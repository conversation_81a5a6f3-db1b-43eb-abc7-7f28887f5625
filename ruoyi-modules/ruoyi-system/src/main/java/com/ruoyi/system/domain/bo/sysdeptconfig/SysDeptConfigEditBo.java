package com.ruoyi.system.domain.bo.sysdeptconfig;

import com.ruoyi.system.domain.po.SysDeptConfig;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 部门配置业务对象 sys_dept_config
 *
 * <AUTHOR>
 * @since 2025-05-29 16:59:45
 */
@Data
@AutoMapper(target = SysDeptConfig.class, reverseConvertGenerate = false)
public class SysDeptConfigEditBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 配置ID
     */
    @NotBlank(message = "配置ID不能为空")
    private String id;
    /**
     * 关联部门ID
     */
    @NotBlank(message = "关联部门ID不能为空")
    private String deptId;
    /**
     * 配置名称
     */
    @NotBlank(message = "配置名称不能为空")
    private String name;
    /**
     * 配置键
     */
    @NotBlank(message = "配置键不能为空")
    private String key;
    /**
     * 配置值
     */
    @NotBlank(message = "配置值不能为空")
    private String value;
    /**
     * 乐观锁版本号
     */
    private Integer version;

}
