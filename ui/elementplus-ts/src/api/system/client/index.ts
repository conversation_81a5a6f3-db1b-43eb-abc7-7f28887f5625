import type { ClientForm, ClientQuery, ClientVO } from '@/api/system/client/types';
import http from '@/utils/http';
import type { PageData } from '@/utils/http/interface';

/**
 * 查询客户端管理列表
 * @param query
 * @returns {*}
 */

export const listClient = (query?: ClientQuery) => {
  return http.get<PageData<ClientVO>>('/system/client/list', query);
};

/**
 * 查询客户端管理详细
 * @param id
 */
export const getClient = (id: string) => {
  return http.get<ClientVO>('/system/client/' + id);
};

/**
 * 新增客户端管理
 * @param data
 */
export const addClient = (data: ClientForm) => {
  return http.post('/system/client', data);
};

/**
 * 修改客户端管理
 * @param data
 */
export const updateClient = (data: ClientForm) => {
  return http.put('/system/client', data);
};

/**
 * 删除客户端管理
 * @param id
 */
export const delClient = (id: string | Array<string>) => {
  return http.delete('/system/client/' + id);
};

/**
 * 状态修改
 * @param id 客户端id
 * @param version 版本号
 * @param status 状态
 */
export const changeStatus = (id: string, version: number, status: string) => {
  const data = {
    id,
    version,
    status
  };
  return http.put('/system/client/changeStatus', data);
};
