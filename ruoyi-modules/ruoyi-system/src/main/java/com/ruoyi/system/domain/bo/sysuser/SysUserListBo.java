package com.ruoyi.system.domain.bo.sysuser;

import com.ruoyi.common.core.core.domain.bo.BaseListBo;
import com.ruoyi.system.domain.po.SysUser;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户信息业务对象 sys_user
 *
 * <AUTHOR>
 * @since 2025-04-29 14:00:02
 */
@Data
@AutoMapper(target = SysUser.class, reverseConvertGenerate = false)
public class SysUserListBo extends BaseListBo {

    /**
     * 用户ID
     */
    private String id;

    /**
     * 部门ID
     */
    private String deptId;

    /**
     * 用户账号
     */
    private String userName;
    /**
     * 用户昵称
     */
    private String nickName;

    /**
     * 手机号码
     */
    private String phonenumber;

    /**
     * 用户性别（0男 1女 2未知）
     */
    private String gender;

    /**
     * 帐号状态（0正常 1停用）
     */
    private String status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
