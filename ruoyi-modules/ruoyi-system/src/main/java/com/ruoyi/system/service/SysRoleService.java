package com.ruoyi.system.service;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.query.QueryMethods;
import com.mybatisflex.core.query.QueryWrapper;
import com.ruoyi.common.core.constant.TenantConstants;
import com.ruoyi.common.core.constant.UserConstants;
import com.ruoyi.common.core.core.domain.PageData;
import com.ruoyi.common.core.core.domain.Result;
import com.ruoyi.common.core.core.domain.dto.RoleDTO;
import com.ruoyi.common.core.core.domain.model.LoginUser;
import com.ruoyi.common.core.enums.CommonResponseEnum;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.MapstructUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.orm.core.page.PageQuery;
import com.ruoyi.common.orm.core.page.PageResult;
import com.ruoyi.common.orm.core.service.impl.BaseServiceImpl;
import com.ruoyi.common.satoken.utlis.LoginHelper;
import com.ruoyi.system.domain.bo.sysdept.SysDeptOptionBo;
import com.ruoyi.system.domain.bo.sysrole.*;
import com.ruoyi.system.domain.po.SysRole;
import com.ruoyi.system.domain.vo.sysdept.DeptTreeSelectVo;
import com.ruoyi.system.domain.vo.sysrole.SysRoleDetailVo;
import com.ruoyi.system.domain.vo.sysrole.SysRoleListVo;
import com.ruoyi.system.mapper.SysRoleMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static com.ruoyi.system.domain.po.table.SysRoleTableDef.SYS_ROLE;
import static com.ruoyi.system.domain.po.table.SysUserTableDef.SYS_USER;

/**
 * 角色信息Service业务层处理
 * <p>
 * 本服务类提供角色信息地增删改查等基础操作，包括：
 * <ul>
 *     <li>分页查询角色信息列表</li>
 *     <li>查询角色信息详情</li>
 *     <li>新增角色信息</li>
 *     <li>修改角色信息</li>
 *     <li>删除角色信息</li>
 * </ul>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-28 12:56:34
 */
@Service
public class SysRoleService extends BaseServiceImpl<SysRoleMapper, SysRole> {
    @Resource
    private SysRoleMapper sysRoleMapper;
    @Resource
    private SysRoleMenuService sysRoleMenuService;
    @Resource
    private SysDeptService sysDeptService;
    @Resource
    private SysRoleDeptService sysRoleDeptService;
    @Resource
    private SysUserRoleService sysUserRoleService;


    /**
     * 获取基础查询对象
     *
     * @return 预设了表信息的查询包装器
     */
    public QueryWrapper query() {
        return super.query().from(SYS_ROLE);
    }

    /**
     * 构建查询条件
     * <p>
     * 根据传入的查询参数构建查询条件，MyBatis Flex会自动处理空值
     *
     * @param bo 查询参数对象
     * @return 构建好地查询条件包装器
     */
    private QueryWrapper buildQueryWrapper(SysRoleListBo bo) {
        return super.buildBaseQueryWrapper()
            // 角色名称
            .and(SYS_ROLE.ROLE_NAME.like(bo.getRoleName()))
            // 角色权限字符串
            .and(SYS_ROLE.ROLE_KEY.eq(bo.getRoleKey()))
            // 角色状态（0正常 1停用）
            .and(SYS_ROLE.STATUS.eq(bo.getStatus()))
            // 创建时间
            .and(SYS_ROLE.CREATE_TIME.eq(bo.getCreateTime()))
            .and(SYS_ROLE.ID.eq(bo.getId()))
            .orderBy(SYS_ROLE.ROLE_SORT.asc());
    }

    /**
     * 分页查询角色信息列表
     * <p>
     * 根据查询条件进行分页查询，并将结果转换为前端所需的VO对象
     *
     * @param bo 角色信息查询参数
     * @return 包含分页角色信息集合的结果对象
     */
    public Result<PageData<SysRoleListVo>> page(SysRoleListBo bo) {
        return PageResult.success(this.pageAs(
                PageQuery.build(),
                buildQueryWrapper(bo),
                SysRoleListVo.class
        ));
    }

    /**
     * 查询角色信息列表（不分页）
     * <p>
     * 根据查询条件获取所有符合条件的角色信息
     *
     * @param bo 角色信息查询参数
     * @return 角色信息集合
     */
    public List<SysRoleListVo> list(SysRoleListBo bo) {
        QueryWrapper queryWrapper = buildQueryWrapper(bo);
        return this.listAs(queryWrapper, SysRoleListVo.class);
    }

    /**
     * 校验属性值是否唯一
     *
     * @param column 列引用
     * @param value  属性值
     * @param id     记录ID（修改时使用，新增时为null）
     * @param message 错误消息
     */
    private void checkUnique(QueryColumn column, Object value, String id, String message) {
        QueryWrapper queryWrapper = QueryWrapper.create()
            .where(column.eq(value));
        if (id != null) {
            queryWrapper.and(SYS_ROLE.ID.ne(id));
        }
        CommonResponseEnum.EXISTS
            .msg(message)
            .assertTrue(count(queryWrapper) > 0);
    }

    /**
     * 校验角色名称和标识的唯一性
     *
     * @param roleName 角色名称
     * @param roleKey  角色标识
     * @param id       角色ID（修改时使用，新增时为null）
     */
    private void checkRoleUnique(String roleName, String roleKey, String id) {
        // 校验角色名称是否唯一
        checkUnique(SYS_ROLE.ROLE_NAME, roleName, id, "角色名称已存在");
        // 校验角色标识是否唯一
        checkUnique(SYS_ROLE.ROLE_KEY, roleKey, id, "角色标识已存在");
    }

    /**
     * 新增角色信息
     * <p>
     * 将前端传入的数据转换为实体并保存到数据库
     *
     * @param bo 角色信息新增参数
     * @return 操作结果，成功返回{@code Result.success()}，失败返回包含错误信息的{@code Result.fail()}
     */
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> add(SysRoleAddBo bo) {
        checkRoleAllowed(SysRoleCheckBo.builder().roleKey(bo.getRoleKey()).build());
        // 校验角色名称和标识的唯一性
        checkRoleUnique(bo.getRoleName(), bo.getRoleKey(), null);
        SysRole entity = MapstructUtils.convert(bo, SysRole.class);
        if(entity != null){
            //默认1：全部数据权限
            entity.setDataScope("1");
            boolean inserted = this.save(entity);
            if (inserted) {
                // 新增角色与菜单管理
                if (bo.getMenuIds() != null && bo.getMenuIds().length > 0) {
                    SysRoleMenuBo roleMenuBo = new SysRoleMenuBo();
                    roleMenuBo.setRoleId(entity.getId());
                    roleMenuBo.setMenuIds(Arrays.asList(bo.getMenuIds()));
                    sysRoleMenuService.change(roleMenuBo);
                }
                return Result.success();
            }
        }
        return Result.fail("新增角色信息失败！");
    }

    /**
     * 修改角色信息
     * <p>
     * 更新数据库中已存在的角色信息
     *
     * @param bo 角色信息编辑参数
     * @return 操作结果，成功返回{@code Result.success()}，失败返回包含错误信息的{@code Result.fail()}
     */
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> edit(SysRoleEditBo bo) {
        checkRoleAllowed(SysRoleCheckBo.builder().roleKey(bo.getRoleKey()).id(bo.getId()).build());
        checkRoleDataScope(bo.getId());
        // 校验角色名称和标识的唯一性
        checkRoleUnique(bo.getRoleName(), bo.getRoleKey(), bo.getId());

        SysRole entity = MapstructUtils.convert(bo, SysRole.class);
        if (ObjectUtil.isNull(entity) || ObjectUtil.isNull(entity.getId())) {
            return Result.fail("角色信息ID不能为空");
        }
        if (UserConstants.ROLE_DISABLE.equals(entity.getStatus()) && sysUserRoleService.countUserRoleByRoleId(entity.getId()) > 0) {
            throw new ServiceException("角色已分配，不能禁用!" );
        }
        boolean updated = this.updateById(entity);
        if(updated){
            // 新增角色与菜单管理
            if (bo.getMenuIds() != null && bo.getMenuIds().length > 0) {
                sysRoleMenuService.deleteRoleMenuByRoleId(entity.getId());

                SysRoleMenuBo roleMenuBo = new SysRoleMenuBo();
                roleMenuBo.setRoleId(entity.getId());
                roleMenuBo.setMenuIds(Arrays.asList(bo.getMenuIds()));
                sysRoleMenuService.change(roleMenuBo);

                cleanOnlineUserByRole(bo.getId());
            }
        }
        return updated
                ? Result.success()
                : Result.fail("修改角色信息记录失败!");
    }


    /**
     * 批量删除角色信息
     * <p>
     * 根据ID数组删除多条角色信息记录
     *
     * @param ids 需要删除的角色信息主键数组
     * @return 操作结果，成功返回{@code Result.success()}，失败返回包含错误信息的{@code Result.fail()}
     * @throws IllegalArgumentException 当ids为null或空数组时抛出
     */
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> removeByIds(String[] ids) {
        // 参数校验
        if (ids == null || ids.length == 0) {
            return Result.fail("删除ID不能为空");
        }

        for (String roleId : ids) {
            checkRoleAllowed(SysRoleCheckBo.builder().id(roleId).build());
            checkRoleDataScope(roleId);
            if (sysUserRoleService.countUserRoleByRoleId(roleId) > 0) {
                throw new ServiceException(String.format("%1$s已分配,不能删除!" , getById(roleId).getRoleName()));
            }
        }
        // 删除角色与菜单关联
        sysRoleMenuService.deleteRoleMenu(ids);
        // 删除角色与部门关联
        sysRoleDeptService.deleteRoleDept(ids);

        // 执行批量删除
        return this.removeByIds(Arrays.asList(ids))
                ? Result.success()
                : Result.fail("删除角色信息记录失败!");
    }

    /**
     * 修改角色状态
     *
     * @param roleBo 角色信息
     * @return boolean
     */
    public Result<Void> updateRoleStatus(SysRoleStatusBo roleBo) {
        checkRoleAllowed(SysRoleCheckBo.builder().id(roleBo.getId()).roleKey(roleBo.getRoleKey()).build());
        checkRoleDataScope(roleBo.getId());
        SysRole role = MapstructUtils.convert(roleBo, SysRole.class);
        String roleId;
        if (role != null) {
            roleId = role.getId();
            String status = role.getStatus();
            if (UserConstants.ROLE_DISABLE.equals(status) && sysUserRoleService.countUserRoleByRoleId(roleId) > 0) {
                throw new ServiceException("角色已分配，不能禁用!" );
            }
            // 修改角色信息
            boolean updated = this.updateById(role);
            if (!updated) {
                return Result.fail("修改角色'" + roleBo.getRoleName() + "'的状态失败，请联系管理员" );
            }
        }
        return Result.success();
    }

    /**
     * 查询角色信息详情
     * <p>
     * 根据ID获取单个角色信息的详细信息
     *
     * @param id 角色信息主键
     * @return 角色信息详细信息，如果id为空则返回null
     */
    public SysRoleDetailVo detail(String id) {
        if (StringUtils.isEmpty(id)) {
            return null;
        }

        return this.getOneAs(
                query().where(SYS_ROLE.ID.eq(id)),
                SysRoleDetailVo.class
        );
    }

    /**
     * 新增角色信息（带主键）
     * <p>
     * 使用前端提供的主键值新增记录，通常用于数据导入场景
     *
     * @param bo 角色信息新增参数（含主键）
     * @return 操作结果，成功返回{@code Result.success()}，失败返回包含错误信息的{@code Result.fail()}
     */
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> insertWithPk(SysRoleAddBo bo) {
        SysRole entity = MapstructUtils.convert(bo, SysRole.class);
        // 使用特定的mapper方法执行带主键的插入
        boolean inserted = sysRoleMapper.insertWithPk(entity) > 0;
        return inserted
                ? Result.success()
                : Result.fail("新增角色信息记录失败！");
    }

    /**
     * 修改数据权限信息
     *
     * @param roleBo 角色信息
     * @return 结果:true 成功，false 失败
     */
    @Transactional
    public Result<Void> authDataScope(SysRoleDataScopeBo roleBo) {
        checkRoleAllowed(SysRoleCheckBo.builder().roleKey(roleBo.getRoleKey()).id(roleBo.getId()).build());
        checkRoleDataScope(roleBo.getId());
        // 修改角色信息
        SysRole role = MapstructUtils.convert(roleBo, SysRole.class);
        // 修改角色信息
        boolean updated = this.updateById(role);
        if (updated && role != null) {
            // 删除角色与部门关联
            sysRoleDeptService.deleteRoleDeptByRoleId(role.getId());

            // 新增角色和部门 信息（数据权限）
            SysRoleDeptBo roleDeptBo=new SysRoleDeptBo();
            roleDeptBo.setDeptIds(roleBo.getDeptIds());
            roleDeptBo.setRoleId(role.getId());
            sysRoleDeptService.insertRoleDept(roleDeptBo);
        }
        if (!updated) {
            return Result.fail("修改角色'" + roleBo.getRoleName() + "'数据权限失败，请联系管理员" );
        }
        return Result.success();
    }

    /**
     * 校验角色是否允许操作
     *
     * @param roleBo 角色信息
     */
    public void checkRoleAllowed(SysRoleCheckBo roleBo) {
        if (ObjectUtil.isNotNull(roleBo.getId()) && LoginHelper.isSuperAdminRole(roleBo.getId())) {
            throw new ServiceException("不允许操作超级管理员角色" );
        }
        String[] keys = new String[]{TenantConstants.SUPER_ADMIN_ROLE_KEY, TenantConstants.TENANT_ADMIN_ROLE_KEY};
        // 新增:不允许使用 管理员标识符
        if (ObjectUtil.isNull(roleBo.getId())
            && StringUtils.equalsAny(roleBo.getRoleKey(), keys)) {
            throw new ServiceException("不允许使用系统内置管理员角色标识符!" );
        }
        // 修改:不允许修改 管理员标识符
        if (ObjectUtil.isNotNull(roleBo.getId())) {
            SysRole sysRole = this.getById(roleBo.getId());
            // 如果标识符不相等 判断为修改了管理员标识符
            if (!StringUtils.equals(sysRole.getRoleKey(), roleBo.getRoleKey())) {
                if (StringUtils.equalsAny(sysRole.getRoleKey(), keys)) {
                    throw new ServiceException("不允许修改系统内置管理员角色标识符!" );
                } else if (StringUtils.equalsAny(roleBo.getRoleKey(), keys)) {
                    throw new ServiceException("不允许使用系统内置管理员角色标识符!" );
                }
            }
        }
    }

    /**
     * 注销该角色的在线用户
     *
     * @param roleId 主键
     */
    public void cleanOnlineUserByRole(String roleId) {
        // 如果角色未绑定用户 直接返回
        int num = sysUserRoleService.countUserRoleByRoleId(roleId);
        if (num == 0) {
            return;
        }
        List<String> keys = StpUtil.searchTokenValue("" , 0, -1, false);
        if (CollUtil.isEmpty(keys)) {
            return;
        }
        // 角色关联的在线用户量过大会导致redis阻塞卡顿 谨慎操作
        keys.parallelStream().forEach(key -> {
            String token = StringUtils.substringAfterLast(key, ":" );
            // 如果已经过期则跳过
            if (StpUtil.stpLogic.getTokenActiveTimeoutByToken(token) < -1) {
                return;
            }
            LoginUser loginUser = LoginHelper.getLoginUser(token);
            if (loginUser != null) {
                for (RoleDTO roleDTO : loginUser.getRoles()) {
                    ObjectUtil.isNotNull(roleDTO.getRoleId());
                }
            }
        });
    }

    /**
     * 根据用户ID查询权限
     *
     * @param userId 用户ID
     * @return 权限列表
     */
    public Set<String> selectRolePermissionByUserId(String userId) {
        List<SysRoleListVo> roles = selectRoleList(SysRoleListBo.builder().userId(userId).build());
        Set<String> permsSet = new HashSet<>();
        for (SysRoleListVo role : roles) {
            if (StringUtils.isNotNull(role) && StringUtils.isNotEmpty(role.getRoleKey())) {
                permsSet.addAll(Arrays.asList(role.getRoleKey().trim().split(",")));
            }
        }
        return permsSet;
    }

    /**
     * 获取对应角色部门树列表
     */
    public Result<DeptTreeSelectVo> deptTree(String roleId) {
        DeptTreeSelectVo selectVo = new DeptTreeSelectVo();
        selectVo.setCheckedKeys(sysDeptService.selectDeptListByRoleId(roleId,getById(roleId).getMenuCheckStrictly()));
        selectVo.setDepts(sysDeptService.optionTree(new SysDeptOptionBo()).getData());
        return Result.success(selectVo);
    }

    /**
     * 选择角色列表
     * 根据传入的查询条件对象获取角色列表信息
     *
     * @param bo 角色列表查询条件对象，包含角色名称、角色权限字符串、角色状态、创建时间等查询条件
     * @return 返回一个角色列表，每个角色信息被封装在SysRoleListVo对象中
     */
    public List<SysRoleListVo> selectRoleList(SysRoleListBo bo) {
        // 创建查询包装器，用于构建复杂的查询条件
        QueryWrapper queryWrapper = sysRoleMapper.selectRoleListQueryWrapper()
            .select(QueryMethods.distinct(SYS_ROLE.ALL_COLUMNS))
            // 角色名称
            .and(SYS_ROLE.ROLE_NAME.like(bo.getRoleName()))
            // 角色权限字符串
            .and(SYS_ROLE.ROLE_KEY.eq(bo.getRoleKey()))
            // 角色状态（0正常 1停用）
            .and(SYS_ROLE.STATUS.eq(bo.getStatus()))
            // 创建时间
            .and(SYS_ROLE.CREATE_TIME.eq(bo.getCreateTime()))
            // 角色ID
            .and(SYS_ROLE.ID.eq(bo.getId()))
            // 用户ID，用于关联查询用户角色信息
            .and(SYS_USER.ID.eq(bo.getUserId()))
            // 用户名，用于进一步过滤用户角色信息
            .and(SYS_USER.USER_NAME.eq(bo.getUserName()))
            .orderBy(SYS_ROLE.ROLE_SORT.asc());
        // 执行查询并转换结果为SysRoleListVo列表返回
        return this.listAs(queryWrapper, SysRoleListVo.class);
    }


    /**
     * 根据用户ID查询角色
     *
     * @param userId 用户ID
     * @return 角色列表
     */
    public List<SysRoleListVo> selectRolesByUserId(String userId) {
        List<SysRoleListVo> userRoles = selectRoleList(SysRoleListBo.builder().userId(userId).build());
        List<SysRoleListVo> roles = selectRoleList(SysRoleListBo.builder().build());
        for (SysRoleListVo role : roles) {
            for (SysRoleListVo userRole : userRoles) {
                if (StringUtils.equals(role.getId(), userRole.getId())) {
                    role.setFlag(true);
                    break;
                }
            }
        }
        return roles;
    }
    /**
     * 校验角色是否有数据权限
     *
     * @param roleId 角色id
     */
    public void checkRoleDataScope(String roleId) {
        if (ObjectUtil.isNull(roleId)) {
            return;
        }
        if (LoginHelper.isSuperAdmin()) {
            return;
        }
        List<SysRoleListVo> roles = selectRoleList(SysRoleListBo.builder().id(roleId).build());
        if (CollUtil.isEmpty(roles)) {
            throw new ServiceException("没有权限访问角色数据！" );
        }
    }
}
