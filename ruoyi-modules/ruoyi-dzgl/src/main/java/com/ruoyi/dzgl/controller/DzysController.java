package com.ruoyi.dzgl.controller;

import java.util.List;
import lombok.RequiredArgsConstructor;
import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.ruoyi.common.core.core.domain.Result;
import cn.hutool.core.lang.tree.Tree;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.web.annotation.RepeatSubmit;
import com.ruoyi.common.web.core.BaseController;
import jakarta.annotation.Resource;
import com.ruoyi.dzgl.domain.vo.dzys.DzysDetailVo;
import com.ruoyi.dzgl.domain.vo.dzys.DzysListVo;
import com.ruoyi.dzgl.domain.vo.dzys.DzysOptionVo;
import com.ruoyi.dzgl.domain.bo.dzys.DzysAddBo;
import com.ruoyi.dzgl.domain.bo.dzys.DzysEditBo;
import com.ruoyi.dzgl.domain.bo.dzys.DzysListBo;
import com.ruoyi.dzgl.domain.bo.dzys.DzysOptionBo;
import com.ruoyi.dzgl.service.DzysService;

/**
 * 地址元素管理控制器
 * <p>
 * 提供地址元素管理地增删改查、导入导出等功能，主要包含：
 * <ul>
 *   <li>地址元素管理地增删改查</li>
 *   <li>地址元素管理的分页查询</li>
 *   <li>地址元素管理的下拉选择</li>
 *   <li>地址元素管理的树形结构</li>
 * </ul>
 *
 * <AUTHOR>
 * @since 2025-06-04 16:50:12
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/dzgl/dzys")
public class DzysController extends BaseController {
    @Resource
    private DzysService dzysService;

    /**
     * 分页查询地址元素管理列表
     * <p>
     * 支持多条件组合查询，返回分页数据
     *
     * @param dzysListBo 查询条件对象，包含分页参数和查询条件
     * @return 包含分页信息和地址元素管理列表的结果对象
     */
    @SaCheckPermission("dzgl:dzys:list")
    @GetMapping("/list")
    public Result<List<DzysListVo>> list(DzysListBo dzysListBo) {
        List<DzysListVo> list = dzysService.list(dzysListBo);
        return Result.success(list);
    }

    /**
     * 获取地址元素管理下拉选择列表
     * <p>
     * 用于前端下拉框等场景，支持模糊搜索
     *
     * @param optionBo 查询条件对象，包含搜索关键字等参数
     * @return 包含地址元素管理ID和显示名称的选项列表
     */
    @SaCheckLogin
    @GetMapping("/listOption")
    public Result<List<DzysOptionVo>> listOption(DzysOptionBo optionBo) {
        return dzysService.optionSelect(optionBo);
    }

    /**
     * 获取地址元素管理树形选择列表
     * <p>
     * 用于前端树形选择等场景，支持按层级展示
     *
     * @param optionBo 查询条件对象，包含搜索关键字等参数
     * @return 树形结构的地址元素管理列表
     */
    @SaCheckLogin
    @GetMapping("/listOptionTree")
    public Result<List<Tree<String>>> listOptionTree(DzysOptionBo optionBo) {
        return dzysService.optionTree(optionBo);
    }

    /**
     * 新增地址元素管理
     * <p>
     * 包含数据校验和重复提交控制，确保数据的完整性和一致性
     *
     * @param dzysAddBo 地址元素管理对象，包含必填字段验证
     * @return 操作结果，成功或失败信息
     */
    @SaCheckPermission("dzgl:dzys:add")
    @Log(title = "地址元素管理", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping
    public Result<Void> add(@Validated @RequestBody DzysAddBo dzysAddBo) {
        return dzysService.add(dzysAddBo);
    }

    /**
     * 修改地址元素管理
     * <p>
     * 包含数据校验和重复提交控制，确保数据的完整性和一致性
     *
     * @param dzysEditBo 地址元素管理对象，包含必填字段验证
     * @return 操作结果，成功或失败信息
     */
    @SaCheckPermission("dzgl:dzys:edit")
    @Log(title = "地址元素管理", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping
    public Result<Void> edit(@Validated @RequestBody DzysEditBo dzysEditBo) {
        return dzysService.edit(dzysEditBo);
    }

    /**
     * 删除地址元素管理
     * <p>
     * 支持批量删除操作，可同时删除多个地址元素管理
     *
     * @param ids 地址元素管理ID数组，包含要删除的ID列表
     * @return 操作结果，成功或失败信息
     */
    @SaCheckPermission("dzgl:dzys:remove")
    @Log(title = "地址元素管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result<Void> remove(@PathVariable String[] ids) {
        return dzysService.removeByIds(ids);
    }

    /**
     * 获取地址元素管理详细信息
     * <p>
     * 根据ID查询单个地址元素管理的完整信息，包含所有字段
     *
     * @param id 地址元素管理ID
     * @return 包含地址元素管理所有字段的详细信息对象
     */
    @SaCheckPermission("dzgl:dzys:query")
    @GetMapping(value = "/{id}")
    public Result<DzysDetailVo> detail(@PathVariable String id) {
        return Result.success(dzysService.detail(id));
    }


}
