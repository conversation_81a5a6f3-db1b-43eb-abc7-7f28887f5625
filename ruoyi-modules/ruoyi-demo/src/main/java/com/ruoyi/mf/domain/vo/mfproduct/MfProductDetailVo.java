package com.ruoyi.mf.domain.vo.mfproduct;

import java.util.Date;
import lombok.Data;
import java.io.Serial;
import java.io.Serializable;

/**
 * 产品树视图对象 mf_product
 *
 * <AUTHOR>
 * @since 2025-03-17 17:51:24
 */
@Data
public class MfProductDetailVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 产品id */
    private String id;
    /** 父产品id */
    private String parentId;
    /** 产品名称 */
    private String productName;
    /** 显示顺序 */
    private Integer orderNum;
    /** 产品状态（0正常 1停用） */
    private String status;
    /** 乐观锁 */
    private Integer version;


}
