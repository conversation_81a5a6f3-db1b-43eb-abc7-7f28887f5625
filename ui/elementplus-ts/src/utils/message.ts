import type { MessageBoxData } from 'element-plus';

export const alert = (content: any) => {
  return ElMessageBox.alert(content, '系统提示', { type: 'success', buttonSize: 'default' });
};
// 错误提示

export const alertError = (content: any) => {
  return ElMessageBox.alert(content, '系统提示', { type: 'error', buttonSize: 'default' });
};
// 警告提示
export const alertWarning = (content: any) => {
  return ElMessageBox.alert(content, '系统提示', { type: 'warning', buttonSize: 'default' });
};

/**
 * 成功确认窗体
 * @param content --内容
 * @param type -- 消息框类型
 * @param title -- 提示标题
 * @param confirText -- 确认键名称
 * @param canceText -- 取消键名称
 */
export function skConfirm(
  content: any,
  type?: any,
  title?: string,
  confirText?: string,
  canceText?: string
): Promise<MessageBoxData> {
  return ElMessageBox.confirm(content, title ? title : '系统提示', {
    confirmButtonText: confirText ? confirText : '确定',
    cancelButtonText: canceText ? canceText : '取消',
    buttonSize: 'default',
    roundButton: true,
    type: type ? type : 'success'
  });
}
