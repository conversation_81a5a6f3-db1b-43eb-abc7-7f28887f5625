package ${packageName}.domain.vo.${lowerClassName};

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.ruoyi.common.excel.annotation.ExcelDictFormat;
import com.ruoyi.common.excel.convert.ExcelDictConvert;
import lombok.Data;
import java.io.Serial;
import java.io.Serializable;
#if($table.sub)
import java.util.List;
import com.mybatisflex.annotation.RelationOneToMany;
import ${packageName}.domain.po.${subClassName};
#end
#if($table.sub)
import com.ruoyi.common.orm.core.domain.BaseEntity;
#end

/**
 * ${functionName}视图对象 ${tableName}
 *
 * <AUTHOR>
 * @since ${datetime}
 */
#if($table.crud || $table.sub)
    #set($Entity="BaseEntity")
#elseif($table.tree)
    #set($Entity="TreeEntity")
#end
@Data
@ExcelIgnoreUnannotated
public class ${ClassName}ExcelVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

#foreach ($column in $columns)
     /** $column.columnComment */
#if($column.list)
#set($parentheseIndex=$column.columnComment.indexOf("（"))
#if($parentheseIndex != -1)
#set($comment=$column.columnComment.substring(0, $parentheseIndex))
#else
#set($comment=$column.columnComment)
#end
#if(${column.dictType} && ${column.dictType} != '')
    @ExcelProperty(value = "${comment}", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "${column.dictType}")
#elseif($parentheseIndex != -1)
    @ExcelProperty(value = "${comment}", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "$column.readConverterExp()")
#else
    @ExcelProperty(value = "${comment}")
#end
    private $column.javaType $column.javaField;

#else
    @ExcelProperty(value = "${column.columnComment}")
    private $column.javaType $column.javaField;

#end
#end

#if($table.sub)
    /** $table.subTable.functionName信息 */
    @RelationOneToMany(selfField = "${pkColumn.javaField}", targetField = "${pkColumn.javaField}")
    private List<${subClassName}> ${subclassName}List;
#end

}
