import type { AxiosRequestConfig } from '@vben/request';

import type { OssQuery, OssVO } from '#/api/system/oss/types';
import type { ID, IDS } from '#/types/global';

import { requestClient } from '#/api/request';

/**
 * 查询OSS对象存储列表
 * @param query
 */
export const listOss = (query?: OssQuery) => {
  return requestClient.get<PageData<OssVO>>('/resource/oss/list', query);
};

/**
 * 查询OSS对象存储详细
 * @param id
 */
export const getOss = (id: number | string) => {
  return requestClient.get<OssVO>(`/resource/oss/${id}`);
};

// 查询OSS对象基于id串
export const listByIds = (id: ID | IDS) => {
  return requestClient.get<OssVO[]>(`/resource/oss/listByIds/${id}`);
};

/**
 * 删除OSS对象存储权限
 * @param id
 */
export const removeOss = (id: Array<number | string> | number | string) => {
  return requestClient.delete(`/resource/oss/${id}`);
};

/**
 * 下载文件  返回为二进制
 * @param ossId ossId
 * @param onDownloadProgress 下载进度(可选)
 * @returns blob
 */
export function ossDownload(
  ossId: ID,
  onDownloadProgress?: AxiosRequestConfig['onDownloadProgress'],
) {
  return requestClient.get<Blob>(`/resource/oss/download/${ossId}`, {
    responseType: 'blob',
    timeout: 30 * 1000,
    isTransformResponse: false,
    onDownloadProgress,
  });
}
