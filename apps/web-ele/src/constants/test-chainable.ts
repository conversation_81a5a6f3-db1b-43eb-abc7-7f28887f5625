// 测试链式调用的 noFullWidthString 函数
import { noFullWidthString } from './zod-enum';

// 测试基础功能
console.log('=== 基础测试 ===');
const basicSchema = noFullWidthString();
console.log('半角字符:', basicSchema.safeParse('hello')); // 应该成功
console.log('全角字符:', basicSchema.safeParse('你好')); // 应该失败

// 测试链式调用 - 邮箱
console.log('\n=== 邮箱链式调用测试 ===');
const emailSchema = noFullWidthString().email('请输入正确的邮箱');
console.log('正确邮箱:', emailSchema.safeParse('<EMAIL>')); // 应该成功
console.log('全角邮箱:', emailSchema.safeParse('测试@example.com')); // 应该失败（全角字符）
console.log('错误格式:', emailSchema.safeParse('invalid-email')); // 应该失败（邮箱格式）

// 测试链式调用 - 正则表达式
console.log('\n=== 手机号链式调用测试 ===');
const phoneSchema = noFullWidthString().regex(/^1[3-9]\d{9}$/, '请输入正确的手机号');
console.log('正确手机:', phoneSchema.safeParse('13812345678')); // 应该成功
console.log('全角手机:', phoneSchema.safeParse('１３８１２３４５６７８')); // 应该失败（全角数字）
console.log('错误格式:', phoneSchema.safeParse('12345678901')); // 应该失败（手机号格式）

// 测试链式调用 - 长度限制
console.log('\n=== 长度链式调用测试 ===');
const lengthSchema = noFullWidthString().length(12).regex(/^\d{12}$/, '请输入12位数字');
console.log('正确长度:', lengthSchema.safeParse('123456789012')); // 应该成功
console.log('全角数字:', lengthSchema.safeParse('１２３４５６７８９０１２')); // 应该失败（全角数字）
console.log('错误长度:', lengthSchema.safeParse('12345')); // 应该失败（长度不够）

// 测试自定义错误消息
console.log('\n=== 自定义消息测试 ===');
const customSchema = noFullWidthString('用户名不能包含中文字符');
console.log('半角用户名:', customSchema.safeParse('username')); // 应该成功
console.log('中文用户名:', customSchema.safeParse('用户名')); // 应该失败，显示自定义消息

// 测试多重链式调用
console.log('\n=== 多重链式调用测试 ===');
const complexSchema = noFullWidthString()
  .min(3, '至少3个字符')
  .max(20, '最多20个字符')
  .regex(/^[a-zA-Z0-9_]+$/, '只能包含字母、数字和下划线');

console.log('复杂验证通过:', complexSchema.safeParse('user_123')); // 应该成功
console.log('复杂验证失败-全角:', complexSchema.safeParse('用户_123')); // 应该失败（全角字符）
console.log('复杂验证失败-长度:', complexSchema.safeParse('ab')); // 应该失败（长度不够）
console.log('复杂验证失败-格式:', complexSchema.safeParse('user-123')); // 应该失败（包含连字符）

export { 
  basicSchema, 
  emailSchema, 
  phoneSchema, 
  lengthSchema, 
  customSchema, 
  complexSchema 
};
