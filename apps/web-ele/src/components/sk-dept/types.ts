import type { DeptDataOption } from '#/api/system/dept/types';

/** 组件类型 */
export type Type = 'cascader' | 'select' | 'tree-select';

/** 层级深度 */
export type Level = 1 | 2 | 3;

/** 组件大小 */
export type Size = 'default' | 'large' | 'small';

/** 组件属性接口 */
export interface Props {
  /** 组件实现方式 */
  type?: Type;
  /** 层级深度 */
  level?: Level;
  /** 返回值类型 */
  jzType?: string;
  /** 组件大小 */
  size?: Size;
  /** 占位符文本数组 */
  placeholders?: string[];
  /** 是否禁用 */
  disabled?: boolean;
  /** 是否可清空 */
  clearable?: boolean;
  /** 是否多选 */
  multiple?: boolean;
  /** 是否启用虚拟滚动 */
  virtual?: boolean;
  /** 数据源 */
  options?: DeptDataOption[];
  /** 是否启用数据缓存 */
  enableCache?: boolean;
  /** 搜索防抖时间（毫秒） */
  debounceTime?: number;
  /** 加载状态 */
  loading?: boolean;
  /** 是否严格模式（只能选择叶子节点） */
  checkStrictly?: boolean;
}

/** 选中数据接口 */
export interface DeptData {
  /** 州市ID */
  zsid?: string;
  /** 州市代码 */
  zsdm?: string;
  /** 州市名称 */
  zsmc?: string;
  /** 区县ID */
  qxid?: string;
  /** 区县代码 */
  qxdm?: string;
  /** 区县名称 */
  qxmc?: string;
  /** 派出所ID */
  pcsid?: string;
  /** 派出所代码 */
  pcsdm?: string;
  /** 派出所名称 */
  pcsmc?: string;
  /** 完整路径 */
  path?: string[];
  /** 完整名称路径 */
  labelPath?: string[];
}

/** 组件实例方法接口 */
export interface DeptInstance {
  /** 获取选中数据 */
  getSelectData: () => DeptData;
  /** 获取原始数据 */
  deptData: DeptDataOption[];
  /** 获取组件实例 */
  element: any;
  /** 清空选择 */
  clear: () => void;
  /** 重新加载数据 */
  reload: () => Promise<void>;
  /** 设置数据 */
  setData: (data: DeptDataOption[]) => void;
}
