package com.ruoyi.dzgl.service;

import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.util.ObjectUtil;
import com.mybatisflex.core.query.QueryMethods;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.update.UpdateChain;
import com.ruoyi.common.core.core.domain.Result;
import com.ruoyi.common.core.utils.MapstructUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.utils.TreeBuildUtils;
import com.ruoyi.common.orm.core.service.impl.BaseServiceImpl;
import com.ruoyi.dzgl.domain.bo.dzys.DzysAddBo;
import com.ruoyi.dzgl.domain.bo.dzys.DzysEditBo;
import com.ruoyi.dzgl.domain.bo.dzys.DzysListBo;
import com.ruoyi.dzgl.domain.bo.dzys.DzysOptionBo;
import com.ruoyi.dzgl.domain.po.Dzys;
import com.ruoyi.dzgl.domain.po.table.DzysTableDef;
import com.ruoyi.dzgl.domain.vo.dzys.DzysDetailVo;
import com.ruoyi.dzgl.domain.vo.dzys.DzysListVo;
import com.ruoyi.dzgl.domain.vo.dzys.DzysOptionTreeVo;
import com.ruoyi.dzgl.domain.vo.dzys.DzysOptionVo;
import com.ruoyi.dzgl.mapper.DzysMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;

import static com.mybatisflex.core.query.QueryMethods.case_;
import static com.ruoyi.dzgl.domain.po.table.DzysTableDef.DZYS;

/**
 * 地址元素管理Service业务层处理
 * <p>
 * 本服务类提供地址元素管理地增删改查等基础操作，包括：
 * <ul>
 *     <li>分页查询地址元素管理列表</li>
 *     <li>查询地址元素管理详情</li>
 *     <li>新增地址元素管理</li>
 *     <li>修改地址元素管理</li>
 *     <li>删除地址元素管理</li>
 * </ul>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-04 16:50:12
 */
@Service
public class DzysService extends BaseServiceImpl<DzysMapper, Dzys> {

    private static final String YNS_ID = "37DFB1821B03A19AE0530AA6016BA19A";

    @Resource
    private DzysMapper dzysMapper;

    /**
     * 获取基础查询对象
     *
     * @return 预设了表信息的查询包装器
     */
    public QueryWrapper query() {
        return super.query().from(DZYS);
    }

    /**
     * 构建查询条件
     * <p>
     * 根据传入的查询参数构建查询条件，MyBatis Flex会自动处理空值
     *
     * @param bo 查询参数对象
     * @return 构建好地查询条件包装器
     */
    private QueryWrapper buildQueryWrapper(DzysListBo bo) {
        return super.buildBaseQueryWrapper()
            // 代码
            .and(DZYS.DM.like(bo.getDm()))
            // 名称
            .and(DZYS.MC.like(bo.getMc()))
            // 父结点ID
            .and(DZYS.PARENT_ID.eq(bo.getParentId()))
            // 地址元素类型代码
            .and(DZYS.DZYSLXDM.eq(bo.getDzyslxdm()))
            // 使用状态代码
            .and(DZYS.SYZTDM.eq(bo.getSyztdm()))
            // 抵边判断标志
            .and(DZYS.DB_PDBZ.eq(bo.getDbPdbz()))
            // 排序
            .orderBy(DZYS.DM.asc(), DZYS.ORDER_NUM.asc())
            ;
    }


    /**
     * 查询地址元素管理列表（不分页）
     * <p>
     * 根据查询条件获取所有符合条件的地址元素管理
     *
     * @param bo 地址元素管理查询参数
     * @return 地址元素管理集合
     */
    public List<DzysListVo> list(DzysListBo bo) {
        QueryWrapper queryWrapper = buildQueryWrapper(bo);
        String parentId = bo.getParentId();
        // 只有当bo为null或者bo的所有业务属性都为空时，才设置默认的parentId = "0"
        if (StringUtils.isEmpty(parentId)) {
            parentId = YNS_ID;
        }
        queryWrapper.and(DZYS.PARENT_ID.eq(parentId));
        queryWrapper.select(
            DZYS.ID,
            DZYS.JLZT,
            DZYS.DM,
            DZYS.MC,
            DZYS.JC,
            DZYS.PARENT_ID,
            DZYS.DZYSLXDM,
            DZYS.SYZTDM,
            DZYS.DB_PDBZ,
            case_(DZYS.PARENT_ID)
                .when("0").then(true)
                .else_(true)
                .end()
                .as("hasChildren")
        );
        return this.listAs(queryWrapper, DzysListVo.class);
    }

    /**
     * 获取地址元素管理选择项查询条件
     * <p>
     * 用于下拉选择等场景的查询条件构建
     *
     * @param optionBo 选择项查询参数
     * @param tableDef 地址元素管理表定义对象
     * @return 选择项查询条件
     */
    private static QueryWrapper getOptionQueryWrapper(DzysOptionBo optionBo, DzysTableDef tableDef) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.and(tableDef.SYZTDM.eq("10"));
        Integer level = optionBo.getLevel();
        if (level == null) {
            level = 5;
        }
        if (level == 1) {
            //州市
            queryWrapper.and(tableDef.DZYSLXDM.eq("12"));
        }
        if (level == 2) {
            //区县
            queryWrapper.and(tableDef.DZYSLXDM.in("12", "13"));
        }
        if (level == 3) {
            //乡镇街道
            queryWrapper.and(tableDef.DZYSLXDM.notLikeLeft("4")).and(tableDef.DZYSLXDM.notLikeLeft("3"));
        }
        if (level == 4) {
            //村居委会
            queryWrapper.and(tableDef.DZYSLXDM.notLikeLeft("4"));
        }
        queryWrapper.select(
                tableDef.ID.as("ID"),
                tableDef.DM.as("value"),
                tableDef.PARENT_ID,
                tableDef.ORDER_NUM,
                tableDef.JC.as("label"),
                case_()
                    .when(tableDef.DZYSLXDM.likeLeft("4").and(String.valueOf(level == 5))).then(true)
                    .when(tableDef.DZYSLXDM.likeLeft("3").and(String.valueOf(level == 4))).then(true)
                    .when(tableDef.DZYSLXDM.likeLeft("2").and(String.valueOf(level == 3))).then(true)
                    .when(tableDef.DZYSLXDM.eq("13").and(String.valueOf(level == 2))).then(true)
                    .when(tableDef.DZYSLXDM.eq("12").and(String.valueOf(level == 1))).then(true)
                    .else_(false)
                    .end()
                    .as("isLeaf")
            )
            .from(tableDef)
            .orderBy(tableDef.ORDER_NUM, true);
        return queryWrapper;
    }

    /**
     * 查询地址元素管理管理选择列表
     * <p>
     * 用于前端下拉选择等场景，返回简化的地址元素管理信息
     *
     * @param optionBo 选择项查询参数
     * @return 包含地址元素管理选项列表的结果对象
     */
    public Result<List<DzysOptionVo>> optionSelect(DzysOptionBo optionBo) {
        DzysTableDef tableDef = DZYS.as("a");
        QueryWrapper queryWrapper = getOptionQueryWrapper(optionBo, tableDef);
        String parentId = optionBo.getParentId();
        if (StringUtils.isEmpty(parentId)) {
            parentId = YNS_ID;
        }
        queryWrapper.and(tableDef.PARENT_ID.eq(parentId));
        return Result.success(this.listAs(queryWrapper, DzysOptionVo.class));
    }

    /**
     * 查询地址元素管理管理选择树形列表
     * <p>
     * 用于前端树形选择控件，返回树形结构的地址元素管理信息
     *
     * @param optionBo 选择项查询参数
     * @return 树形结构的地址元素管理信息列表
     */
    public Result<List<Tree<String>>> optionTree(DzysOptionBo optionBo) {
        DzysTableDef tableDef = DZYS.as("a");
        QueryWrapper queryWrapper = getOptionQueryWrapper(optionBo, tableDef);
        List<DzysOptionTreeVo> optionVoList = this.listAs(queryWrapper, DzysOptionTreeVo.class);
        return Result.success(TreeBuildUtils.buildOptionTree(optionVoList, YNS_ID));
    }


    /**
     * 新增地址元素管理
     * <p>
     * 将前端传入的数据转换为实体并保存到数据库
     *
     * @param bo 地址元素管理新增参数
     * @return 操作结果，成功返回{@code Result.success()}，失败返回包含错误信息的{@code Result.fail()}
     */
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> add(DzysAddBo bo) {
        Dzys entity = MapstructUtils.convert(bo, Dzys.class);
        // 设置祖级列表
        setAncestors(entity, bo.getParentId());
        boolean inserted = this.save(entity);
        return inserted
            ? Result.success()
            : Result.fail("新增地址元素管理记录失败！");
    }

    /**
     * 修改地址元素管理
     * <p>
     * 更新数据库中已存在的地址元素管理
     *
     * @param bo 地址元素管理编辑参数
     * @return 操作结果，成功返回{@code Result.success()}，失败返回包含错误信息的{@code Result.fail()}
     */
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> edit(DzysEditBo bo) {
        Dzys entity = MapstructUtils.convert(bo, Dzys.class);
        if (ObjectUtil.isNull(entity) || ObjectUtil.isNull(entity.getId())) {
            return Result.fail("地址元素管理ID不能为空");
        }

        // 更新祖级列表字段
        Dzys newParentEntity = getById(entity.getParentId());
        Dzys oldEntity = getById(entity.getId());
        if (ObjectUtil.isNotNull(newParentEntity) && ObjectUtil.isNotNull(oldEntity)) {
            String newAncestors = newParentEntity.getAncestors() + "," + newParentEntity.getId();
            String oldAncestors = oldEntity.getAncestors();
            entity.setAncestors(newAncestors);
            editDzysChildren(entity.getId(), newAncestors, oldAncestors);
        }
        boolean updated = this.updateById(entity);
        return updated
            ? Result.success()
            : Result.fail("修改地址元素管理记录失败!");
    }

    /**
     * 修改子元素关系
     * <p>
     * 更新所有子节点的祖级列表字段
     *
     * @param id           主键ID
     * @param newAncestors 新的父ID集合
     * @param oldAncestors 旧的父ID集合
     */
    private void editDzysChildren(String id, String newAncestors, String oldAncestors) {
        // 查询所有子节点
        QueryWrapper queryWrapper = QueryWrapper.create()
            .from(DZYS)
            .where(QueryMethods.findInSet(QueryMethods.string(id), DZYS.ANCESTORS).gt(0));

        List<Dzys> children = list(queryWrapper);

        // 更新子节点的祖级列表
        for (Dzys child : children) {
            child.setAncestors(child.getAncestors().replaceFirst(oldAncestors, newAncestors));

            UpdateChain.of(Dzys.class)
                .set(Dzys::getAncestors, child.getAncestors())
                .where(Dzys::getId).eq(child.getId())
                .update();
        }
    }

    /**
     * 设置实体的祖级列表
     * <p>
     * 根据父ID设置实体的祖级列表字段
     *
     * @param entity   需要设置祖级列表的实体
     * @param parentId 父ID
     */
    private void setAncestors(Dzys entity, String parentId) {
        if (StringUtils.equals(parentId, "0")) {
            entity.setAncestors("0");
        } else {
            Dzys parentEntity = getById(parentId);
            if (ObjectUtil.isNotNull(parentEntity)) {
                entity.setAncestors(parentEntity.getAncestors() + "," + parentId);
            } else {
                entity.setAncestors("0");
            }
        }
    }

    /**
     * 批量删除地址元素管理
     * <p>
     * 根据ID数组删除多条地址元素管理记录
     *
     * @param ids 需要删除的地址元素管理主键数组
     * @return 操作结果，成功返回{@code Result.success()}，失败返回包含错误信息的{@code Result.fail()}
     * @throws IllegalArgumentException 当ids为null或空数组时抛出
     */
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> removeByIds(String[] ids) {
        // 参数校验
        if (ids == null || ids.length == 0) {
            return Result.fail("删除ID不能为空");
        }

        // 执行批量删除
        return this.removeByIds(Arrays.asList(ids))
            ? Result.success()
            : Result.fail("删除地址元素管理记录失败!");
    }

    /**
     * 查询地址元素管理详情
     * <p>
     * 根据ID获取单个地址元素管理的详细信息
     *
     * @param id 地址元素管理主键
     * @return 地址元素管理详细信息，如果id为空则返回null
     */
    public DzysDetailVo detail(String id) {
        if (StringUtils.isEmpty(id)) {
            return null;
        }

        return this.getOneAs(
            query().where(DZYS.ID.eq(id)),
            DzysDetailVo.class
        );
    }

    /**
     * 新增地址元素管理（带主键）
     * <p>
     * 使用前端提供的主键值新增记录，通常用于数据导入场景
     *
     * @param bo 地址元素管理新增参数（含主键）
     * @return 操作结果，成功返回{@code Result.success()}，失败返回包含错误信息的{@code Result.fail()}
     */
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> insertWithPk(DzysAddBo bo) {
        Dzys entity = MapstructUtils.convert(bo, Dzys.class);
        // 设置祖级列表
        setAncestors(entity, bo.getParentId());
        // 使用特定的mapper方法执行带主键的插入
        boolean inserted = dzysMapper.insertWithPk(entity) > 0;
        return inserted
            ? Result.success()
            : Result.fail("新增地址元素管理记录失败！");
    }


}
