// 测试修复后的 noFullWidthString 函数
import { noFullWidthString } from './zod-enum';

// 测试基础功能
const basicSchema = noFullWidthString();
console.log('基础测试:', basicSchema.safeParse('hello')); // 应该成功
console.log('基础测试:', basicSchema.safeParse('你好')); // 应该失败

// 测试链式调用 - 邮箱
const emailSchema = noFullWidthString().email('请输入正确的邮箱');
console.log('邮箱测试:', emailSchema.safeParse('<EMAIL>')); // 应该成功
console.log('邮箱测试:', emailSchema.safeParse('测试@example.com')); // 应该失败（全角字符）
console.log('邮箱测试:', emailSchema.safeParse('invalid-email')); // 应该失败（邮箱格式）

// 测试链式调用 - 正则表达式
const phoneSchema = noFullWidthString().regex(/^1[3-9]\d{9}$/, '请输入正确的手机号');
console.log('手机测试:', phoneSchema.safeParse('13812345678')); // 应该成功
console.log('手机测试:', phoneSchema.safeParse('１３８１２３４５６７８')); // 应该失败（全角数字）
console.log('手机测试:', phoneSchema.safeParse('12345678901')); // 应该失败（手机号格式）

// 测试链式调用 - 长度限制
const lengthSchema = noFullWidthString().length(12).regex(/^\d{12}$/, '请输入12位数字');
console.log('长度测试:', lengthSchema.safeParse('123456789012')); // 应该成功
console.log('长度测试:', lengthSchema.safeParse('１２３４５６７８９０１２')); // 应该失败（全角数字）
console.log('长度测试:', lengthSchema.safeParse('12345')); // 应该失败（长度不够）

// 测试自定义错误消息
const customSchema = noFullWidthString('用户名不能包含中文字符');
console.log('自定义消息测试:', customSchema.safeParse('username')); // 应该成功
console.log('自定义消息测试:', customSchema.safeParse('用户名')); // 应该失败，显示自定义消息

export { basicSchema, emailSchema, phoneSchema, lengthSchema, customSchema };
