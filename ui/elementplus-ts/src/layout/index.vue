<template>
  <div :class="classObj" class="app-wrapper" :style="{ '--current-color': theme }">
    <div v-if="device === 'mobile' && sidebar.opened" class="drawer-bg" @click="handleClickOutside" />
    <side-bar v-if="!sidebar.hide" v-show="!isFullscreen && !isBodyFullscreen" class="sidebar-container" />
    <div
      :class="{ 'is-fullscreen ': isFullscreen || isBodyFullscreen, hasTagsView: needTagsView, sidebarHide: sidebar.hide }"
      class="main-container transition-all-300"
    >
      <div :class="{ 'fixed-header': fixedHeader }">
        <navbar v-show="!isFullscreen && !isBodyFullscreen" ref="navbarRef" @set-layout="setLayout" />
        <TagsView v-if="needTagsView" v-model:isFullscreen="isFullscreen" v-model:isBodyFullscreen="isBodyFullscreen" />
      </div>
      <app-main
        class="body-main"
        :class="{ 'is-fullscreen ': isBodyFullscreen, hasTagsView: needTagsView, sidebarHide: sidebar.hide }"
      />
      <Maximize v-show="isBodyFullscreen" @exit-maximize="exiMaximize" />
      <settings ref="settingRef" />
      <el-footer>
        <Footer />
      </el-footer>
    </div>
  </div>
</template>

<script setup lang="ts">
import SideBar from './components/Sidebar/index.vue';
import Footer from './components/Footer/index.vue';
import { AppMain, Navbar, Settings, TagsView } from './components';
import useAppStore from '@/store/modules/app';
import useSettingsStore from '@/store/modules/settings';
import { initWebSocket } from '@/utils/websocket';
import Maximize from '@/layout/components/TagsView/Maximize.vue';
import { initSSE } from '@/utils/sse';

const settingsStore = useSettingsStore();
const theme = computed(() => settingsStore.theme);
const sidebar = computed(() => useAppStore().sidebar);
const device = computed(() => useAppStore().device);
const needTagsView = computed(() => settingsStore.tagsView);
const fixedHeader = computed(() => settingsStore.fixedHeader);

const classObj = computed(() => ({
  hideSidebar: !sidebar.value.opened,
  openSidebar: sidebar.value.opened,
  withoutAnimation: sidebar.value.withoutAnimation,
  mobile: device.value === 'mobile'
}));

const { width } = useWindowSize();
const WIDTH = 992; // refer to Bootstrap's responsive design
const isFullscreen = ref(false);
const isBodyFullscreen = ref(false);

watchEffect(() => {
  if (device.value === 'mobile' && sidebar.value.opened) {
    useAppStore().closeSideBar({ withoutAnimation: false });
  }
  if (width.value - 1 < WIDTH) {
    useAppStore().toggleDevice('mobile');
    useAppStore().closeSideBar({ withoutAnimation: true });
  } else {
    useAppStore().toggleDevice('desktop');
  }
});

const navbarRef = ref<InstanceType<typeof Navbar>>();
const settingRef = ref<InstanceType<typeof Settings>>();

onMounted(() => {
  nextTick(() => {
    navbarRef.value?.initTenantList();
  });
});

onMounted(() => {
  let protocol = window.location.protocol === 'https:' ? 'wss://' : 'ws://';
  initWebSocket(protocol + window.location.host + import.meta.env.VITE_APP_BASE_API + '/resource/websocket');
});

onMounted(() => {
  initSSE(import.meta.env.VITE_APP_BASE_API + '/resource/sse');
});

const handleClickOutside = () => {
  useAppStore().closeSideBar({ withoutAnimation: false });
};

const setLayout = () => {
  settingRef.value?.openSetting();
};
const exiMaximize = () => {
  isBodyFullscreen.value = false;
};
</script>

<style lang="scss" scoped>
@import '@/assets/styles/mixin.scss';
@import '@/assets/styles/variables.module.scss';

.app-wrapper {
  @include clearfix;
  position: relative;
  height: 100%;
  width: 100%;

  &.mobile.openSidebar {
    position: fixed;
    top: 0;
  }
}

.drawer-bg {
  background: #000;
  opacity: 0.3;
  width: 100%;
  top: 0;
  height: 100%;
  position: absolute;
  z-index: 999;
}

.fixed-header {
  position: fixed;
  top: 0;
  right: 0;
  z-index: 9;
  width: calc(100% - #{$base-sidebar-width});
  transition: width 0.28s;
  background: $fixed-header-bg;
}

.hideSidebar .fixed-header {
  width: calc(100% - 54px);
}

.sidebarHide .fixed-header {
  width: 100%;
}

.mobile .fixed-header {
  width: 100%;
}
//全屏功能
.is-fullscreen {
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  position: fixed !important;
  margin-left: 0px !important;
}
.transition-all-300 {
  transition-property: all !important;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1) !important;
  transition-duration: 0.5s !important;
}

//内容按钮
.maximize {
  position: fixed;
  top: -25;
}
</style>
