import type { ColumnProps, SkTableInstance } from '@/components/SkUi/SkTable/interface';
import { reactive, ref } from 'vue';
import type { OnlineVO } from '@/api/monitor/online/types';
import { forceLogout } from '@/api/monitor/online';
import to from 'await-to-js';

export function useOnline() {
  const skTable = ref<SkTableInstance>();

  /**
   * 查询列配置
   */
  const columns = reactive<ColumnProps<OnlineVO>[]>([
    { type: 'index', label: '#', width: 80 },
    {
      prop: 'tokenId',
      label: '会话编号',
      align: 'center'
    },
    { prop: 'userName', label: '登录名称', align: 'left', search: { el: 'input' } },
    { prop: 'clientKey', label: '客户端', align: 'center' },
    {
      prop: 'sblx',
      label: '设备类型',
      align: 'center'
    },
    { prop: 'deptName', label: '所属部门', align: 'center' },
    {
      prop: 'ipaddr',
      label: '主机',
      align: 'center'
    },
    {
      prop: 'loginLocation',
      label: '登录地点',
      align: 'center',
      search: { el: 'input' }
    },
    {
      prop: 'os',
      label: '操作系统',
      align: 'center'
    },
    {
      prop: 'browser',
      label: '浏览器',
      align: 'center',
      width: '120'
    },
    {
      prop: 'loginTime',
      label: '登录时间',
      align: 'center',
      width: '120'
    },
    { prop: 'operation', label: '操作', width: 80, fixed: 'right' }
  ]);

  /** 强退按钮操作 */
  const handleForceLogout = async (row: OnlineVO) => {
    const [err] = await to(window.skynet.confirm('是否确认强退名称为"' + row.userName + '"的用户?') as any);
    if (!err) {
      await forceLogout(row.tokenId);
      await skTable.value?.getTableList();
      window.skynet.alert('删除成功');
    }
  };

  return {
    skTable,
    columns,
    handleForceLogout
  };
}
