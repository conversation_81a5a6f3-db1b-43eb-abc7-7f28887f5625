// 测试类型修复后的 noFullWidthString 函数
import { noFullWidthString } from './zod-enum';

console.log('=== 类型修复后的测试 ===');

// 1. 基础测试
console.log('\n1. 基础功能测试:');
const basic = noFullWidthString();

try {
  const result1 = basic.safeParse('hello');
  console.log('✅ 半角字符 safeParse:', result1);
} catch (error) {
  console.log('❌ 半角字符 safeParse 错误:', error);
}

try {
  const result2 = basic.safeParse('你好');
  console.log('❌ 全角字符 safeParse:', result2);
} catch (error) {
  console.log('❌ 全角字符 safeParse 错误:', error);
}

// 2. parse 方法测试
console.log('\n2. parse 方法测试:');

try {
  const result3 = basic.parse('hello');
  console.log('✅ 半角字符 parse:', result3);
} catch (error) {
  console.log('❌ 半角字符 parse 错误:', error);
}

try {
  const result4 = basic.parse('你好');
  console.log('❌ 全角字符 parse:', result4);
} catch (error) {
  console.log('❌ 全角字符 parse 错误:', error.message);
}

// 3. 链式调用测试
console.log('\n3. 链式调用测试:');

try {
  const emailSchema = noFullWidthString().email('请输入正确的邮箱');
  console.log('✅ 邮箱链式调用创建成功');
  
  const emailResult1 = emailSchema.safeParse('<EMAIL>');
  console.log('✅ 正确邮箱:', emailResult1);
  
  const emailResult2 = emailSchema.safeParse('测试@example.com');
  console.log('❌ 全角邮箱:', emailResult2);
  
  const emailResult3 = emailSchema.safeParse('invalid-email');
  console.log('❌ 错误格式:', emailResult3);
} catch (error) {
  console.log('❌ 邮箱链式调用错误:', error);
}

// 4. 正则表达式链式调用测试
console.log('\n4. 正则表达式链式调用测试:');

try {
  const phoneSchema = noFullWidthString().regex(/^1[3-9]\d{9}$/, '请输入正确的手机号');
  console.log('✅ 手机号链式调用创建成功');
  
  const phoneResult1 = phoneSchema.safeParse('13812345678');
  console.log('✅ 正确手机号:', phoneResult1);
  
  const phoneResult2 = phoneSchema.safeParse('１３８１２３４５６７８');
  console.log('❌ 全角手机号:', phoneResult2);
} catch (error) {
  console.log('❌ 手机号链式调用错误:', error);
}

// 5. 复杂链式调用测试
console.log('\n5. 复杂链式调用测试:');

try {
  const complexSchema = noFullWidthString()
    .min(3, '至少3个字符')
    .max(20, '最多20个字符')
    .regex(/^[a-zA-Z0-9_]+$/, '只能包含字母、数字和下划线');
  
  console.log('✅ 复杂链式调用创建成功');
  
  const complexResult1 = complexSchema.safeParse('user_123');
  console.log('✅ 复杂验证通过:', complexResult1);
  
  const complexResult2 = complexSchema.safeParse('用户_123');
  console.log('❌ 复杂验证失败-全角:', complexResult2);
  
  const complexResult3 = complexSchema.safeParse('ab');
  console.log('❌ 复杂验证失败-长度:', complexResult3);
} catch (error) {
  console.log('❌ 复杂链式调用错误:', error);
}

// 6. 自定义错误消息测试
console.log('\n6. 自定义错误消息测试:');

try {
  const customSchema = noFullWidthString('用户名不能包含中文字符');
  
  const customResult1 = customSchema.safeParse('username');
  console.log('✅ 自定义消息通过:', customResult1);
  
  const customResult2 = customSchema.safeParse('用户名');
  console.log('❌ 自定义消息失败:', customResult2);
} catch (error) {
  console.log('❌ 自定义消息错误:', error);
}

export { basic };
