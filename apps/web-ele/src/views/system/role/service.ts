import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { MenuDataOption } from '#/api/system/menu/types';
import type { RoleForm, RoleVO } from '#/api/system/role/types';
import type { ButtonConfig } from '#/components/action-button-group/types';

import { useVbenDrawer, useVbenForm } from '@vben/common-ui';
import { Delete, Edit, Plus } from '@vben/icons';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { listMenuOption, listMenuTreeSelectVo } from '#/api/system/menu';
import {
  addRole,
  editRole,
  getRole,
  listRole,
  removeRole,
} from '#/api/system/role';
import MenuPermission from '#/components/system/menu-permission/index.vue';

import { useFormSchema, useGridColumns, useGridFormSchema } from './data';

export const useRoleService = () => {
  const menuPermissionRef = ref<InstanceType<typeof MenuPermission>>();
  const menuTree = ref<MenuDataOption[]>([]);
  const router = useRouter();
  const dataScopeRef = ref();
  // 查询表单和表格配置
  const [BasicTable, gridApi] = useVbenVxeGrid({
    formOptions: {
      schema: useGridFormSchema(),
      commonConfig: {
        labelWidth: 120,
        componentProps: {
          allowClear: true,
        },
      },
    },
    gridOptions: {
      columns: useGridColumns(),
      pagerConfig: { enabled: true },
      proxyConfig: {
        ajax: {
          query: async (_, formValues = {}) => {
            return await listRole({
              ...formValues,
            });
          },
        },
      },
    } as VxeTableGridOptions,
  });

  // 编辑表单配置
  const [Form, formApi] = useVbenForm({
    schema: useFormSchema(),
    commonConfig: {
      labelWidth: 100,
    },
    showDefaultActions: false,
    wrapperClass: 'md:grid-cols-1',
  });

  /**
   * 异步函数：根据角色ID设置菜单树
   * 如果提供了ID，则获取对应角色的菜单树和选中的菜单项
   * 如果未提供ID，则获取默认的菜单树，并清空选中的菜单项
   * @param id 可选参数，角色ID，可以是数字或字符串
   */
  async function setupMenuTree(id?: number | string) {
    if (id) {
      // 根据角色ID获取菜单树数据
      const resp = await listMenuTreeSelectVo({ roleId: id });
      // 设置菜单信息
      menuTree.value = resp.menus;
      // keys依赖于menu 需要先加载menu
      await nextTick();
      // 设置选中的菜单项
      await formApi.setFieldValue('menuIds', resp.checkedKeys);
    } else {
      // 设置默认的菜单信息
      menuTree.value = await listMenuOption({ isTree: true });
      // keys依赖于menu 需要先加载menu
      await nextTick();
      // 清空选中的菜单项
      await formApi.setFieldValue('menuIds', []);
    }
  }

  // 编辑抽屉配置
  const [FormDialog, formDialogApi] = useVbenDrawer({
    async onConfirm() {
      const { valid } = await formApi.validate();
      if (!valid) return;
      const data = await formApi.getValues<RoleForm>();
      formDialogApi.lock();
      // 这个用于提交
      data.menuIds = menuPermissionRef.value?.getCheckedKeys?.() ?? [];
      (data?.id ? editRole(data) : addRole(data))
        .then(() => {
          onRefresh();
          formDialogApi.close();
        })
        .catch(() => {
          formDialogApi.unlock();
        });
    },
    async onOpenChange(isOpen) {
      if (isOpen) {
        const data = formDialogApi.getData<RoleForm>();
        if (data) {
          formApi.setValues(data).then(() => {});
        } else {
          formApi.resetForm().then(() => {});
        }
        const id = data.id;
        // init菜单 注意顺序要放在赋值record之后 内部watch会依赖record
        await setupMenuTree(id);
      }
    },
    destroyOnClose: true,
  });

  /**
   * 通过回调更新 无法通过v-model
   * @param value 菜单选择是否严格模式
   */
  function handleMenuCheckStrictlyChange(value: boolean) {
    formApi.setFieldValue('menuCheckStrictly', value).then();
  }

  /**
   * 刷新表格
   */
  const onRefresh = async () => {
    await gridApi.query();
  };

  /**
   * 创建角色信息
   */
  const onCreate = () => {
    formDialogApi.setData(null).open();
  };

  /**
   * 编辑角色信息
   */
  const handleEdit = async (row: RoleVO) => {
    const data = await getRole(row.id);
    formDialogApi.setData(data).open();
  };

  /**
   * 删除角色信息
   */
  const handleDelete = async (row: RoleVO) => {
    await ElMessageBox.confirm('确认删除该角色信息吗？', '提示', {
      type: 'warning',
    })
      .then(async () => {
        await removeRole(row.id).then(() => {
          onRefresh();
        });
      })
      .catch(() => {});
  };

  /**
   * 打开特定用户角色的详情页面
   * 此函数通过接收一个用户角色ID，来路由导航到该用户角色的详情页面
   * 选择使用路由名称'FeatureTabDetailDemo'进行导航，因为这种方式提供了更明确的页面引用，
   * 同时传递ID作为路由参数，确保目标页面可以接收到必要的信息以展示正确的数据
   *
   * @param row
   */
  function handleAssignRole(row: RoleVO) {
    router.push(`/system/role-assign/${row.id}`).then();
  }

  function handleDataScope(row: RoleVO) {
    dataScopeRef.value.setData({ id: row.id });
    dataScopeRef.value.open();
  }

  /**
   * 工具栏按钮配置
   */
  const useToolbarButtons = (): ButtonConfig[] => {
    return [
      {
        label: '新增角色信息',
        type: 'primary',
        icon: Plus,
        onClick: onCreate,
        auth: ['system:role:add'],
      },
    ];
  };

  /**
   * 行操作按钮配置
   */
  const useRowButtons = (): ButtonConfig[] => {
    return [
      {
        label: '编辑',
        icon: Edit,
        link: true,
        onClick: handleEdit,
        auth: ['system:role:edit'],
      },
      {
        label: '分配用户',
        icon: Edit,
        link: true,
        onClick: handleAssignRole,
        auth: ['system:role:edit'],
      },
      {
        label: '数据权限',
        icon: Edit,
        link: true,
        onClick: handleDataScope,
        auth: ['system:role:edit'],
      },
      {
        label: '删除',
        icon: Delete,
        link: true,
        onClick: handleDelete,
        auth: ['system:role:remove'],
      },
    ];
  };

  return {
    Form,
    BasicTable,
    FormDialog,
    useToolbarButtons,
    useRowButtons,
    handleMenuCheckStrictlyChange,
    menuPermissionRef,
    onRefresh,
    formApi,
    menuTree,
    dataScopeRef,
  };
};
