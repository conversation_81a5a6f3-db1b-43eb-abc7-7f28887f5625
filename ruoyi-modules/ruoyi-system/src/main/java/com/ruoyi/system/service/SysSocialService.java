package com.ruoyi.system.service;

import com.mybatisflex.core.query.QueryWrapper;
import com.ruoyi.common.core.utils.MapstructUtils;
import com.ruoyi.common.orm.core.service.impl.BaseServiceImpl;
import com.ruoyi.system.domain.bo.SysSocialBo;
import com.ruoyi.system.domain.po.SysSocial;
import com.ruoyi.system.domain.vo.SysSocialVo;
import com.ruoyi.system.mapper.SysSocialMapper;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.ruoyi.system.domain.po.table.SysSocialTableDef.SYS_SOCIAL;

/**
 * 社会化关系Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-06-12
 */
@RequiredArgsConstructor
@Service
public class SysSocialService extends BaseServiceImpl<SysSocialMapper, SysSocial> {

    @Resource
    private SysSocialMapper sysSocialMapper;


    /**
     * 查询社会化关系
     */
    public SysSocialVo queryById(String id) {
        return sysSocialMapper.selectOneWithRelationsByIdAs(id, SysSocialVo.class);
    }

    /**
     * 授权列表
     */
    public List<SysSocialVo> queryList(SysSocialBo bo) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.and(SYS_SOCIAL.USER_ID.eq(bo.getUserId()));
        queryWrapper.and(SYS_SOCIAL.AUTH_ID.eq(bo.getAuthId()));
        queryWrapper.and(SYS_SOCIAL.SOURCE.eq(bo.getSource()));
        return sysSocialMapper.selectListByQueryAs(QueryWrapper.create().from(SYS_SOCIAL), SysSocialVo.class);
    }

    public List<SysSocialVo> selectListByUserId(String userId) {
        return sysSocialMapper.selectListByQueryAs(QueryWrapper.create().from(SYS_SOCIAL).where(SYS_SOCIAL.USER_ID.eq(userId)), SysSocialVo.class);
    }


    /**
     * 新增社会化关系
     */
    public Boolean insertByBo(SysSocialBo bo) {
        SysSocial add = MapstructUtils.convert(bo, SysSocial.class);
        validEntityBeforeSave(add);
        return sysSocialMapper.insert(add, true) > 0;
    }

    /**
     * 更新社会化关系
     */
    public Boolean updateByBo(SysSocialBo bo) {
        SysSocial update = MapstructUtils.convert(bo, SysSocial.class);
        validEntityBeforeSave(update);
        return sysSocialMapper.update(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SysSocial entity) {
        //TODO 做一些数据校验,如唯一约束
    }


    /**
     * 删除社会化关系
     */
    public Boolean deleteWithValidById(String id) {
        return sysSocialMapper.deleteById(id) > 0;
    }


    /**
     * 根据 authId 查询用户信息
     *
     * @param authId 认证id
     * @return 授权信息
     */
    public List<SysSocialVo> selectListByAuthId(String authId) {
        return sysSocialMapper.selectListByQueryAs(QueryWrapper.create().from(SYS_SOCIAL).where(SYS_SOCIAL.AUTH_ID.eq(authId)), SysSocialVo.class);
    }

}
