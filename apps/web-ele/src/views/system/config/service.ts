import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { ConfigForm, ConfigVO } from '#/api/system/config/types';
import type { ButtonConfig } from '#/components/action-button-group/types';

import { useVbenDrawer, useVbenForm } from '@vben/common-ui';
import { Delete, Edit, Plus, Refresh } from '@vben/icons';

import { useVbenVxeGrid, vxeCheckboxChecked } from '#/adapter/vxe-table';
import {
  addConfig,
  editConfig,
  getConfig,
  listConfig,
  refreshCache,
  removeConfig,
} from '#/api/system/config';

import { useFormSchema, useGridColumns, useGridFormSchema } from './data';

export const useConfigService = () => {
  // 查询表单和表格配置
  const [BasicTable, gridApi] = useVbenVxeGrid({
    formOptions: {
      schema: useGridFormSchema(),
      commonConfig: {
        labelWidth: 120,
        componentProps: {
          allowClear: true,
        },
      },
    },
    gridOptions: {
      columns: useGridColumns(),
      pagerConfig: { enabled: true },
      proxyConfig: {
        ajax: {
          query: async ({ page }, formValues = {}) => {
            return await listConfig({
              ...formValues,
              pageNum: page.currentPage,
              pageSize: page.pageSize,
            });
          },
        },
      },
    } as VxeTableGridOptions,
  });

  // 编辑表单配置
  const [Form, formApi] = useVbenForm({
    schema: useFormSchema(),
    commonConfig: {
      labelWidth: 80,
    },
    showDefaultActions: false,
    wrapperClass: 'md:grid-cols-1',
  });

  // 编辑抽屉配置
  const [FormDialog, formDialogApi] = useVbenDrawer({
    async onConfirm() {
      const { valid } = await formApi.validate();
      if (!valid) return;
      const data = await formApi.getValues<ConfigForm>();
      formDialogApi.lock();
      (data?.id ? editConfig(data) : addConfig(data))
        .then(() => {
          handleQuery();
          formDialogApi.close();
        })
        .catch(() => {
          formDialogApi.unlock();
        });
    },
    onOpenChange(isOpen) {
      if (isOpen) {
        const data = formDialogApi.getData<ConfigForm>();
        if (data) {
          formApi.setValues(data).then(() => {});
        } else {
          formApi.resetForm().then(() => {});
        }
      }
    },
    destroyOnClose: true,
  });

  /**
   * 刷新表格
   */
  const handleQuery = async () => {
    await gridApi.query();
  };

  /**
   * 创建参数配置
   */
  const handleCreate = () => {
    formDialogApi.setData(null).open();
  };

  /**
   * 编辑参数配置
   */
  const handleEdit = async (row: ConfigVO) => {
    const data = await getConfig(row.id);
    formDialogApi.setData(data).open();
  };

  /**
   * 删除参数配置
   */
  const handleDelete = async (row: ConfigVO) => {
    await window.skynet
      .confirm('确认要删除选中的参数配置?')
      .then(async () => {
        await removeConfig(row.id).then(() => {
          handleQuery();
        });
      })
      .catch(() => {});
  };

  /**
   * 批量删除
   */
  const handleMultipleDelete = async function () {
    const rows = gridApi.grid.getCheckboxRecords();
    const ids = rows.map((row: ConfigVO) => row.id);

    await window.skynet
      .confirm('是否要删除选中的参数配置?')
      .then(async () => {
        await removeConfig(ids);
        await handleQuery();
        await gridApi.grid.clearCheckboxRow();
      })
      .catch(() => {});
  };

  /**
   * 刷新缓存
   */
  async function handleRefreshCache() {
    await refreshCache();
    await handleQuery();
  }

  /**
   * 工具栏按钮配置
   */
  const useToolbarButtons = (): ButtonConfig[] => {
    return [
      {
        label: '刷新缓存',
        type: 'primary',
        icon: Refresh,
        onClick: handleRefreshCache,
        auth: ['system:config:edit'],
      },
      {
        label: '新增参数配置',
        type: 'primary',
        icon: Plus,
        onClick: handleCreate,
        auth: ['system:config:add'],
      },
      {
        label: '删除',
        icon: Delete,
        onClick: handleMultipleDelete,
        auth: ['system:config:remove'],
        disabled: !vxeCheckboxChecked(gridApi),
      },
    ];
  };

  /**
   * 行操作按钮配置
   */
  const useRowButtons = (): ButtonConfig[] => {
    return [
      {
        label: '编辑',
        icon: Edit,
        link: true,
        onClick: handleEdit,
        auth: ['system:config:edit'],
      },
      {
        label: '删除',
        icon: Delete,
        link: true,
        onClick: handleDelete,
        auth: ['system:config:remove'],
      },
    ];
  };

  return {
    BasicTable,
    Form,
    FormDialog,
    useToolbarButtons,
    useRowButtons,
  };
};
