import type { ConfigForm, ConfigQuery, ConfigVO } from './types';
import http from '@/utils/http';

// 查询参数列表
export const listConfig = (query: ConfigQuery) => {
  return http.get<ConfigVO[]>('/system/config/list', query);
};

// 查询参数详细
export const getConfig = (id: string | number) => {
  return http.get<ConfigVO>('/system/config/' + id);
};

// 根据参数键名查询参数值
export const getConfigKey = (configKey: string) => {
  return http.get<ConfigVO>('/system/config/configKey/' + configKey);
};

// 新增参数配置
export const addConfig = (data: ConfigForm) => {
  return http.post('/system/config', data);
};

// 修改参数配置
export const updateConfig = (data: ConfigForm) => {
  return http.put('/system/config', data);
};

// 修改参数配置
export const updateConfigByKey = (key: string, value: any, version: number) => {
  return http.put('/system/config/updateByKey', {
    configKey: key,
    configValue: value,
    version: version
  });
};

// 删除参数配置
export const delConfig = (id: string | number | Array<string | number>) => {
  return http.delete('/system/config/' + id);
};

// 刷新参数缓存
export const refreshCache = () => {
  return http.delete('/system/config/refreshCache');
};
