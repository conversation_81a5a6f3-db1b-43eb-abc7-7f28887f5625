package com.ruoyi.common.orm.listener;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpStatus;
import com.mybatisflex.annotation.InsertListener;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.orm.core.domain.BaseEntity;
import com.ruoyi.common.satoken.utlis.LoginHelper;

import java.time.LocalDateTime;


/**
 * Entity实体类全局插入数据监听器
 *
 * <AUTHOR>
 */
public class EntityInsertListener implements InsertListener {

    @Override
    public void onInsert(Object entity) {
        try {
            if (ObjectUtil.isNotNull(entity) && (entity instanceof BaseEntity baseEntity)) {

                String loginUserId = LoginHelper.getUserId();

                LocalDateTime createTime = ObjectUtil.isNotNull(baseEntity.getCreateTime())
                    ? baseEntity.getCreateTime() : LocalDateTime.now();

                baseEntity.setCreateBy(loginUserId);
                baseEntity.setCreateTime(createTime);
                baseEntity.setUpdateBy(loginUserId);
                baseEntity.setUpdateTime(createTime);
            }
        } catch (Exception e) {
            throw new ServiceException("全局插入数据监听器注入异常 => " + e.getMessage(), HttpStatus.HTTP_UNAUTHORIZED);
        }
    }
}
