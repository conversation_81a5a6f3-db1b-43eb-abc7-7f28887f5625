package com.ruoyi.system.domain.vo.sysmenu;

import java.util.Date;
import lombok.Data;
import java.io.Serial;
import java.io.Serializable;

/**
 * 菜单权限视图对象 sys_menu
 *
 * <AUTHOR>
 * @since 2025-04-16 14:13:43
 */
@Data
public class SysMenuDetailVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 菜单ID */
    private String id;
    /** 菜单名称 */
    private String menuName;
    /** 父菜单ID */
    private String parentId;
    /** 显示顺序 */
    private Integer orderNum;
    /** 路由地址 */
    private String path;
    /** 组件路径 */
    private String component;
    /** 路由参数 */
    private String queryParam;
    /** 是否为外链（0是 1否） */
    private String isFrame;
    /** 是否缓存（0缓存 1不缓存） */
    private String isCache;
    /** 菜单类型（M目录 C菜单 F按钮） */
    private String menuType;
    /** 显示状态（0显示 1隐藏） */
    private String visible;
    /** 菜单状态（0正常 1停用） */
    private String status;
    /** 权限标识 */
    private String perms;
    /** 菜单图标 */
    private String icon;
    /** 乐观锁 */
    private Integer version;
    /** 备注 */
    private String remark;

}
