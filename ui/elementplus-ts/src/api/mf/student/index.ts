import http from '@/utils/http';
import type { StudentVO, StudentForm, StudentQuery } from '@/api/mf/student/types';
import type { PageData } from '@/utils/http/interface';

/**
 * 查询学生信息单列表
 * @param query
 * @returns {*}
 */
export const listStudent = (query?: StudentQuery) => {
  return http.get<PageData<StudentVO>>('/mf/student/list', query);
};

/**
 * 查询学生信息单详细
 * @param id
 */
export const getStudent = (id: string | number) => {
  return http.get<StudentVO>('/mf/student/' + id);
};

/**
 * 新增学生信息单
 * @param data
 */
export const addStudent = (data: StudentForm) => {
  return http.post('/mf/student', data);
};

/**
 * 修改学生信息单
 * @param data
 */
export const editStudent = (data: StudentForm) => {
  return http.put('/mf/student', data);
};

/**
 * 删除学生信息单
 * @param id
 */
export const removeStudent = (id: string | number | Array<string | number>) => {
  return http.delete('/mf/student/' + id);
};
