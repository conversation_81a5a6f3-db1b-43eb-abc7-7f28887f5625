package com.ruoyi.safetyoutline.domain.bo.syslevel3protectconfig;

import com.ruoyi.common.core.core.domain.bo.BaseListBo;
import com.ruoyi.safetyoutline.domain.po.SysLevel3protectConfig;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 三级等保配置信息业务对象 sys_level3protect_config
 *
 * <AUTHOR>
 * @date 2025-01-13 15:56:35
 */
@Data
@AutoMapper(target = SysLevel3protectConfig.class, reverseConvertGenerate = false)
public class SysLevel3protectConfigListBo extends BaseListBo {

    /**
     * 配置双因子登录模式(0关闭1开启)
     */
    @NotNull(message = "配置双因子登录模式(0关闭1开启)不能为空")
    private String pzsyzdlmsbz;

    /**
     * 最大连续登录失败次数
     */
    @NotNull(message = "最大连续登录失败次数不能为空")
    private Integer zdlxdlsbcs;

    /**
     * 连续登录失败锁定分钟
     */
    @NotNull(message = "连续登录失败锁定分钟不能为空")
    private Integer lxdlsbsdsj;

    /**
     * 登录后无操作自动退出的分钟
     */
    @NotNull(message = "登录后无操作自动退出的分钟不能为空")
    private Integer dlwczzdtcsj;

    /**
     * 开启密码复杂度(0关闭1开启)
     */
    @NotNull(message = "开启密码复杂度(0关闭1开启)不能为空")
    private String kqmmfzdbz;

    /**
     * 定期修改密码时间间隔
     */
    @NotNull(message = "定期修改密码时间间隔不能为空")
    private Integer dqxgmmsjjg;

    /**
     * 定期修改密码不允许重复次数
     */
    @NotNull(message = "定期修改密码不允许重复次数不能为空")
    private Integer dqxgmmbxxcfcs;

    /**
     * 文件安全检测(0关闭1开启)
     */
    @NotNull(message = "文件安全检测(0关闭1开启)不能为空")
    private String wjaqjcbz;

    /**
     * 上传文件大小限制
     */
    @NotNull(message = "上传文件大小限制不能为空")
    private Integer scwjdxxz;


}
