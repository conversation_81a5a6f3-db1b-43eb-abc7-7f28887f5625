<script lang="ts" setup>
import { Page } from '@vben/common-ui';

import Config from './config/index.vue';
import Parent from './parent/index.vue';
import { useDeptService } from './service';

defineOptions({
  name: 'DeptIndex',
});

const {
  id,
  Form,
  BasicTable,
  FormDialog,
  handleStatusChange,
  useToolbarButtons,
  useRowButtons,
} = useDeptService();
</script>

<template>
  <Page auto-content-height>
    <FormDialog class="w-[780px]" title="机构管理">
      <Form />
      <Parent :dept-id="id" />
      <Config :dept-id="id" />
    </FormDialog>

    <BasicTable>
      <template #toolbar-actions>
        <ActionButtonGroup :buttons="useToolbarButtons()" />
      </template>

      <template #action="{ row }">
        <ActionButtonGroup :buttons="useRowButtons()" :row="row" />
      </template>

      <template #status="{ row }">
        <ElSwitch
          v-model="row.syztdm"
          inline-prompt
          active-text="正常"
          inactive-text="停用"
          active-value="10"
          inactive-value="20"
          style="--el-switch-off-color: #ff4949"
          @change="handleStatusChange(row)"
        />
      </template>
    </BasicTable>
  </Page>
</template>
