import type { ComponentInternalInstance as ComponentInstance } from 'vue';
import type { LanguageEnum } from '@/enums/LanguageEnum';
import type { download } from '@/plugins/download';
import type { useDict } from '@/utils/dict';
import type { getConfigKey, updateConfigByKey } from '@/api/system/config';
import type { addDateRange, handleTree, parseTime, selectDictLabel, selectDictLabels } from '@/utils/ruoyi';
import type { alert, alertError, alertWarning, skConfirm } from '@/utils/message';
import type animate from '@/animate';
import type tab from '@/plugins/tab';
import type cache from '@/plugins/cache';
import type auth from '@/plugins/auth';
import type { download as dl } from '@/utils/http/com';
import type { endLoading, startLoading } from '@/components/Loading/fullScreen';

declare global {
  /** vue Instance */
  type ComponentInternalInstance = ComponentInstance;

  type Recordable<T = any, K = string> = Record<K extends null | undefined ? string : K, T>;

  type ComponentRef<T> = InstanceType<T>;

  declare type ElementPlusInfoType = 'success' | 'info' | 'warning' | 'danger';

  /**
   * 通过window挂载常用全局变量
   */
  interface Window {
    skynet?: {
      tab: typeof tab;
      cache: typeof cache;
      download: typeof dl;
      downloadOss: typeof download.oss;
      downloadZip: typeof download.zip;
      auth: typeof auth;
      useDict: typeof useDict;
      getConfigKey: typeof getConfigKey;
      updateConfigByKey: typeof updateConfigByKey;
      parseTime: typeof parseTime;
      handleTree: typeof handleTree;
      addDateRange: typeof addDateRange;
      selectDictLabel: typeof selectDictLabel;
      selectDictLabels: typeof selectDictLabels;
      animate: typeof animate;
      alert: typeof alert;
      alertWarning: typeof alertWarning;
      alertError: typeof alertError;
      confirm: typeof skConfirm;
      loading: typeof startLoading;
      closeLoading: typeof endLoading;
    };
  }

  /**
   * 界面字段隐藏属性
   */
  interface FieldOption {
    key: number;
    label: string;
    visible: boolean;
    children?: Array<FieldOption>;
  }

  /**
   * 弹窗属性
   */
  interface DialogOption {
    /**
     * 弹窗标题
     */
    title?: string;
    /**
     * 是否显示
     */
    visible: boolean;
  }

  interface UploadOption {
    /** 设置上传的请求头部 */
    headers: { [key: string]: any };

    /** 上传的地址 */
    url: string;
  }

  /**
   * 导入属性
   */
  interface ImportOption extends UploadOption {
    /** 是否显示弹出层 */
    open: boolean;
    /** 弹出层标题 */
    title: string;
    /** 是否禁用上传 */
    isUploading: boolean;

    updateSupport: number;

    /** 其他参数 */
    [key: string]: any;
  }

  /**
   * 选项基本数据结构
   */
  interface BaseDataOption {
    id?: string;
    label: string;
    value: string;
    isLeaf?: boolean;
  }

  /**
   * 字典数据  数据配置
   */
  type DictDataOption = BaseDataOption;

  interface BaseEntity {
    /** 乐观锁 */
    version?: number;
    createBy?: any;
    createDept?: any;
    createTime?: string;
    updateBy?: any;
    updateTime?: any;
  }

  /**
   * 分页数据
   * T : 表单数据
   * D : 查询参数
   */
  interface PageData<T, D> {
    form: T;
    queryParams: D;
    rules: ElFormRules;
  }

  /**
   * 分页查询参数
   */
  interface PageQuery {
    pageNum: number;
    pageSize: number;
  }

  interface LayoutSetting {
    /**
     * 是否显示顶部导航
     */
    topNav: boolean;

    /**
     * 是否显示多标签导航
     */
    tagsView: boolean;
    /**
     * 是否固定头部
     */
    fixedHeader: boolean;
    /**
     * 是否显示侧边栏Logo
     */
    sidebarLogo: boolean;
    /**
     * 是否显示动态标题
     */
    dynamicTitle: boolean;
    /**
     * 侧边栏主题 theme-dark | theme-light
     */
    sideTheme: string;
    /**
     * 主题模式
     */
    theme: string;
  }

  interface DefaultSettings extends LayoutSetting {
    /**
     * 网页标题
     */
    title: string;

    /**
     * 是否显示系统布局设置
     */
    showSettings: boolean;

    /**
     * 导航栏布局
     */
    layout: string;

    /**
     * 布局大小
     */
    size: 'large' | 'default' | 'small';

    /**
     * 语言
     */
    language: LanguageEnum;

    /**
     * 是否启用动画效果
     */
    animationEnable: boolean;
    /**
     *  是否启用暗黑模式
     *
     * true:暗黑模式
     * false: 明亮模式
     */
    dark: boolean;

    errorLog: string;
  }

  /**
   * 六级行政区划数据结构
   */
  interface XzqhData {
    /**
     * 省级代码
     */
    sjdm?: string;
    /**
     * 省级名称
     */
    sjmc?: string;
    /**
     * 州市代码
     */
    zsdm?: string;
    /**
     * 州市名称
     */
    zsmc?: string;
    /**
     * 区县代码
     */
    qxdm?: string;
    /**
     * 区县名称
     */
    qxmc?: string;
    /**
     *乡镇街道代码
     */
    xzjddm?: string;
    /**
     *乡镇街道名称
     */
    xzjdmc?: string;
    /**
     *居委会代码
     */
    jwhdm?: string;
    /**
     *居委会名称
     */
    jwhmc?: string;
    /**
     *街路巷代码
     */
    jlxdm?: string;
    /**
     *街路巷名称
     */
    jlxmc?: string;
  }
}
export {};
