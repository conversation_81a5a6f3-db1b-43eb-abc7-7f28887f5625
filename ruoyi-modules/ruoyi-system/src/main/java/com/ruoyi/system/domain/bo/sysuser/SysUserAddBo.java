package com.ruoyi.system.domain.bo.sysuser;

import com.ruoyi.system.domain.po.SysUser;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import jakarta.validation.constraints.*;

import java.io.Serial;
import java.io.Serializable;

/**
 * 用户信息业务对象 sys_user
 *
 * <AUTHOR>
 * @since 2025-04-29 14:00:02
 */
@Data
@AutoMapper(target = SysUser.class, reverseConvertGenerate = false)
public class SysUserAddBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 部门ID
     */
    @NotBlank(message = "部门ID不能为空")
    private String deptId;

    /**
     * 用户账号
     */
    @NotBlank(message = "用户账号不能为空")
    private String userName;

    /**
     * 用户昵称
     */
    @NotBlank(message = "用户昵称不能为空")
    private String nickName;

    /**
     * 用户类型（sys_user系统用户）
     */
    @NotBlank(message = "用户类型（sys_user系统用户）不能为空")
    private String userType;

    /**
     * 用户邮箱
     */
    @NotBlank(message = "用户邮箱不能为空")
    private String email;

    /**
     * 手机号码
     */
    @NotBlank(message = "手机号码不能为空")
    private String phonenumber;

    /**
     * 用户性别（0男 1女 2未知）
     */
    @NotBlank(message = "用户性别（0男 1女 2未知）不能为空")
    private String gender;

    /**
     * 头像地址
     */
    private Long avatar;

    /**
     * 密码
     */
    @NotBlank(message = "密码不能为空")
    private String password;

    /**
     * 帐号状态（0正常 1停用）
     */
    @NotBlank(message = "帐号状态（0正常 1停用）不能为空")
    private String status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建者
     */
    private String createBy;

}
