import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { VbenFormSchema } from '#/adapter/form';

import { FolderIcon, MenuIcon, OkButtonIcon } from '@vben/icons';

import { VbenIcon } from '@vben-core/shadcn-ui';

import { z } from '#/adapter/form';
import { listMenuOption } from '#/api/system/menu';
import { renderDictTag } from '#/utils/dict';

// 菜单类型（M目录 C菜单 F按钮）
export const menuTypeOptions = [
  { label: '目录', value: 'M' },
  { label: '菜单', value: 'C' },
  { label: '按钮', value: 'F' },
];

export const yesNoOptions = [
  { label: '是', value: '0' },
  { label: '否', value: '1' },
];

/**
 * 获取编辑表单的字段配置
 * @returns VbenFormSchema[] 表单配置
 */
export function useFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      dependencies: {
        show: () => false,
        triggerFields: [''],
      },
      fieldName: 'id',
      label: '菜单ID',
    },
    {
      component: 'Input',
      dependencies: {
        show: () => false,
        triggerFields: [''],
      },
      fieldName: 'version',
      label: '乐观锁',
    },
    {
      component: 'ApiTreeSelect',
      fieldName: 'parentId',
      label: '父菜单ID',
      defaultValue: '0',
      rules: 'selectRequired',
      componentProps: {
        placeholder: '请选择父结点ID',
        checkStrictly: true,
        clearable: true,
        api: async () => {
          const res = await listMenuOption({ isTree: true });
          const options: DataOptionTree[] = [];
          const data: DataOptionTree = {
            id: '0',
            value: '0',
            label: '根目录',
            isLeaf: false,
            children: [],
          };
          data.children = res;
          options.push(data);
          return options;
        },
        childrenField: 'children',
        labelField: 'label',
        valueField: 'id',
      },
    },
    {
      component: 'SkDict',
      rules: 'selectRequired',
      componentProps: {
        type: 'radio-group-button',
        data: menuTypeOptions,
        placeholder: '请选择菜单类型',
        clearable: true,
      },
      defaultValue: 'M',
      dependencies: {
        componentProps: (_, api) => {
          // 切换时清空校验
          // 直接抄的源码 没有清空校验的方法
          Object.keys(api.errors.value).forEach((key) => {
            api.setFieldError(key, undefined);
          });
          return {};
        },
        triggerFields: ['menuType'],
      },
      fieldName: 'menuType',
      label: '菜单类型',
    },
    {
      component: 'IconPicker',
      rules: 'required',
      dependencies: {
        // 类型不为按钮时显示
        show: (values) => values.menuType !== 'F',
        triggerFields: ['menuType'],
      },
      fieldName: 'icon',
      label: '菜单图标',
    },
    {
      component: 'Input',
      fieldName: 'menuName',
      label: '菜单名称',
      rules: 'required',
      help: '支持i18n写法, 如: menu.system.user',
      componentProps: {
        placeholder: '请输入菜单名称',
        clearable: true,
      },
    },
    {
      component: 'InputNumber',
      fieldName: 'orderNum',
      label: '显示顺序',
      rules: 'required',
      help: '排序, 数字越小越靠前',
      componentProps: {
        placeholder: '请输入显示顺序',
        clearable: true,
        style: {
          width: '100%',
        },
      },
    },
    {
      component: 'Input',
      componentProps: (model) => {
        const placeholder =
          model.isFrame === '0'
            ? '填写链接地址http(s)://  使用新页面打开'
            : '填写`路由地址`或者`链接地址`  链接默认使用内部iframe内嵌打开';
        return {
          placeholder,
        };
      },
      dependencies: {
        rules: (model) => {
          if (model.isFrame !== '0') {
            return z
              .string({ message: '请输入路由地址' })
              .refine((val) => !val.startsWith('/'), {
                message: '路由地址不需要带/',
              });
          }
          // 为链接
          return z
            .string({ message: '请输入链接地址' })
            .regex(/^https?:\/\//, { message: '请输入正确的链接地址' });
        },
        // 类型不为按钮时显示
        show: (values) => values?.menuType !== 'F',
        triggerFields: ['isFrame', 'menuType'],
      },
      fieldName: 'path',
      help: `路由地址不带/, 如: menu, user\n 链接为http(s)://开头\n 链接默认使用内部iframe打开, 可通过{是否外链}控制打开方式`,
      label: '路由地址',
    },
    {
      component: 'Input',
      componentProps: (model) => {
        return {
          // 为链接时组件disabled
          disabled: model.isFrame === '0',
        };
      },
      defaultValue: '',
      dependencies: {
        rules: (model) => {
          // 非链接时为必填项
          if (model.path && !/^https?:\/\//.test(model.path)) {
            return z
              .string()
              .min(1, { message: '非链接时必填组件路径' })
              .refine((val) => !val.startsWith('/') && !val.endsWith('/'), {
                message: '组件路径开头/末尾不需要带/',
              });
          }
          // 为链接时非必填
          return z.string().optional();
        },
        // 类型为菜单时显示
        show: (values) => values.menuType === 'C',
        triggerFields: ['menuType', 'path'],
      },
      fieldName: 'component',
      help: '填写./src/views下的组件路径, 如system/menu/index',
      label: '组件路径',
    },
    {
      component: 'Input',
      dependencies: {
        // 类型为菜单/按钮时显示
        show: (values) => values.menuType !== 'M',
        triggerFields: ['menuType'],
      },
      fieldName: 'perms',
      help: `控制器中定义的权限字符\n 如: @SaCheckPermission("system:user:import")`,
      label: '权限标识',
    },
    {
      component: 'Input',
      componentProps: (model) => ({
        // 为链接时组件disabled
        disabled: model.isFrame === '0',
        placeholder: '必须为json字符串格式',
      }),
      dependencies: {
        // 类型为菜单时显示
        show: (values) => values.menuType === 'C',
        triggerFields: ['menuType'],
      },
      fieldName: 'queryParam',
      help: 'vue-router中的query属性\n 如{"name": "xxx", "age": 16}',
      label: '路由参数',
    },
    {
      component: 'SkDict',
      componentProps: {
        type: 'radio-group-button',
        data: yesNoOptions,
        placeholder: '请选择是否外链',
        clearable: true,
      },
      defaultValue: '1',
      dependencies: {
        // 类型不为按钮时显示
        show: (values) => values.menuType !== 'F',
        triggerFields: ['menuType'],
      },
      fieldName: 'isFrame',
      help: '外链为http(s)://开头\n 选择否时, 使用iframe从内部打开页面, 否则新窗口打开',
      label: '是否外链',
    },
    {
      component: 'SkDict',
      componentProps: {
        type: 'radio-group-button',
        data: 'cdzt',
        placeholder: '请选择是否显示',
        clearable: true,
      },
      defaultValue: '0',
      dependencies: {
        // 类型不为按钮时显示
        show: (values) => values.menuType !== 'F',
        triggerFields: ['menuType'],
      },
      fieldName: 'visible',
      help: '隐藏后不会出现在菜单栏, 但仍然可以访问',
      label: '是否显示',
    },
    {
      component: 'SkDict',
      componentProps: {
        type: 'radio-group-button',
        data: 'xtkg',
        placeholder: '请选择菜单状态',
        clearable: true,
      },
      defaultValue: '0',
      dependencies: {
        // 类型不为按钮时显示
        show: (values) => values.menuType !== 'F',
        triggerFields: ['menuType'],
      },
      fieldName: 'status',
      help: '停用后不会出现在菜单栏, 也无法访问',
      label: '菜单状态',
    },
    {
      component: 'RadioGroup',
      componentProps: {
        type: 'radio-group-button',
        data: yesNoOptions,
        placeholder: '请选择是否缓存',
        clearable: true,
      },
      defaultValue: '0',
      dependencies: {
        // 类型为菜单时显示
        show: (values) => values.menuType === 'C',
        triggerFields: ['menuType'],
      },
      fieldName: 'isCache',
      help: '路由的keepAlive属性',
      label: '是否缓存',
    },
  ];
}

/**
 * 获取查询表单的字段配置
 * @returns VbenFormSchema[] 查询表单配置
 */
export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'menuName',
      label: '菜单名称',
      componentProps: {
        placeholder: '请输入菜单名称',
        clearable: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'menuType',
      label: '菜单类型',
      componentProps: {
        placeholder: '请输入菜单类型',
        clearable: true,
      },
    },
  ];
}

// （M目录 C菜单 F按钮）
const menuTypes = {
  C: { icon: MenuIcon, value: '菜单' },
  F: { icon: OkButtonIcon, value: '按钮' },
  M: { icon: FolderIcon, value: '目录' },
};

/**
 * 获取表格列配置
 * @returns VxeTableGridOptions['columns'] 表格列配置
 * @description 使用函数的形式返回列数据而不是直接export一个Array常量，是为了响应语言切换时重新翻译表头
 */
export function useGridColumns(): VxeTableGridOptions['columns'] {
  return [
    {
      title: '#',
      type: 'seq',
      width: 60,
      fixed: 'left',
    },
    {
      field: 'menuName',
      title: '菜单名称',
      fixed: 'left',
      align: 'left',
      treeNode: true,
    },
    {
      title: '菜单图标',
      field: 'icon',
      width: 80,
      slots: {
        default: ({ row }) => {
          if (row?.icon === '#') {
            return '';
          }
          return (
            <span class={'flex justify-center'}>
              <VbenIcon icon={row.icon} />
            </span>
          );
        },
      },
    },
    {
      field: 'orderNum',
      title: '显示顺序',
      width: 140,
      minWidth: 200,
      align: 'center',
    },
    {
      title: '菜单类型',
      field: 'menuType',
      width: 150,
      slots: {
        default: ({ row }) => {
          const current = menuTypes[row.menuType as 'C' | 'F' | 'M'];
          if (!current) {
            return '未知';
          }
          return (
            <span class="flex items-center justify-center gap-1">
              {h(current.icon, { class: 'size-[18px]' })}
              <span>{current.value}</span>
            </span>
          );
        },
      },
    },
    {
      field: 'visible',
      title: '显示状态',
      width: 140,
      minWidth: 200,
      align: 'center',
      slots: {
        default: ({ row }) => {
          return renderDictTag(row.visible, 'cdzt');
        },
      },
    },
    {
      title: '操作',
      field: 'action',
      fixed: 'right',
      slots: { default: 'action' },
      width: 240,
    },
  ];
}
