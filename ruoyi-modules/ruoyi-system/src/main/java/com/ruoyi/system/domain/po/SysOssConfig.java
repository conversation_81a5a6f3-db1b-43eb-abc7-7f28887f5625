package com.ruoyi.system.domain.po;

import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import com.ruoyi.common.orm.core.domain.BaseEntity;

/**
 * 对象存储配置对象 sys_oss_config
 *
 * <AUTHOR>
 * @since 2025-05-08 21:55:46
 */
@Data
@Table(value = "sys_oss_config")
public class SysOssConfig extends BaseEntity {

    /** 主建 */
    @Id
    private String id;

    /** 配置名称 */
    private String key;

    /** accessKey */
    private String accessKey;

    /** secretKey */
    private String secretKey;

    /** 桶名称 */
    private String bucketName;

    /** 前缀 */
    private String prefix;

    /** 访问站点 */
    private String endpoint;

    /** 自定义域名 */
    private String domain;

    /** 是否https（Y=是,N=否） */
    private String isHttps;

    /** 域 */
    private String region;

    /** 桶权限类型（0=private 1=public 2=custom） */
    private String accessPolicy;

    /** 是否默认（0=是,1=否） */
    private String status;

    /** 扩展字段 */
    private String ext1;

    /** 备注 */
    private String remark;


}
