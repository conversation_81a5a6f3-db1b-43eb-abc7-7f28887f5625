--- # 数据源配置
spring:
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      # 最大连接池数量
      maximum-pool-size: 20
      # 最小空闲线程数量
      minimum-idle: 10
      # 配置获取连接等待超时的时间
      connectionTimeout: 30000
      # 校验超时时间
      validationTimeout: 5000
      # 空闲连接存活最大时间，默认10分钟
      idleTimeout: 600000
      # 此属性控制池中连接的最长生命周期，值0表示无限生命周期，默认30分钟
      maxLifetime: 1800000
      # 多久检查一次连接的活性
      keepaliveTime: 30000
mybatis-flex:
  # sql审计
  audit_enable: true
  # sql打印
  sql_print: true
  datasource:
    # 数据源-1
    ds1:
      # jdbc 所有参数配置参考 https://lionli.blog.csdn.net/article/details/122018562
      # rewriteBatchedStatements=true 批处理优化 大幅提升批量插入更新删除性能(对数据库有性能损耗 使用批量操作应考虑性能问题)
      type: ${spring.datasource.type}
      # mysql数据库
      #      driver-class-name: com.mysql.cj.jdbc.Driver
      #      url: *********************************************************************************************************************************************************************************************************************************
      #      username: root
      #      password: Root@369
      #postgresql数据库
      driver-class-name: org.postgresql.Driver
      url: *****************************************************************************************************************************************
      username: postgres
      password: 123456

# redis 单机配置(单机与集群只能开启一个另一个需要注释掉)
spring.data:
  redis:
    # 地址
    host: ************
    # 端口，默认为6379
    port: 6379
    # 数据库索引
    database: 0
    # 密码(如没有密码请注释掉)
    password: 123456
    # 连接超时时间
    timeout: 10s
    # 是否开启ssl
    ssl.enabled: false

redisson:
  # redis key前缀
  keyPrefix:
  # 线程池数量
  threads: 4
  # Netty线程池数量
  nettyThreads: 8
  # 单节点配置
  singleServerConfig:
    # 客户端名称
    clientName: ${ruoyi.name}
    # 最小空闲连接数
    connectionMinimumIdleSize: 8
    # 连接池大小
    connectionPoolSize: 32
    # 连接空闲超时，单位：毫秒
    idleConnectionTimeout: 10000
    # 命令等待超时，单位：毫秒
    timeout: 3000
    # 发布和订阅连接池大小
    subscriptionConnectionPoolSize: 50

--- # 监控中心客户端配置
spring.boot.admin.client:
  # 增加客户端开关
  enabled: false
  url: http://localhost:9090/admin
  instance:
    service-host-type: IP
  username: ruoyi
  password: 123456
--- # powerjob 配置
powerjob:
  worker:
    # 如何开启调度中心请查看文档教程
    enabled: false
    # 需要先在 powerjob 登录页执行应用注册后才能使用
    app-name: ruoyi-worker
    # 28080 端口 随着主应用端口飘逸 避免集群冲突
    port: 2${server.port}
    protocol: http
    server-address: 127.0.0.1:7700
    store-strategy: disk
    allow-lazy-connect-server: false
    max-appended-wf-context-length: 4096
    max-result-length: 4096
--- # mail 邮件发送
mail:
  enabled: false
  host: smtp.163.com
  port: 465
  # 是否需要用户名密码验证
  auth: true
  # 发送方，遵循RFC-822标准
  from: <EMAIL>
  # 用户名（注意：如果使用foxmail邮箱，此处user为qq号）
  user: <EMAIL>
  # 密码（注意，某些邮箱需要为SMTP服务单独设置密码，详情查看相关帮助）
  pass: xxxxxxxxxx
  # 使用 STARTTLS安全连接，STARTTLS是对纯文本通信协议的扩展。
  starttlsEnable: true
  # 使用SSL安全连接
  sslEnable: true
  # SMTP超时时长，单位毫秒，缺省值不超时
  timeout: 0
  # Socket连接超时值，单位毫秒，缺省值不超时
  connectionTimeout: 0
--- # sms 短信 支持 阿里云 腾讯云 云片 等等各式各样的短信服务商
# https://sms4j.com/doc3/ 差异配置文档地址 支持单厂商多配置，可以配置多个同时使用
sms:
  # 配置源类型用于标定配置来源(interface,yaml)
  config-type: yaml
  # 用于标定yml中的配置是否开启短信拦截，接口配置不受此限制
  restricted: true
  # 短信拦截限制单手机号每分钟最大发送，只对开启了拦截的配置有效
  minute-max: 1
  # 短信拦截限制单手机号每日最大发送量，只对开启了拦截的配置有效
  account-max: 30
  # 以下配置来自于 org.dromara.sms4j.provider.config.BaseConfig类中
  blends:
    # 唯一ID 用于发送短信寻找具体配置 随便定义别用中文即可
    # 可以同时存在两个相同厂商 例如: ali1 ali2 两个不同的阿里短信账号 也可用于区分租户
    config1:
      # 框架定义的厂商名称标识，标定此配置是哪个厂商，详细请看厂商标识介绍部分
      supplier: alibaba
      # 有些称为accessKey有些称之为apiKey，也有称为sdkKey或者appId。
      access-key-id: 您的accessKey
      # 称为accessSecret有些称之为apiSecret
      access-key-secret: 您的accessKeySecret
      signature: 您的短信签名
      sdk-app-id: 您的sdkAppId
    config2:
      # 厂商标识，标定此配置是哪个厂商，详细请看厂商标识介绍部分
      supplier: tencent
      access-key-id: 您的accessKey
      access-key-secret: 您的accessKeySecret
      signature: 您的短信签名
      sdk-app-id: 您的sdkAppId
# security配置
security:
  # 排除路径
  excludes:
    # 静态资源
    - /*.html
    - /**/*.html
    - /**/*.css
    - /**/*.js
    - /profile/**
    # 公共路径
    - /favicon.ico
    - /error
    # swagger 文档配置
    - /*/api-docs
    - /*/api-docs/**
    # actuator 监控配置
    - /actuator
    - /actuator/**
    # 其它链接
    - /login
    - /register
    - /captchaImage
    - /captcha/get
    - /captcha/check
    - /device/log/gettest
    - /auth/login
    - /warm-flow-ui/**
    - /warm-flow/**
--- # 三方授权
justauth:
  # 前端外网访问地址
  address: http://localhost:80
  type:
    maxkey:
      # maxkey 服务器地址
      # 注意 如下均配置均不需要修改 maxkey 已经内置好了数据
      server-url: http://sso.maxkey.top
      client-id: 876892492581044224
      client-secret: x1Y5MTMwNzIwMjMxNTM4NDc3Mzche8
      redirect-uri: ${justauth.address}/social-callback?source=maxkey
    topiam:
      # topiam 服务器地址
      server-url: http://127.0.0.1:1989/api/v1/authorize/y0q************spq***********8ol
      client-id: 449c4*********937************759
      client-secret: ac7***********1e0************28d
      redirect-uri: ${justauth.address}/social-callback?source=topiam
      scopes: [openid, email, phone, profile]
    qq:
      client-id: 10**********6
      client-secret: 1f7d08**********5b7**********29e
      redirect-uri: ${justauth.address}/social-callback?source=qq
      union-id: false
    weibo:
      client-id: 10**********6
      client-secret: 1f7d08**********5b7**********29e
      redirect-uri: ${justauth.address}/social-callback?source=weibo
    gitee:
      client-id: 91436b7940090**********d67eea73acbf61b6b590751a98
      client-secret: 02c6fcfd70342980cd8**********c754d7a264c4e125f9ba915ac
      redirect-uri: ${justauth.address}/social-callback?source=gitee
    dingtalk:
      client-id: 10**********6
      client-secret: 1f7d08**********5b7**********29e
      redirect-uri: ${justauth.address}/social-callback?source=dingtalk
    baidu:
      client-id: 10**********6
      client-secret: 1f7d08**********5b7**********29e
      redirect-uri: ${justauth.address}/social-callback?source=baidu
    csdn:
      client-id: 10**********6
      client-secret: 1f7d08**********5b7**********29e
      redirect-uri: ${justauth.address}/social-callback?source=csdn
    coding:
      client-id: 10**********6
      client-secret: 1f7d08**********5b7**********29e
      redirect-uri: ${justauth.address}/social-callback?source=coding
      coding-group-name: xx
    oschina:
      client-id: 10**********6
      client-secret: 1f7d08**********5b7**********29e
      redirect-uri: ${justauth.address}/social-callback?source=oschina
    alipay_wallet:
      client-id: 10**********6
      client-secret: 1f7d08**********5b7**********29e
      redirect-uri: ${justauth.address}/social-callback?source=alipay_wallet
      alipay-public-key: MIIB**************DAQAB
    wechat_open:
      client-id: 10**********6
      client-secret: 1f7d08**********5b7**********29e
      redirect-uri: ${justauth.address}/social-callback?source=wechat_open
    wechat_mp:
      client-id: 10**********6
      client-secret: 1f7d08**********5b7**********29e
      redirect-uri: ${justauth.address}/social-callback?source=wechat_mp
    wechat_enterprise:
      client-id: 10**********6
      client-secret: 1f7d08**********5b7**********29e
      redirect-uri: ${justauth.address}/social-callback?source=wechat_enterprise
      agent-id: 1000002
    gitlab:
      client-id: 10**********6
      client-secret: 1f7d08**********5b7**********29e
      redirect-uri: ${justauth.address}/social-callback?source=gitlab
# warm-flow工作流配置
warm-flow:
  # 是否开启工作流，默认true
  enabled: true
  # 是否显示banner图，默认是
  banner: true
  # id生成器类型, 不填默认为orm扩展自带生成器或者warm-flow内置的19位雪花算法, SnowId14:14位，SnowId15:15位， SnowFlake19：19位
  key_type: SnowId19
  # 是否开启设计器ui，默认true
  ui: true
  ## 如果需要工作流共享业务系统权限，默认Authorization，如果有多个token，用逗号分隔
  token-name: Authorization
#  # 填充器，内部有默认实现，如果不满足实际业务，可通过此配置自定义实现
#  data-fill-handler-path: com.ruoyi.system.handle.CustomDataFillHandler
#  # 全局租户处理器，有多租户需要，可以配置自定义实现
#  tenant_handler_path: com.ruoyi.system.handle.CustomTenantHandler
#  # 是否开启逻辑删除（orm框架本身不支持逻辑删除，可通过这种方式开启，比如jpa）
#  logic_delete: true
#  # 逻辑删除字段值（开启后默认为2）
#  logic_delete_value: 2
#  # 逻辑未删除字段（开启后默认为0）
#  logic_not_delete_value: 0
#  # 当使用JPA时指定JpaPersistenceProvider
#  jpa_persistence_provider: org.springframework.orm.jpa.vendor.SpringHibernateJpaPersistenceProvider
