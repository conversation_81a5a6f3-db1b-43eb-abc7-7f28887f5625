<script setup lang="ts">
import { StudentForm } from '@/api/mf/student/types';

defineOptions({
  name: 'StudentDialog'
});
//定义属性并指定默认值
const contentProps = defineProps<{
  model?: StudentForm;
}>();
const newFormInline = ref(contentProps.model);
</script>
<template>
  <div>
    <el-row>
      <el-col :span="12">
        <el-form-item label="学生名称" prop="studentName">
          <el-input v-model="newFormInline.studentName" clearable placeholder="请输入学生名称" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="年龄" prop="studentAge">
          <el-input v-model="newFormInline.studentAge" clearable placeholder="请输入年龄" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="爱好" prop="studentHobby">
          <el-input v-model="newFormInline.studentHobby" clearable placeholder="请输入爱好" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="性别" prop="studentGender">
          <SkDict v-model="newFormInline.studentGender" data="xb" placeholder="请输入性别"></SkDict>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="状态" prop="studentStatus">
          <SkDict v-model="newFormInline.studentStatus" data="xtkg" placeholder="请输入状态"></SkDict>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="生日" prop="studentBirthday">
          <el-date-picker
            v-model="newFormInline.studentBirthday"
            type="date"
            value-format="YYYY-MM-DD"
            clearable
            placeholder="请选择生日"
          />
        </el-form-item>
      </el-col>
    </el-row>
  </div>
</template>
<style lang="scss" scoped>
:deep(.el-form-item__content) {
  margin-left: 0 !important;
}

:deep(.el-table .el-form-item--large) {
  margin-bottom: 0 !important;
}
</style>
