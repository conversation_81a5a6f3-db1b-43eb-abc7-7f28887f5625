/**
 * 验证器相关的常量定义
 */

/**
 * 身份证验证错误信息
 */
export const ID_CARD_ERROR_MESSAGES = {
  EMPTY: '身份证号码不能为空',
  LENGTH: '身份证号码必须为18位',
  FORMAT: '身份证号码格式不正确',
  AREA_CODE: '身份证号码地区代码无效',
  BIRTH_DATE: '身份证号码出生日期无效',
  CHECKSUM: '身份证号码校验码错误',
  FULL_WIDTH: '不能包含全角字符，请使用半角字符',
} as const;

/**
 * 身份证地区代码映射
 */
export const ID_CARD_AREA_CODES = {
  // 华北地区
  11: '北京市',
  12: '天津市',
  13: '河北省',
  14: '山西省',
  15: '内蒙古自治区',
  
  // 东北地区
  21: '辽宁省',
  22: '吉林省',
  23: '黑龙江省',
  
  // 华东地区
  31: '上海市',
  32: '江苏省',
  33: '浙江省',
  34: '安徽省',
  35: '福建省',
  36: '江西省',
  37: '山东省',
  
  // 华中地区
  41: '河南省',
  42: '湖北省',
  43: '湖南省',
  
  // 华南地区
  44: '广东省',
  45: '广西壮族自治区',
  46: '海南省',
  
  // 西南地区
  50: '重庆市',
  51: '四川省',
  52: '贵州省',
  53: '云南省',
  54: '西藏自治区',
  
  // 西北地区
  61: '陕西省',
  62: '甘肃省',
  63: '青海省',
  64: '宁夏回族自治区',
  65: '新疆维吾尔自治区',
  
  // 特别行政区和其他
  71: '台湾省',
  81: '香港特别行政区',
  82: '澳门特别行政区',
  91: '国外',
} as const;

/**
 * 有效的地区代码列表
 */
export const VALID_AREA_CODES = Object.keys(ID_CARD_AREA_CODES).map(Number);

/**
 * 身份证校验码权重
 */
export const ID_CARD_WEIGHTS = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2] as const;

/**
 * 身份证校验码对应表
 */
export const ID_CARD_CHECK_CODES = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'] as const;

/**
 * 手机号验证相关常量
 */
export const PHONE_VALIDATION = {
  /** 手机号长度 */
  LENGTH: 11,
  /** 有效的手机号前缀 */
  VALID_PREFIXES: ['13', '14', '15', '16', '17', '18', '19'],
  /** 错误信息 */
  ERROR_MESSAGES: {
    LENGTH: '手机号必须为11位',
    FORMAT: '请输入正确的手机号',
    FULL_WIDTH: '不能包含全角字符，请使用半角字符',
  },
} as const;

/**
 * 邮箱验证相关常量
 */
export const EMAIL_VALIDATION = {
  /** 错误信息 */
  ERROR_MESSAGES: {
    FORMAT: '请输入正确的邮箱',
    FULL_WIDTH: '不能包含全角字符，请使用半角字符',
  },
} as const;
