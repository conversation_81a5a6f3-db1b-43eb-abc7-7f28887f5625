// 测试身份证号码验证功能
import { BaseSchema, ZodEnum } from './zod-enum';

console.log('=== 身份证号码验证测试 ===');

// 1. 测试有效的身份证号码
console.log('\n1. 有效身份证号码测试:');

const validIdCards = [
  '11010519491231002X', // 北京，1949年12月31日，校验码X
  '110105194912310029', // 北京，1949年12月31日，校验码9
  '320311198001010011', // 江苏徐州，1980年1月1日
  '******************', // 广东深圳，1990年1月1日
  '510104200001010012', // 四川成都，2000年1月1日
];

validIdCards.forEach((idCard, index) => {
  const result = BaseSchema.ID_CARD.safeParse(idCard);
  console.log(`✅ 有效身份证 ${index + 1}: ${idCard} - ${result.success ? '通过' : '失败'}`);
  if (!result.success) {
    console.log(`   错误: ${result.error.issues[0].message}`);
  }
});

// 2. 测试无效的身份证号码 - 长度错误
console.log('\n2. 长度错误测试:');

const lengthErrorCases = [
  '1101051949123100', // 16位
  '110105194912310', // 15位
  '11010519491231002XX', // 19位
  '', // 空字符串
];

lengthErrorCases.forEach((idCard, index) => {
  const result = BaseSchema.ID_CARD.safeParse(idCard);
  console.log(`❌ 长度错误 ${index + 1}: "${idCard}" - ${result.success ? '意外通过' : result.error.issues[0].message}`);
});

// 3. 测试无效的身份证号码 - 格式错误
console.log('\n3. 格式错误测试:');

const formatErrorCases = [
  '11010519491231002A', // 最后一位不是数字或X
  '1101051949123100ZX', // 倒数第二位不是数字
  'ABCDEFGHIJKLMNOPQR', // 全字母
  '110105194912310@2X', // 包含特殊字符
];

formatErrorCases.forEach((idCard, index) => {
  const result = BaseSchema.ID_CARD.safeParse(idCard);
  console.log(`❌ 格式错误 ${index + 1}: ${idCard} - ${result.success ? '意外通过' : result.error.issues[0].message}`);
});

// 4. 测试无效的身份证号码 - 地区代码错误
console.log('\n4. 地区代码错误测试:');

const areaCodeErrorCases = [
  '00010519491231002X', // 地区代码00无效
  '99010519491231002X', // 地区代码99无效
  '01010519491231002X', // 地区代码01无效
  '10010519491231002X', // 地区代码10无效
];

areaCodeErrorCases.forEach((idCard, index) => {
  const result = BaseSchema.ID_CARD.safeParse(idCard);
  console.log(`❌ 地区代码错误 ${index + 1}: ${idCard} - ${result.success ? '意外通过' : result.error.issues[0].message}`);
});

// 5. 测试无效的身份证号码 - 出生日期错误
console.log('\n5. 出生日期错误测试:');

const birthDateErrorCases = [
  '11010518991231002X', // 年份1899无效（太早）
  '11010520251231002X', // 年份2025无效（未来）
  '11010519491301002X', // 月份13无效
  '11010519490001002X', // 月份00无效
  '11010519491232002X', // 日期32无效
  '11010519491200002X', // 日期00无效
  '11010519490229002X', // 非闰年2月29日
];

birthDateErrorCases.forEach((idCard, index) => {
  const result = BaseSchema.ID_CARD.safeParse(idCard);
  console.log(`❌ 出生日期错误 ${index + 1}: ${idCard} - ${result.success ? '意外通过' : result.error.issues[0].message}`);
});

// 6. 测试无效的身份证号码 - 校验码错误
console.log('\n6. 校验码错误测试:');

const checksumErrorCases = [
  '11010519491231002Y', // 正确应该是X
  '110105194912310020', // 正确应该是9
  '320311198001010010', // 正确应该是1
  '******************', // 正确应该是8
  '510104200001010013', // 正确应该是2
];

checksumErrorCases.forEach((idCard, index) => {
  const result = BaseSchema.ID_CARD.safeParse(idCard);
  console.log(`❌ 校验码错误 ${index + 1}: ${idCard} - ${result.success ? '意外通过' : result.error.issues[0].message}`);
});

// 7. 测试全角字符
console.log('\n7. 全角字符测试:');

const fullWidthCases = [
  '１１０１０５１９４９１２３１００２Ｘ', // 全角数字和字母
  '11010519491231002Ｘ', // 部分全角
  '１１０１０５１９４９１２３１００２X', // 数字全角，字母半角
];

fullWidthCases.forEach((idCard, index) => {
  const result = BaseSchema.ID_CARD.safeParse(idCard);
  console.log(`❌ 全角字符 ${index + 1}: ${idCard} - ${result.success ? '意外通过' : result.error.issues[0].message}`);
});

// 8. 测试 ZodEnum
console.log('\n8. ZodEnum 测试:');

const zodEnumTest = ZodEnum.ID_CARD.safeParse('11010519491231002X');
console.log(`ZodEnum.ID_CARD: ${zodEnumTest.success ? '✅ 通过' : `❌ ${zodEnumTest.error.issues[0].message}`}`);

const zodEnumEmptyTest = ZodEnum.ID_CARD_EMPTY.safeParse('');
console.log(`ZodEnum.ID_CARD_EMPTY (空值): ${zodEnumEmptyTest.success ? '✅ 通过' : `❌ ${zodEnumEmptyTest.error.issues[0].message}`}`);

// 9. 边界情况测试
console.log('\n9. 边界情况测试:');

const boundaryTests = [
  { name: '闰年2月29日', idCard: '11010520000229001X' },
  { name: '平年2月28日', idCard: '11010519990228001X' },
  { name: '1月31日', idCard: '11010520000131001X' },
  { name: '12月31日', idCard: '11010520001231001X' },
  { name: '小写x', idCard: '11010519491231002x' },
];

boundaryTests.forEach(test => {
  const result = BaseSchema.ID_CARD.safeParse(test.idCard);
  console.log(`${result.success ? '✅' : '❌'} ${test.name}: ${test.idCard} - ${result.success ? '通过' : result.error.issues[0].message}`);
});

// 10. 性能测试
console.log('\n10. 性能测试:');

const performanceTestIdCard = '11010519491231002X';
const startTime = Date.now();
for (let i = 0; i < 1000; i++) {
  BaseSchema.ID_CARD.safeParse(performanceTestIdCard);
}
const endTime = Date.now();
console.log(`验证1000次身份证号码耗时: ${endTime - startTime}ms`);

// 11. 总结
console.log('\n=== 测试总结 ===');
console.log('✅ 身份证号码验证功能已实现，包括：');
console.log('   - 长度验证（18位）');
console.log('   - 格式验证（前17位数字，最后一位数字或X）');
console.log('   - 地区代码验证（有效的省市代码）');
console.log('   - 出生日期验证（年月日有效性）');
console.log('   - 校验码验证（ISO 7064:1983.MOD 11-2算法）');
console.log('   - 全角字符检测（优先级最高）');
console.log('   - 支持可选值（ID_CARD_EMPTY）');

export {
  validIdCards,
  lengthErrorCases,
  formatErrorCases,
  areaCodeErrorCases,
  birthDateErrorCases,
  checksumErrorCases,
  fullWidthCases
};
