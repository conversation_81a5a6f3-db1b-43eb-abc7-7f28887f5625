package com.ruoyi.system.domain.bo.sysdept;

import com.ruoyi.system.domain.po.SysDept;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 机构管理业务对象 sys_dept
 *
 * <AUTHOR>
 * @since 2025-05-29 16:59:45
 */
@Data
@AutoMapper(target = SysDept.class, reverseConvertGenerate = false)
public class SysDepStatusBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 部门ID
     */
    @NotBlank(message = "部门ID不能为空")
    private String id;
    /**
     * 乐观锁
     */
    private Integer version;

    /**
     * 使用状态
     */
    @NotBlank(message = "使用状态不能为空")
    private String syztdm;

}
