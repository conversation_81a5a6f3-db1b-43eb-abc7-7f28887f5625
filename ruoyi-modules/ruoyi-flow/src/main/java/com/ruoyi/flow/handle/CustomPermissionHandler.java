package com.ruoyi.flow.handle;

import com.ruoyi.common.core.core.domain.model.LoginUser;
import com.ruoyi.common.satoken.utlis.LoginHelper;
import com.ruoyi.system.domain.po.SysRole;
import org.dromara.warm.flow.core.handler.PermissionHandler;

import java.util.List;
import java.util.Set;

public class CustomPermissionHandler implements PermissionHandler {


    @Override
    public List<String> permissions() {
        LoginUser sysUser = LoginHelper.getLoginUser();
        Set<String> rolePermission = sysUser.getRolePermission();
        return rolePermission.stream().toList();
    }

    @Override
    public String getHandler() {
        LoginUser sysUser = LoginHelper.getLoginUser();
        if (sysUser != null) {
            return String.valueOf(sysUser.getUserId());
        }
        return null;
    }
}
