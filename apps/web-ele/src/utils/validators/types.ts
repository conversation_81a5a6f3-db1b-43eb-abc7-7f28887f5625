/**
 * 验证器相关的类型定义
 */

/**
 * 身份证验证结果
 */
export interface IdCardValidationResult {
  isValid: boolean;
  errorType?: 'areaCode' | 'birthDate' | 'checksum' | 'format' | 'length';
  errorMessage?: string;
}

/**
 * 身份证信息
 */
export interface IdCardInfo {
  /** 地区代码 */
  areaCode: string;
  /** 出生年份 */
  birthYear: number;
  /** 出生月份 */
  birthMonth: number;
  /** 出生日期 */
  birthDay: number;
  /** 顺序码 */
  sequenceCode: string;
  /** 校验码 */
  checkCode: string;
  /** 性别 */
  gender: 'female' | 'male';
}

/**
 * 验证错误类型
 */
export type ValidationErrorType =
  | 'areaCode'
  | 'birthDate'
  | 'checksum'
  | 'format'
  | 'fullWidth'
  | 'length';

/**
 * 通用验证器函数类型
 */
export type ValidatorFunction<T = string> = (value: T) => boolean;

/**
 * 带错误信息的验证器函数类型
 */
export type ValidatorWithErrorFunction<T = string> = (value: T) => {
  errorMessage?: string;
  isValid: boolean;
};
