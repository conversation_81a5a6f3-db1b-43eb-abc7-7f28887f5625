<script setup lang="ts">
// 导入 Element Plus 的相关类型
import type { SelectInstance } from 'element-plus';

import type { Props, XzqhData, XzqhDataOption } from '#/types/xzqh';

import { computed, ref } from 'vue';

import { ElTreeSelect } from 'element-plus';

import { useDzysStore } from '#/store/dzys';

// 设置组件默认配置
defineOptions({
  name: 'SkDzysTreeSelect',
  inheritAttrs: false,
});

// 提取 props 默认值
const props = withDefaults(defineProps<Props>(), {
  level: 5,
  multiple: false,
  options: () => [],
});

const emit = defineEmits<{
  (e: 'change', value: string | string[]): void;
}>();

const modelValue = defineModel<any | number[] | string | string[]>(
  'modelValue',
  {
    type: [String, Array, Number],
    default: '',
  },
);

/** 组件引用 */
const treeSelectRef = ref<SelectInstance>();
// 使用 Pinia store
const xzqhStore = useDzysStore();

// 树形选择器配置
const treeSelectProps = computed(() => ({
  value: 'id',
  label: props.level > 3 ? 'label' : 'fullName',
  children: 'children',
  disabled: 'disabled',
  isLeaf: 'isLeaf',
}));

// 处理选择变化
const handleChange = (value: any) => {
  const emitValue: any = value;
  modelValue.value = emitValue;
  emit('change', emitValue);
};

// 处理动态加载子节点
const handleLoadData = async (
  node: {
    data: XzqhDataOption;
    level: number;
  },
  resolve: (data: XzqhDataOption[]) => void,
) => {
  if (node.level >= props.level) {
    resolve([]);
    return;
  }

  try {
    const res = await xzqhStore.fetchDzysData({
      isTree: false,
      parentId: node.data.id,
      level: props.level,
    });
    resolve(res);
  } catch (error) {
    console.error('[tree-select.vue] 加载子节点数据失败:', error);
    resolve([]);
  }
};

/**
 * 获取选中数据的详细信息
 * @returns {XzqhData} 包含选中区划完整信息的对象
 */
const getSelectData = (): XzqhData => {
  return {};
};

// 过滤方法
const filterMethod = (keyword: string, node: any) => {
  if (!keyword) return true;
  const searchValue = keyword.toLowerCase();
  return (
    node.value.includes(keyword) ||
    node.label.toLowerCase().includes(searchValue)
  );
};

// 暴露组件方法和数据
defineExpose({
  element: treeSelectRef,
  xzqhData: computed(() => props.options),
  getSelectData,
  clear: () => {
    modelValue.value = '';
  },
  loading: computed(() => props.loading),
  handleLoadData,
});
</script>

<template>
  <div class="xzqh-tree-select">
    <ElTreeSelect
      ref="treeSelectRef"
      v-model="modelValue"
      v-bind="$attrs"
      :data="options"
      :props="treeSelectProps"
      :multiple="multiple"
      :size="props.size"
      :disabled="props.disabled"
      :clearable="props.clearable"
      :placeholder="placeholders?.[props.level - 1]"
      value-key="id"
      node-key="id"
      :lazy="props.level > 3"
      @change="handleChange"
      style="width: 100%"
      :show-all-levels="true"
      :load="handleLoadData"
      filterable
      :filter-node-method="filterMethod"
      :loading="loading"
    >
      <template #default="{ data }">
        <span>{{ data.label }}</span>
        <span> ({{ data.value }}) </span>
      </template>
    </ElTreeSelect>
  </div>
</template>

<style scoped>
.xzqh-tree-select {
  display: inline-block;
  width: 100%;
}
</style>
