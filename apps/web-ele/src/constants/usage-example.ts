// 使用示例：如何用 noFullWidthString 替换 BaseSchema 中的 z.string()

import { z } from '#/adapter/form';
import { noFullWidthString, optional } from './zod-enum';

// 原来的写法
const originalSchema = {
  STRING: z.string(),
  EMAIL: z.string().email('请输入正确的邮箱'),
  PHONE: z.string().regex(/^1[3-9]\d{9}$/, '请输入正确的手机号'),
};

// 使用 noFullWidthString 替换后的写法
const newSchema = {
  STRING: noFullWidthString(), // 替换 z.string()
  EMAIL: noFullWidthString().email('请输入正确的邮箱'), // 替换 z.string()
  PHONE: noFullWidthString().regex(/^1[3-9]\d{9}$/, '请输入正确的手机号'), // 替换 z.string()
};

// 自定义错误消息的写法
const customMessageSchema = {
  USERNAME: noFullWidthString('用户名不能包含中文字符'),
  PASSWORD: noFullWidthString('密码不能包含全角字符'),
};

// 与 optional 包装器组合使用
const optionalSchema = {
  DESCRIPTION: optional(noFullWidthString('描述不能包含全角字符')),
  REMARK: optional(noFullWidthString()),
};

// 使用示例
export const ExampleUsage = {
  // 基础用法
  basic: newSchema,
  
  // 自定义消息
  custom: customMessageSchema,
  
  // 可选字段
  optional: optionalSchema,
  
  // 完整的表单 schema 示例
  userForm: z.object({
    username: noFullWidthString('用户名不能包含中文字符'),
    email: noFullWidthString().email('请输入正确的邮箱'),
    phone: optional(noFullWidthString().regex(/^1[3-9]\d{9}$/, '请输入正确的手机号')),
    description: optional(noFullWidthString()),
  }),
};

// 验证示例
const testValidation = () => {
  const schema = noFullWidthString();
  
  // 这些会通过验证
  console.log('✅ 通过:', schema.safeParse('hello')); // 半角字符
  console.log('✅ 通过:', schema.safeParse('Hello123')); // 半角字符和数字
  console.log('✅ 通过:', schema.safeParse('')); // 空字符串
  console.log('✅ 通过:', schema.safeParse('<EMAIL>')); // 半角邮箱
  
  // 这些会失败
  console.log('❌ 失败:', schema.safeParse('你好')); // 中文字符
  console.log('❌ 失败:', schema.safeParse('hello世界')); // 混合字符
  console.log('❌ 失败:', schema.safeParse('　')); // 全角空格
  console.log('❌ 失败:', schema.safeParse('！')); // 全角感叹号
};

export { testValidation };
