package com.ruoyi.system.domain.po;

import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Table;
import com.ruoyi.common.orm.core.domain.BaseEntity;
import lombok.Data;

/**
 * 参数配置对象 sys_config
 *
 * <AUTHOR>
 * @since 2025-05-09 21:27:42
 */
@Data
@Table(value = "sys_config")
public class SysConfig extends BaseEntity {

    /** 参数主键 */
    @Id
    private String id;

    /** 参数名称 */
    private String name;

    /** 参数键名 */
    private String key;

    /** 参数键值 */
    private String value;

    /** 系统内置（Y是 N否） */
    private String type;

    /** 备注 */
    private String remark;


}
