import type { PostForm, PostQuery, PostVO } from './types';
import http from '@/utils/http';

// 查询岗位列表
export const listPost = (query: PostQuery) => {
  return http.get<PostVO[]>('/system/post/list', query);
};

// 查询岗位详细
export const getPost = (id: string | number) => {
  return http.get<PostVO>('/system/post/' + id);
};

// 新增岗位
export const addPost = (data: PostForm) => {
  return http.post('/system/post', data);
};

// 修改岗位
export const updatePost = (data: PostForm) => {
  return http.put('/system/post', data);
};

// 删除岗位
export const delPost = (id: string | number | (string | number)[]) => {
  return http.delete('/system/post/' + id);
};
