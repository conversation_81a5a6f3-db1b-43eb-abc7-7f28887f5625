package ${packageName}.domain.po;

#foreach ($import in $subImportList)
import ${import};
#end
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serial;
import java.io.Serializable;
#if($table.crud || $table.sub)
import com.ruoyi.common.orm.core.domain.BaseEntity;
#elseif($table.tree)
import com.ruoyi.common.orm.core.domain.TreeEntity;
#end

/**
 * ${subTable.functionName}对象 ${subTableName}
 *
 * <AUTHOR>
 * @since ${datetime}
 */
#if($table.crud || $table.sub)
    #set($Entity="BaseEntity")
#elseif($table.tree)
    #set($Entity="TreeEntity")
#end
@Data
@EqualsAndHashCode(callSuper = true)
@Table(value = "${subTableName}")
public class ${subClassName} extends ${Entity} {
    @Serial
    private static final long serialVersionUID = 1L;

#foreach ($column in $subTable.columns)
#if(!$subTable.isSuperColumn($column.javaField))
    /** $column.columnComment */
    #if($column.javaField=='jlzt')
    @Column(isLogicDelete = true)
    #end
    #if($column.javaField=='version')
    @Column(version = true)
    #end
    #if($column.isPk==1)
    @Id
    #end
    #if($column.javaType == 'LocalDateTime')
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" )
    #end
    private $column.javaType $column.javaField;

#end
#end

}
