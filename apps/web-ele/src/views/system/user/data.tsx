import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { VbenFormSchema } from '#/adapter/form';
import type { DeptDataOption } from '#/api/system/dept/types';

import { addFullName } from '@vben/utils';

import { z } from '#/adapter/form';
import { listDeptOptionTree } from '#/api/system/dept';
import { ZodEnum } from '#/constants/zod-enum';
import { renderDictTag } from '#/utils/dict';

/**
 * 获取编辑表单的字段配置
 * @returns VbenFormSchema[] 表单配置
 */
export function useFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      dependencies: {
        show: () => false,
        triggerFields: [''],
      },
      fieldName: 'id',
      label: '用户ID',
    },
    {
      component: 'Input',
      dependencies: {
        show: () => false,
        triggerFields: [''],
      },
      fieldName: 'version',
      label: '乐观锁',
    },
    {
      component: 'Input',
      fieldName: 'userName',
      label: '用户账号',
      rules: 'required',
      disabled: false,
      componentProps: {
        placeholder: '请输入用户账号',
        clearable: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'password',
      label: '密码',
      componentProps: {
        minLength: 5,
        maxlength: 20,
        type: 'password',
      },
      dependencies: {
        if: (values) => !values?.id,
        triggerFields: ['id'],
      },
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'nickName',
      label: '用户昵称',
      rules: 'required',
      componentProps: {
        placeholder: '请输入用户昵称',
        clearable: true,
      },
    },
    {
      component: 'ApiTreeSelect',
      fieldName: 'deptId',
      label: '部门',
      rules: 'required',
      componentProps: {
        placeholder: '请选择部门',
        checkStrictly: true,
        api: async () => {
          const res = await listDeptOptionTree();
          // 选中后显示在输入框的值 即父节点 / 子节点
          addFullName(res, 'label', ' / ');
          return res;
        },
        props: {
          value: 'id',
          label: 'fullName',
          children: 'children',
        },
      },
      renderComponentContent() {
        return {
          default: ({ data }: { data: DeptDataOption }) => data.label,
        };
      },
    },
    {
      component: 'Input',
      fieldName: 'userType',
      label: '用户类型',
      rules: 'required',
      componentProps: {
        placeholder: '请输入用户类型',
        clearable: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'email',
      label: '用户邮箱',
      rules: ZodEnum.EMAIL.or(z.literal('')).optional(),
      componentProps: {
        placeholder: '请输入用户邮箱',
        clearable: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'phonenumber',
      label: '手机号码',
      componentProps: {
        placeholder: '请输入手机号码',
        clearable: true,
      },
      rules: z
        .string()
        .regex(/^1[3-9]\d{9}$/, '请输入正确的手机号码')
        .optional()
        .or(z.literal('')),
    },
    {
      component: 'SkDict',
      fieldName: 'gender',
      label: '用户性别',
      rules: 'required',
      componentProps: {
        data: 'xb',
        placeholder: '请选择用户性别',
        type: 'radio-group-button',
        clearable: true,
      },
    },
    {
      component: 'SkDict',
      fieldName: 'status',
      label: '帐号状态',
      rules: 'required',
      defaultValue: '0',
      componentProps: {
        data: 'xtkg',
        type: 'radio-group-button',
        placeholder: '请选择帐号状态',
        clearable: true,
      },
    },
    {
      component: 'SkDict',
      fieldName: 'postIds',
      label: '岗位',
      rules: 'selectRequired',
      componentProps: {
        placeholder: '请选择岗位',
        multiple: true,
        clearable: true,
      },
    },
    {
      component: 'SkDict',
      fieldName: 'roleIds',
      label: '角色',
      rules: 'selectRequired',
      componentProps: {
        placeholder: '请选择角色',
        multiple: true,
        clearable: true,
      },
    },
    {
      component: 'Textarea',
      fieldName: 'remark',
      label: '备注',
      componentProps: {
        placeholder: '请输入备注',
        clearable: true,
      },
    },
  ];
}

/**
 * 获取查询表单的字段配置
 * @returns VbenFormSchema[] 查询表单配置
 */
export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'userName',
      label: '用户账号',
      componentProps: {
        placeholder: '请输入用户账号',
        clearable: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'phonenumber',
      label: '手机号码',
      componentProps: {
        placeholder: '请输入手机号码',
        clearable: true,
      },
    },
    {
      component: 'SkDict',
      fieldName: 'gender',
      label: '用户性别',
      componentProps: {
        data: 'xb',
        placeholder: '请选择用户性别',
        clearable: true,
      },
    },
    {
      component: 'SkDict',
      fieldName: 'status',
      label: '帐号状态',
      componentProps: {
        data: 'xtkg',
        placeholder: '请选择帐号状态',
        clearable: true,
      },
    },
    {
      component: 'DatePicker',
      fieldName: 'createTime',
      label: '创建时间',
      componentProps: {
        type: 'daterange',
        valueFormat: 'YYYY-MM-DD HH:mm:ss',
        rangeSeparator: '-',
        startPlaceholder: '开始日期',
        endPlaceholder: '结束日期',
        clearable: true,
      },
    },
  ];
}

/**
 * 获取表格列配置
 * @returns VxeTableGridOptions['columns'] 表格列配置
 * @description 使用函数的形式返回列数据而不是直接export一个Array常量，是为了响应语言切换时重新翻译表头
 */
export function useGridColumns(): VxeTableGridOptions['columns'] {
  return [
    {
      title: '#',
      type: 'seq',
      width: 60,
      fixed: 'left',
    },
    {
      field: 'userName',
      title: '用户账号',
      align: 'center',
    },
    {
      field: 'nickName',
      title: '用户昵称',
      align: 'center',
    },
    {
      field: 'userType',
      title: '用户类型',
      align: 'center',
    },
    {
      field: 'deptName',
      title: '部门',
      minWidth: 120,
    },
    {
      field: 'phonenumber',
      title: '手机号码',
      align: 'center',
    },
    {
      field: 'gender',
      title: '用户性别',
      align: 'center',
      slots: {
        default: ({ row }) => {
          return renderDictTag(row.gender, 'xb');
        },
      },
    },
    {
      field: 'status',
      title: '帐号状态',
      align: 'center',
      slots: { default: 'status' },
      minWidth: 80,
    },
    {
      title: '操作',
      field: 'action',
      fixed: 'right',
      slots: { default: 'action' },
      width: 240,
    },
  ];
}
