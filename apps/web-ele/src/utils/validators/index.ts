/**
 * 验证器模块统一导出
 */

// 中文姓名验证器
export { isChinese, validateChineseNameWithDetails } from './chinese-name-rule';

// 身份证验证器
export {
  generateIdCardChecksum,
  parseIdCardInfo,
  validateIdCard,
  validateIdCardAreaCode,
  validateIdCardBasicFormat,
  validateIdCardBirthDate,
  validateIdCardChecksum,
} from './id-card-validator';

// Schema 验证器（核心验证逻辑）
export {
  BaseSchema,
  ValidationSchemas,
  withNoFullWidth,
  optional,
} from './schemas';

// 类型定义
export type {
  ValidationSchemaType,
  ValidationSchemaKey,
} from './schemas';
