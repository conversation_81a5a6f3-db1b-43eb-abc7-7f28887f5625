<script setup lang="ts">
import type { Props, XzqhDataOption, XzqhInstance } from '#/types/xzqh';

import { computed, ref } from 'vue';

import { addFullName } from '@vben/utils';

import { useDzysStore } from '#/store/dzys';

import SkDzysCascader from './cascader.vue';
import SkDzysSelect from './select.vue';
import SkDzysTreeSelect from './tree-select.vue';

// 设置组件默认配置
defineOptions({
  name: 'SkDzys',
  inheritAttrs: false,
});

// 提取 props 默认值
const props = withDefaults(defineProps<Props>(), {
  type: 'cascader',
  level: 5,
  valueType: 'code',
  size: 'default',
  disabled: false,
  clearable: true,
  placeholders: () => [
    '请选择州市',
    '请选择区县',
    '请选择乡镇街道',
    '请选择村居委会',
    '请选择街路巷',
  ],
  multiple: false,
  virtual: false,
  enableCache: true,
  debounceTime: 300,
  loading: false,
});

const emit = defineEmits<{
  (e: 'change', value: string | string[]): void;
}>();

const modelValue = defineModel<any | number[] | string | string[]>(
  'modelValue',
  {
    type: [String, Array, Number],
    default: '',
  },
);

// 组件引用
const componentRef = ref<XzqhInstance>();
// 使用 Pinia store
const dzysStore = useDzysStore();

// 在 index.vue 中维护数据和状态的本地副本
const options = ref<XzqhDataOption[]>([]);
const loading = ref(false);

// 根据 type 选择对应的组件
const component = computed(() => {
  switch (props.type) {
    case 'cascader': {
      return SkDzysCascader;
    }
    case 'tree-select': {
      return SkDzysTreeSelect;
    }
    default: {
      return SkDzysSelect;
    }
  }
});

// 加载数据
const loadData = async (params: any, force = false) => {
  if (loading.value) return; // 防止重复加载

  loading.value = true;
  try {
    // 如果提供了数据源，直接使用
    if (props.options) {
      options.value = props.options;
    } else if (props.enableCache) {
      const res = await dzysStore.fetchDzysData(params, force);
      // 选中后显示在输入框的值 即父节点 / 子节点
      if (props.type === 'tree-select') {
        addFullName(res, 'label', ' / ');
      }
      options.value = res;
    }
    // 如果没有options且没有启用缓存，options保持为空
  } catch (error_) {
    console.error('[index.vue] 获取机构数据失败:', error_); // 保留错误日志
  } finally {
    loading.value = false;
  }
};

// 使用防抖函数包装数据加载
const debouncedLoadData = useDebounceFn(
  (force = false) => loadData({ isTree: true, level: props.level }, force),
  props.debounceTime,
);

// 监听数据源变化
watch(
  () => props.options,
  (newVal) => {
    if (newVal) {
      options.value = newVal;
    } else if (
      props.enableCache && // 当 options 变为 null/undefined 且启用缓存时，尝试加载数据
      // loadData 函数内部会检查缓存
      props.level < 4
    ) {
      debouncedLoadData();
    }
  },
  { immediate: true },
);

// 监听 level 变化
watch(
  () => props.level,
  (newLevel, oldLevel) => {
    if (newLevel !== oldLevel && !props.options && props.enableCache) {
      // 当 level 变化且没有提供 options 且启用缓存时，强制加载数据
      debouncedLoadData(true);
    }
  },
);

// 暴露组件方法
defineExpose({
  // 获取选中数据
  getSelectData: () => {
    return componentRef.value?.getSelectData();
  },
  // 获取原始数据
  // 直接从本地 options 状态获取
  xzqhData: computed(() => options.value),
  // 获取组件实例
  element: computed(() => componentRef.value?.element),
  // 清空选择
  clear: () => {
    modelValue.value = '';
    componentRef.value?.clear?.();
  },
  // 重新加载数据
  reload: async () => {},
  // 设置数据
  setData: (data: XzqhDataOption[]) => {
    // 直接更新本地 options 状态
    options.value = data;
    // 如果需要，可以在这里选择是否同步更新 store 的缓存
    // xzqhStore.setXzqhData(data); // 根据需求决定是否调用
  },
});
</script>

<template>
  <div class="sk-xzqh-wrapper">
    <component
      ref="componentRef"
      :is="component"
      v-bind="$attrs"
      v-model="modelValue"
      :level="level"
      :value-type="valueType"
      :size="size"
      :disabled="disabled"
      :clearable="clearable"
      :placeholders="placeholders"
      :multiple="multiple"
      :virtual="virtual"
      :options="options"
      :loading="loading"
      @change="emit('change', $event)"
    />
  </div>
</template>

<style scoped lang="scss">
.sk-xzqh-wrapper {
  display: inline-block;
  width: 100%;
}
</style>
