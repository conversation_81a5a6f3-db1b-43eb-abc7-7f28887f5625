import type { OssConfigForm, OssConfigQuery, OssConfigVO } from './types';
import http from '@/utils/http';
import type { PageData } from '@/utils/http/interface';

// 查询对象存储配置列表
export const listOssConfig = (query: OssConfigQuery) => {
  return http.get<PageData<OssConfigVO>>('/resource/oss/config/list', query);
};

// 查询对象存储配置详细
export const getOssConfig = (id: string | number) => {
  return http.get<OssConfigVO>('/resource/oss/config/' + id);
};

// 新增对象存储配置
export const addOssConfig = (data: OssConfigForm) => {
  return http.post('/resource/oss/config', data);
};

// 修改对象存储配置
export const updateOssConfig = (data: OssConfigForm) => {
  return http.put('/resource/oss/config', data);
};

// 删除对象存储配置
export const delOssConfig = (id: string | number | Array<string | number>) => {
  return http.delete('/resource/oss/config/' + id);
};

// 对象存储状态修改
export const changeOssConfigStatus = (id: string | number, version: number, status: string, configKey: string) => {
  const data = {
    id,
    version,
    status,
    configKey
  };
  return http.put('/resource/oss/config/changeStatus', data);
};
