import { z } from '#/adapter/form';

// 全角字符检测正则表达式
// 包括：全角空格(\u3000)、全角标点符号(\uFF00-\uFFEF)、中日韩字符(\u4E00-\u9FFF)、
// 平假名(\u3040-\u309F)、片假名(\u30A0-\u30FF)等
const FULL_WIDTH_REGEX =
  /[\u3000\uFF00-\uFFEF\u4E00-\u9FFF\u3040-\u309F\u30A0-\u30FF]/;

/**
 * 为任何 schema 添加全角字符验证的包装器
 * 使用 refine 方法确保全角字符验证优先级最高
 * @param schema 要包装的 schema
 * @param message 自定义错误消息
 * @returns 包装后的 schema
 */
export const withNoFullWidth = <T extends z.ZodTypeAny>(
  schema: T,
  message?: string,
) =>
  z
    .string()
    .refine((val) => {
      // 首先检查全角字符
      return !(val && val !== '' && FULL_WIDTH_REGEX.test(val));
    }, message || '不能包含全角字符，请使用半角字符')
    .refine( 
      (val) => {
        // 然后执行原始验证
        const result = schema.safeParse(val);
        return result.success;
      },
      (val: string) => {
        // 获取原始验证的错误信息
        const result = schema.safeParse(val);
        if (!result.success && result.error.issues.length > 0) {
          return result.error.issues[0].message;
        }
        return '验证失败';
      }
    );

// 基础验证规则（避免循环引用）
export const BaseSchema = {
  STRING: withNoFullWidth(z.string()),
  EMAIL: withNoFullWidth(z.string().email('请输入正确的邮箱')),
  PHONE: withNoFullWidth(
    z.string().regex(/^1[3-9]\d{9}$/, '请输入正确的手机号'),
  ),
  GAJGJGDM: withNoFullWidth(
    z
      .string()
      .length(12, '请输入12位数字')
      .regex(/^\d{12}$/, '请输入12位数字'),
  ),
};

// 可选值包装器（函数形式）
export const optional = <T extends z.ZodTypeAny>(schema: T) =>
  schema.optional().or(z.literal(''));

// 枚举式 Zod 模式集合
export const ZodEnum = {
  STRING: BaseSchema.STRING,
  EMAIL: BaseSchema.EMAIL,
  EMAIL_EMPTY: optional(BaseSchema.EMAIL),
  PHONE: BaseSchema.PHONE,
  PHONE_EMPTY: optional(BaseSchema.PHONE),
  /**
   *公安机关机构代码
   */
  GAJGJGDM: BaseSchema.GAJGJGDM,
  /**
   *公安机关机构代码，可为空
   */
  GAJGJGDM_EMPTY: optional(BaseSchema.GAJGJGDM),
} as const;

export type ZodEnumKey = keyof typeof ZodEnum;
