<script setup lang="ts">
import { PropType, ref } from 'vue';
import type { RouteLocationNormalizedLoaded } from 'vue-router';
import { ContextMenuSchema } from './types/index';
import { ElDropdown } from 'element-plus';

const emit = defineEmits(['visibleChange']);
const props = defineProps({
  schema: {
    type: Array as PropType<ContextMenuSchema[]>,
    default: () => []
  },
  trigger: {
    type: String as PropType<'click' | 'hover' | 'focus' | 'contextmenu'>,
    default: 'contextmenu'
  },
  tagItem: {
    type: Object as PropType<RouteLocationNormalizedLoaded>,
    default: () => ({})
  }
});

const command = (item: ContextMenuSchema) => {
  item.command && item.command(item);
};

const visibleChange = (visible: boolean) => {
  emit('visibleChange', visible, props.tagItem);
};

const elDropdownMenuRef = ref<ComponentRef<typeof ElDropdown>>();
defineExpose({
  elDropdownMenuRef,
  tagItem: props.tagItem
});
</script>

<template>
  <el-dropdown
    ref="elDropdownMenuRef"
    :trigger="trigger"
    placement="bottom-start"
    popper-class="v-context-menu-popper"
    @command="command"
    @visible-change="visibleChange"
  >
    <slot name="droBody"></slot>
    <template #dropdown>
      <el-dropdown-menu>
        <el-dropdown-item
          v-for="(item, index) in schema"
          :key="`dropdown${index}`"
          :divided="item.divided"
          :disabled="item.disabled"
          :command="item"
        >
          <component :is="item.icon" class="button-svg" />
          {{ item.label }}
        </el-dropdown-item>
      </el-dropdown-menu>
    </template>
  </el-dropdown>
</template>
<style lang="scss" scoped>
.button-svg {
  margin-right: 8px;
  width: 1em;
  height: 1em;
}
</style>
