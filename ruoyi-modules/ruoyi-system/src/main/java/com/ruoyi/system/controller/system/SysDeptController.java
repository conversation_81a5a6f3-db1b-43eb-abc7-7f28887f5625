package com.ruoyi.system.controller.system;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.lang.tree.Tree;
import com.ruoyi.common.core.core.domain.PageData;
import com.ruoyi.common.core.core.domain.Result;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.web.annotation.RepeatSubmit;
import com.ruoyi.common.web.core.BaseController;
import com.ruoyi.system.domain.bo.sysdept.*;
import com.ruoyi.system.domain.vo.sysdept.SysDeptAddVo;
import com.ruoyi.system.domain.vo.sysdept.SysDeptDetailVo;
import com.ruoyi.system.domain.vo.sysdept.SysDeptListVo;
import com.ruoyi.system.domain.vo.sysdept.SysDeptOptionVo;
import com.ruoyi.system.service.SysDeptService;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 机构管理控制器
 * <p>
 * 提供机构管理地增删改查、导入导出等功能，主要包含：
 * <ul>
 *   <li>机构管理地增删改查</li>
 *   <li>机构管理的分页查询</li>
 *   <li>机构管理的下拉选择</li>
 * </ul>
 *
 * <AUTHOR>
 * @since 2025-05-29 16:59:45
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/dept")
public class SysDeptController extends BaseController {
    @Resource
    private SysDeptService sysDeptService;

    /**
     * 分页查询机构管理列表
     * <p>
     * 支持多条件组合查询，返回分页数据
     *
     * @param sysDeptListBo 查询条件对象，包含分页参数和查询条件
     * @return 包含分页信息和机构管理列表的结果对象
     */
    @SaCheckPermission("system:dept:list")
    @GetMapping("/list")
    public Result<PageData<SysDeptListVo>> page(SysDeptListBo sysDeptListBo) {
        return sysDeptService.page(sysDeptListBo);
    }

    /**
     * 获取机构管理下拉选择列表
     * <p>
     * 用于前端下拉框等场景，支持模糊搜索
     *
     * @param optionBo 查询条件对象，包含搜索关键字等参数
     * @return 包含机构管理ID和显示名称的选项列表
     */
    @SaCheckLogin
    @GetMapping("/listOption")
    public Result<List<SysDeptOptionVo>> listOption(SysDeptOptionBo optionBo) {
        return sysDeptService.optionSelect(optionBo);
    }

    /**
     * 获取部门管理树形选择列表
     * <p>
     * 用于前端树形选择等场景，支持按层级展示
     *
     * @param optionBo 查询条件对象，包含搜索关键字等参数
     * @return 树形结构的部门管理列表
     */
    @SaCheckLogin
    @GetMapping("/listOptionTree")
    public Result<List<Tree<String>>> listOptionTree(SysDeptOptionBo optionBo) {
        return sysDeptService.optionTree(optionBo);
    }


    /**
     * 新增机构管理
     * <p>
     * 包含数据校验和重复提交控制，确保数据的完整性和一致性
     *
     * @param sysDeptAddBo 机构管理对象，包含必填字段验证
     * @return 操作结果，成功或失败信息
     */
    @SaCheckPermission("system:dept:add")
    @Log(title = "机构管理", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping
    public Result<SysDeptAddVo> add(@Validated @RequestBody SysDeptAddBo sysDeptAddBo) {
        return sysDeptService.add(sysDeptAddBo);
    }

    /**
     * 修改机构管理
     * <p>
     * 包含数据校验和重复提交控制，确保数据的完整性和一致性
     *
     * @param sysDeptEditBo 机构管理对象，包含必填字段验证
     * @return 操作结果，成功或失败信息
     */
    @SaCheckPermission("system:dept:edit")
    @Log(title = "机构管理", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping
    public Result<SysDeptAddVo> edit(@Validated @RequestBody SysDeptEditBo sysDeptEditBo) {
        return sysDeptService.edit(sysDeptEditBo);
    }

    /**
     * 删除机构管理
     * <p>
     * 支持批量删除操作，可同时删除多个机构管理
     *
     * @param ids 机构管理ID数组，包含要删除的ID列表
     * @return 操作结果，成功或失败信息
     */
    @SaCheckPermission("system:dept:remove")
    @Log(title = "机构管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result<Void> remove(@PathVariable String[] ids) {
        return sysDeptService.removeByIds(ids);
    }

    /**
     * 获取机构管理详细信息
     * <p>
     * 根据ID查询单个机构管理的完整信息，包含所有字段
     *
     * @param id 机构管理ID
     * @return 包含机构管理所有字段的详细信息对象
     */
    @SaCheckPermission("system:dept:query")
    @GetMapping(value = "/{id}")
    public Result<SysDeptDetailVo> detail(@PathVariable String id) {
        return Result.success(sysDeptService.detail(id));
    }

    /**
     * 状态修改
     */
    @SaCheckPermission("system:dept:edit")
    @Log(title = "更新使用状态", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public Result<Void> changeStatus(@Validated @RequestBody SysDepStatusBo bo) {
        return sysDeptService.changeStatus(bo);
    }
}
