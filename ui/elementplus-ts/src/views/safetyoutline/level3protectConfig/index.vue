<script setup name="Level3protectConfig" lang="ts">
import { listLevel3protectConfig } from '@/api/safetyoutline/level3protectConfig';
import { useLevel3protectConfig } from '@/views/safetyoutline/level3protectConfig/utils';

const { skTable, columns, openDialog, handleExport, handleDelete } = useLevel3protectConfig();
</script>
<template>
  <div class="table-box">
    <SkTable
      ref="skTable"
      row-key="${treeCode}"
      :columns="columns"
      :request-api="listLevel3protectConfig"
      :search-col="{ xs: 1, sm: 1, md: 2, lg: 3, xl: 3 }"
    >
      <!-- 表格 header 按钮 -->
      <template #tableHeader>
        <el-button
          v-hasPermi="['safetyoutline:level3protectConfig:add']"
          type="primary"
          icon="CirclePlus"
          @click="openDialog('新增')"
        >
          新增
        </el-button>
        <el-button
          v-hasPermi="['safetyoutline:level3protectConfig:export']"
          type="primary"
          plain
          icon="Download"
          @click="handleExport"
        >
          导出
        </el-button>
      </template>
      <!-- 表格操作 -->
      <template #operation="scope">
        <div>
          <el-button
            v-hasPermi="['safetyoutline:level3protectConfig:edit']"
            type="primary"
            link
            icon="EditPen"
            @click="openDialog('编辑', scope.row)"
          >
            编辑
          </el-button>
          <el-button
            v-hasPermi="['safetyoutline:level3protectConfig']"
            type="danger"
            link
            icon="Delete"
            @click="handleDelete(scope.row)"
          >
            删除
          </el-button>
        </div>
      </template>
    </SkTable>
  </div>
</template>
