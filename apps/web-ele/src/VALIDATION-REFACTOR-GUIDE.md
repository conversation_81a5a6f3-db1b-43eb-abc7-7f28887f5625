# 验证逻辑重构指南

## 🎯 重构目标

根据您的要求，已完成以下重构：
1. **简化代码结构** - 删除多余文件，统一验证逻辑
2. **分离关注点** - 验证逻辑在 `validators` 目录，前端调用在 `constants/zod-enum.ts`
3. **统一正则表达式** - 所有正则表达式集中在 `constants/regex-enum.ts`

## 📁 重构后的目录结构

```
apps/web-ele/src/
├── constants/
│   ├── regex-enum.ts           # ✅ 统一的正则表达式定义
│   └── zod-enum.ts             # ✅ 前端调用枚举（简化版）
├── utils/validators/
│   ├── chinese-name-rule.ts    # ✅ 中文姓名验证逻辑
│   ├── id-card-validator.ts    # ✅ 身份证验证逻辑
│   ├── schemas.ts              # 🆕 Schema 验证逻辑（从 zod-enum.ts 移入）
│   └── index.ts                # ✅ 验证器统一导出
```

## 🔄 重构内容

### **1. `constants/zod-enum.ts` - 简化为纯枚举**

**重构前**：包含复杂的验证逻辑、Schema 定义、包装器函数
```typescript
// 87行复杂的验证逻辑
export const withNoFullWidth = <T extends z.ZodTypeAny>(...) => { ... };
export const BaseSchema = { ... };
export const ZodEnum = { ... };
```

**重构后**：只作为前端调用的枚举入口
```typescript
// 17行简洁的枚举定义
import { BaseSchema, ValidationSchemas } from '#/utils/validators/schemas';

export const ZodEnum = ValidationSchemas;
export { BaseSchema };
```

### **2. `utils/validators/schemas.ts` - 新增验证逻辑文件**

将所有 Schema 验证逻辑从 `zod-enum.ts` 移动到此文件：
- `withNoFullWidth` 包装器函数
- `optional` 包装器函数
- `BaseSchema` 基础验证定义
- `ValidationSchemas` 完整验证枚举

### **3. `constants/regex-enum.ts` - 统一正则表达式**

添加了中文姓名相关的正则表达式：
```typescript
export const RegexEnum = {
  // 原有的正则...
  MOBILE: /^1[3-9]\d{9}$/,
  ID_CARD_BASIC: /^\d{17}[\dX]$/i,
  FULL_WIDTH_REGEX: /[\u3000\uFF00-\uFFEF...]/,
  
  // 🆕 新增的中文姓名正则
  CHINESE_NAME: /^[\u0391-\uFFE5]+(?:·[\u0391-\uFFE5]+)?$/,
  CHINESE_CHARS: /^[\u0391-\uFFE5]+$/,
  FORBIDDEN_CHARS: /^[^【】、﹒▪•]+$/,
} as const;
```

### **4. `utils/validators/chinese-name-rule.ts` - 使用统一正则**

更新为使用 `regex-enum.ts` 中的正则表达式：
```typescript
import { RegexEnum } from '#/constants/regex-enum';

const CHINESE_FORMAT_REGEX = RegexEnum.CHINESE_NAME;
const FORBIDDEN_CHARS_REGEX = RegexEnum.FORBIDDEN_CHARS;
```

## 🔧 使用方式

### **前端调用（保持不变）**
```typescript
import { ZodEnum, BaseSchema } from '#/constants/zod-enum';

// 使用方式完全不变
const emailResult = BaseSchema.EMAIL.safeParse('<EMAIL>');
const phoneResult = ZodEnum.PHONE_EMPTY.safeParse('');
const nameResult = ZodEnum.CHINESE_NAME.safeParse('张三');
```

### **验证器直接调用**
```typescript
import { isChinese, validateIdCard } from '#/utils/validators';

// 直接使用验证函数
const isValidName = isChinese('张三', false);
const idResult = validateIdCard('11010519491231002X');
```

### **Schema 验证逻辑**
```typescript
import { ValidationSchemas, withNoFullWidth } from '#/utils/validators/schemas';

// 使用完整的验证 Schema
const customSchema = withNoFullWidth(z.string().min(2));
```

## 📊 重构效果

### **代码简化**
- ✅ `zod-enum.ts`：从 121行 → 17行（减少 86%）
- ✅ 验证逻辑集中在 `validators` 目录
- ✅ 正则表达式统一在 `regex-enum.ts`

### **结构清晰**
- ✅ **关注点分离**：前端调用 vs 验证逻辑
- ✅ **职责单一**：每个文件有明确的职责
- ✅ **易于维护**：相关代码组织在一起

### **向后兼容**
- ✅ **API 不变**：所有现有的调用方式保持不变
- ✅ **功能完整**：所有验证功能正常工作
- ✅ **类型安全**：完整的 TypeScript 支持

## 🎯 文件职责说明

| 文件 | 职责 | 内容 |
|------|------|------|
| `constants/zod-enum.ts` | 前端调用枚举 | 只包含 `ZodEnum` 和 `BaseSchema` 的导出 |
| `constants/regex-enum.ts` | 正则表达式定义 | 所有验证用的正则表达式 |
| `utils/validators/schemas.ts` | Schema 验证逻辑 | `withNoFullWidth`、`BaseSchema` 定义等 |
| `utils/validators/chinese-name-rule.ts` | 中文姓名验证 | `isChinese` 函数实现 |
| `utils/validators/id-card-validator.ts` | 身份证验证 | 身份证相关验证函数 |
| `utils/validators/index.ts` | 验证器导出 | 统一导出所有验证器 |

## 🚀 后续建议

1. **删除冗余文件**：如果有其他重复的验证文件，可以删除
2. **统一导入路径**：建议统一使用 `#/constants/zod-enum` 进行前端调用
3. **扩展验证器**：新的验证逻辑添加到 `utils/validators` 目录
4. **正则表达式**：新的正则表达式添加到 `regex-enum.ts`

## ✅ 重构完成

现在的代码结构更加清晰：
- 📁 **constants** - 前端调用的枚举和常量
- 📁 **utils/validators** - 后端验证逻辑实现
- 🔗 **清晰的依赖关系** - constants 依赖 validators，不反向依赖

您现在可以继续使用原有的调用方式，同时享受更清晰的代码结构！
