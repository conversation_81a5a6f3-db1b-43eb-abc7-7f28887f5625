<script setup lang="ts">
//定义属性并指定默认值
import { GenTableVO } from '@/api/tool/gen/types';
import { listMenu } from '@/api/system/menu';
import { QuestionFilled } from '@element-plus/icons-vue';
import { getDictDataOption } from '@/utils/dict';

interface MenuOptionsType {
  id: number | string;
  menuName: string;
  children?: MenuOptionsType[];
}

//定义属性并指定默认值
const contentProps = defineProps<{
  model?: GenTableVO;
  rules?: any;
}>();
const processData = ref([]);
const activeName = ref('columnInfo');

const columns = contentProps.model.rows;
const info = ref(contentProps.model.info);
const tables = ref(contentProps.model.tables);
const subColumns = ref<any>([]);
const menuOptions = ref<Array<MenuOptionsType>>([]);

const subSelectChange = () => {
  info.value.subTableFkName = '';
};
const tplSelectChange = (value: string) => {
  if (value !== 'sub') {
    info.value.subTableName = '';
    info.value.subTableFkName = '';
  }
};
const setSubTableColumns = (value: string) => {
  tables.value.forEach((item: any) => {
    const name = item.tableName;
    if (value === name) {
      subColumns.value = item.columns;
      return;
    }
  });
};

/** 查询菜单下拉树结构 */
const getMenuTreeSelect = async () => {
  const res = await listMenu();
  const data = window.skynet.handleTree<MenuOptionsType>(res.data);
  if (data) menuOptions.value = data;
};

watch(
  () => info.value.subTableName,
  (val) => {
    setSubTableColumns(val);
  }
);

// 表单校验
onMounted(async () => {
  await getMenuTreeSelect();
  processData.value = await getDictDataOption({ isTree: true, sjlx: '1' });
  if (info.value.subTableName) {
    setSubTableColumns(info.value.subTableName);
  }
});
</script>

<template>
  <el-tabs v-model="activeName">
    <el-tab-pane label="基本信息" name="basic">
      <el-row>
        <el-col :span="12">
          <el-form-item label="表名称" prop="info.tableName">
            <el-input v-model="info.tableName" placeholder="请输入仓库名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="表描述" prop="info.tableComment">
            <el-input v-model="info.tableComment" placeholder="请输入" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="实体类名称" prop="info.className">
            <el-input v-model="info.className" placeholder="请输入" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="作者" prop="info.functionAuthor">
            <el-input v-model="info.functionAuthor" placeholder="请输入" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="备注" prop="info.remark">
            <el-input v-model="info.remark" type="textarea" :rows="3"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-tab-pane>
    <el-tab-pane label="字段信息" name="columnInfo" style="height: 600px">
      <SkTable ref="dragTable" :data="columns" row-key="id" :pagination="false" :toolButton="false">
        <el-table-column fixed label="序号" type="index" width="80" />
        <el-table-column fixed label="字段列名" prop="columnName" width="150" :show-overflow-tooltip="true" />
        <el-table-column label="字段描述" width="150">
          <template #default="scope">
            <el-form-item :prop="'rows.' + scope.$index + '.columnComment'" :rules="contentProps.rules.rows.columnComment">
              <el-input v-model="scope.row.columnComment" />
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column label="物理类型" prop="columnType" :show-overflow-tooltip="true" />
        <el-table-column label="Java类型">
          <template #default="scope">
            <el-select v-model="scope.row.javaType">
              <el-option label="Long" value="Long" />
              <el-option label="String" value="String" />
              <el-option label="Integer" value="Integer" />
              <el-option label="Double" value="Double" />
              <el-option label="BigDecimal" value="BigDecimal" />
              <el-option label="Date" value="Date" />
              <el-option label="Boolean" value="Boolean" />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="java属性">
          <template #default="scope">
            <el-input v-model="scope.row.javaField"></el-input>
          </template>
        </el-table-column>
        <el-table-column label="插入" width="80">
          <template #default="scope">
            <el-checkbox v-model="scope.row.isInsert" true-value="1" false-value="0"></el-checkbox>
          </template>
        </el-table-column>
        <el-table-column label="编辑" width="80">
          <template #default="scope">
            <el-checkbox v-model="scope.row.isEdit" true-value="1" false-value="0"></el-checkbox>
          </template>
        </el-table-column>
        <el-table-column label="列表" width="80">
          <template #default="scope">
            <el-checkbox v-model="scope.row.isList" true-value="1" false-value="0"></el-checkbox>
          </template>
        </el-table-column>
        <el-table-column label="查询" width="80">
          <template #default="scope">
            <el-checkbox v-model="scope.row.isQuery" true-value="1" false-value="0"></el-checkbox>
          </template>
        </el-table-column>
        <el-table-column label="查询方式">
          <template #default="scope">
            <el-select v-model="scope.row.queryType">
              <el-option label="=" value="EQ" />
              <el-option label="!=" value="NE" />
              <el-option label=">" value="GT" />
              <el-option label=">=" value="GE" />
              <el-option label="<" value="LT" />
              <el-option label="<=" value="LE" />
              <el-option label="LIKE" value="LIKE" />
              <el-option label="BETWEEN" value="BETWEEN" />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="必填" width="80">
          <template #default="scope">
            <el-checkbox v-model="scope.row.isRequired" true-value="1" false-value="0"></el-checkbox>
          </template>
        </el-table-column>
        <el-table-column label="显示类型">
          <template #default="scope">
            <el-select v-model="scope.row.htmlType">
              <el-option label="文本框" value="input" />
              <el-option label="文本域" value="textarea" />
              <el-option label="下拉框" value="select" />
              <el-option label="单选框" value="radio-group" />
              <el-option label="单选按钮框" value="radio-group-button" />
              <el-option label="复选框" value="checkbox" />
              <el-option label="日期控件" value="datetime" />
              <el-option label="图片上传" value="imageUpload" />
              <el-option label="文件上传" value="fileUpload" />
              <el-option label="富文本控件" value="editor" />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="字典类型">
          <template #default="scope">
            <sk-dict
              v-if="scope.row.htmlType == 'select' || scope.row.htmlType == 'radio-group' || scope.row.htmlType == 'checkbox'"
              v-model="scope.row.dictType"
              clearable
              filterable
              placeholder="请选择"
              :data="processData"
            ></sk-dict>
          </template>
        </el-table-column>
        <el-table-column label="最大长度" width="120">
          <template #default="scope">
            <el-form-item :prop="'rows.' + scope.$index + '.maxLength'">
              <el-input-number
                v-model="scope.row.maxLength"
                :disabled="scope.row.javaType !== 'String'"
                :placeholder="scope.row.javaType !== 'String' ? '非String类型' : '请输入最大长度'"
                :min="1"
                controls-position="right"
              />
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column label="是否唯一" width="80">
          <template #default="scope">
            <el-checkbox v-model="scope.row.unique"></el-checkbox>
          </template>
        </el-table-column>
        <el-table-column label="验证类型" width="120">
          <template #default="scope">
            <el-select v-model="scope.row.validateType" clearable placeholder="请选择验证类型">
              <el-option label="无验证" value="" />
              <el-option label="手机号" value="mobile" />
              <el-option label="邮箱" value="email" />
              <el-option label="身份证" value="idcard" />
              <el-option label="数字" value="number" />
              <el-option label="字母" value="letter" />
              <el-option label="字母数字" value="alphanumeric" />
              <el-option label="网址" value="url" />
              <el-option label="正整数" value="positiveInteger" />
            </el-select>
          </template>
        </el-table-column>
      </SkTable>
    </el-tab-pane>
    <el-tab-pane label="生成信息" name="genInfo">
      <el-row>
        <el-col :span="12">
          <el-form-item prop="info.tplCategory">
            <template #label>生成模板</template>
            <el-select v-model="info.tplCategory" @change="tplSelectChange">
              <el-option label="单表（增删改查）" value="crud" />
              <el-option label="主子表（增删改查）" value="sub" />
              <el-option label="树表（增删改查）" value="tree" />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item prop="info.packageName">
            <template #label>
              生成包路径
              <el-tooltip content="生成在哪个java包下，例如 com.ruoyi.system" placement="top">
                <el-icon>
                  <question-filled />
                </el-icon>
              </el-tooltip>
            </template>
            <el-input v-model="info.packageName" />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item prop="info.moduleName">
            <template #label>
              生成模块名
              <el-tooltip content="可理解为子系统名，例如 system" placement="top">
                <el-icon>
                  <question-filled />
                </el-icon>
              </el-tooltip>
            </template>
            <el-input v-model="info.moduleName" />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item prop="info.businessName">
            <template #label>
              生成业务名
              <el-tooltip content="可理解为功能英文名，例如 user" placement="top">
                <el-icon>
                  <question-filled />
                </el-icon>
              </el-tooltip>
            </template>
            <el-input v-model="info.businessName" />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item prop="info.functionName">
            <template #label>
              生成功能名
              <el-tooltip content="用作类描述，例如 用户" placement="top">
                <el-icon>
                  <question-filled />
                </el-icon>
              </el-tooltip>
            </template>
            <el-input v-model="info.functionName" />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item>
            <template #label>
              上级菜单
              <el-tooltip content="分配到指定菜单下，例如 系统管理" placement="top">
                <el-icon>
                  <question-filled />
                </el-icon>
              </el-tooltip>
            </template>
            <el-tree-select
              v-model="info.parentMenuId"
              :data="menuOptions"
              :props="{ value: 'id', label: 'menuName', children: 'children' }"
              value-key="id"
              node-key="id"
              placeholder="选择上级菜单"
              check-strictly
              filterable
              clearable
              highlight-current
            />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item prop="genType">
            <template #label>
              生成代码方式
              <el-tooltip content="默认为zip压缩包下载，也可以自定义生成路径" placement="top">
                <el-icon>
                  <question-filled />
                </el-icon>
              </el-tooltip>
            </template>
            <el-radio v-model="info.genType" value="0" label="zip压缩包"></el-radio>
            <el-radio v-model="info.genType" value="1" label="自定义路径"></el-radio>
          </el-form-item>
        </el-col>

        <el-col v-if="info.genType == '1'" :span="24">
          <el-form-item prop="genPath">
            <template #label>
              自定义路径
              <el-tooltip content="填写磁盘绝对路径，若不填写，则生成到当前Web项目下" placement="top">
                <el-icon>
                  <question-filled />
                </el-icon>
              </el-tooltip>
            </template>
            <el-input v-model="info.genPath">
              <template #append>
                <el-dropdown>
                  <el-button type="primary">
                    最近路径快速选择
                    <i class="el-icon-arrow-down el-icon--right"></i>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item @click="info.genPath = '/'">恢复默认的生成基础路径</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="number" prop="editColumns" class="label-right-align">
            <template #label>编辑页列数</template>
            <el-input-number
              v-model="info.editColumns"
              class="full-width-input"
              controls-position="right"
              :min="1"
              :max="4"
              :precision="0"
              :step="1"
            ></el-input-number>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-row>
            <el-col :span="6">
              <el-form-item label="Excel导入" prop="info.isImport">
                <template #label>
                  Excel导入
                  <el-tooltip content="开启后将生成Excel导入功能，包括导入控制器、服务方法和导入模板" placement="top">
                    <el-icon>
                      <question-filled />
                    </el-icon>
                  </el-tooltip>
                </template>
                <el-switch v-model="info.isImport" active-value="1" inactive-value="0"></el-switch>
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-form-item label="Excel导出" prop="info.isExport">
                <template #label>
                  Excel导出
                  <el-tooltip content="开启后将生成Excel导出功能，包括导出控制器和服务方法" placement="top">
                    <el-icon>
                      <question-filled />
                    </el-icon>
                  </el-tooltip>
                </template>
                <el-switch v-model="info.isExport" active-value="1" inactive-value="0"></el-switch>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="下拉选项" prop="info.isOption">
                <template #label>
                  下拉选项
                  <el-tooltip
                    content="开启后将生成下拉选择框的后端接口，包括控制器listOption方法和服务层optionSelect方法，树表类型将强制生成"
                    placement="top"
                  >
                    <el-icon>
                      <question-filled />
                    </el-icon>
                  </el-tooltip>
                </template>
                <el-switch v-model="info.isOption" active-value="1" inactive-value="0"></el-switch>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="弹窗类型" prop="info.dialogType">
                <template #label>
                  弹窗类型
                  <el-tooltip
                    content="选择表单弹窗的组件类型，Dialog是对话框，Drawer是抽屉式"
                    placement="top"
                  >
                    <el-icon>
                      <question-filled />
                    </el-icon>
                  </el-tooltip>
                </template>
                <el-radio-group v-model="info.dialogType">
                  <el-radio label="0">Dialog弹窗</el-radio>
                  <el-radio label="1">Drawer抽屉</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="组件类型" prop="info.componentType">
                <template #label>
                  组件类型
                  <el-tooltip
                    content="选择使用的组件库类型，Element Plus 或 Vben Admin"
                    placement="top"
                  >
                    <el-icon>
                      <question-filled />
                    </el-icon>
                  </el-tooltip>
                </template>
                <el-radio-group v-model="info.componentType">
                  <el-radio label="0">Element Plus</el-radio>
                  <el-radio label="1">Vben Admin</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
        </el-col>
      </el-row>

      <template v-if="info.tplCategory == 'tree'">
        <h4 class="form-header">其他信息</h4>
        <el-row v-show="info.tplCategory == 'tree'">
          <el-col :span="12">
            <el-form-item>
              <template #label>
                树编码字段
                <el-tooltip content="树显示的编码字段名， 如：dept_id" placement="top">
                  <el-icon>
                    <question-filled />
                  </el-icon>
                </el-tooltip>
              </template>
              <el-select v-model="info.treeCode" placeholder="请选择">
                <el-option
                  v-for="(column, index) in info.columns"
                  :key="index"
                  :label="column.columnName + '：' + column.columnComment"
                  :value="column.columnName"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item>
              <template #label>
                树父编码字段
                <el-tooltip content="树显示的父编码字段名， 如：parent_Id" placement="top">
                  <el-icon>
                    <question-filled />
                  </el-icon>
                </el-tooltip>
              </template>
              <el-select v-model="info.treeParentCode" placeholder="请选择">
                <el-option
                  v-for="(column, index) in info.columns"
                  :key="index"
                  :label="column.columnName + '：' + column.columnComment"
                  :value="column.columnName"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item>
              <template #label>
                树名称字段
                <el-tooltip content="树节点的显示名称字段名， 如：dept_name" placement="top">
                  <el-icon>
                    <question-filled />
                  </el-icon>
                </el-tooltip>
              </template>
              <el-select v-model="info.treeName" placeholder="请选择">
                <el-option
                  v-for="(column, index) in info.columns"
                  :key="index"
                  :label="column.columnName + '：' + column.columnComment"
                  :value="column.columnName"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </template>

      <template v-if="info.tplCategory == 'sub'">
        <h4 class="form-header">关联信息</h4>
        <el-row>
          <el-col :span="12">
            <el-form-item>
              <template #label>
                关联子表的表名
                <el-tooltip content="关联子表的表名， 如：sys_user" placement="top">
                  <el-icon>
                    <question-filled />
                  </el-icon>
                </el-tooltip>
              </template>
              <el-select v-model="info.subTableName" placeholder="请选择" @change="subSelectChange">
                <el-option
                  v-for="(t, index) in tables"
                  :key="index"
                  :label="t.tableName + '：' + t.tableComment"
                  :value="t.tableName"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item>
              <template #label>
                子表关联的外键名
                <el-tooltip content="子表关联的外键名， 如：user_id" placement="top">
                  <el-icon>
                    <question-filled />
                  </el-icon>
                </el-tooltip>
              </template>
              <el-select v-model="info.subTableFkName" placeholder="请选择">
                <el-option
                  v-for="(column, index) in subColumns"
                  :key="index"
                  :label="column.columnName + '：' + column.columnComment"
                  :value="column.columnName"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </template>
    </el-tab-pane>
  </el-tabs>
</template>

<style lang="scss" scoped>
:deep(.el-form-item__content) {
  margin-left: 0 !important;
}

:deep(.el-table .el-form-item--large) {
  margin-bottom: 0 !important;
}
</style>
