<script setup name="Sjy" lang="ts">
import { listSjy } from '@/api/system/sjy';
import { useSjy } from '@/views/system/sjy/utils';

const { skTable, columns, openDialog, handleExport, handleDelete } = useSjy();
</script>

<template>
  <div class="table-box">
    <SkTable
      ref="skTable"
      row-key="${treeCode}"
      label-width="120px"
      :columns="columns"
      :request-api="listSjy"
      :search-col="{ xs: 1, sm: 1, md: 2, lg: 3, xl: 3 }"
    >
      <!-- 表格 header 按钮 -->
      <template #tableHeader>
        <el-button v-hasPermi="['system:sjy:add']" type="primary" icon="CirclePlus" @click="openDialog('新增')"> 新增 </el-button>
        <el-button v-hasPermi="['system:sjy:export']" type="primary" plain icon="Download" @click="handleExport">
          导出
        </el-button>
      </template>
      <!-- 表格操作 -->
      <template #operation="scope">
        <div>
          <el-button v-hasPermi="['system:sjy:edit']" type="primary" link icon="EditPen" @click="openDialog('编辑', scope.row)">
            编辑
          </el-button>
          <el-button v-hasPermi="['system:sjy']" type="danger" link icon="Delete" @click="handleDelete(scope.row)">
            删除
          </el-button>
        </div>
      </template>
    </SkTable>
  </div>
</template>
