import { z } from '#/adapter/form';

// 全角字符检测正则表达式
// 包括：全角空格(\u3000)、全角标点符号(\uFF00-\uFFEF)、中日韩字符(\u4E00-\u9FFF)、
// 平假名(\u3040-\u309F)、片假名(\u30A0-\u30FF)等
const FULL_WIDTH_REGEX =
  /[\u3000\uFF00-\uFFEF\u4E00-\u9FFF\u3040-\u309F\u30A0-\u30FF]/;

/**
 * 创建一个支持链式调用的 ZodString，自动添加全角字符验证
 * 使用重写 safeParse 和 parse 方法的简单方式
 * @param message 自定义错误消息
 * @returns 支持链式调用的 ZodString
 */
export const noFullWidthString = (message?: string) => {
  const baseString = z.string();

  // 保存原始方法
  const originalSafeParse = baseString.safeParse.bind(baseString);
  baseString.parse.bind(baseString);
  // 重写 safeParse 方法
  baseString.safeParse = (input: unknown) => {
    const result = originalSafeParse(input);

    if (
      result.success &&
      result.data &&
      result.data !== '' &&
      FULL_WIDTH_REGEX.test(result.data)
    ) {
      return {
        success: false,
        error: new z.ZodError([
          {
            code: z.ZodIssueCode.custom,
            message: message || '不能包含全角字符，请使用半角字符',
            path: [],
          },
        ]),
      };
    }

    return result;
  };

  // 重写 parse 方法
  baseString.parse = (input: unknown) => {
    const result = originalSafeParse(input);

    if (!result.success) {
      throw result.error;
    }

    if (
      result.data &&
      result.data !== '' &&
      FULL_WIDTH_REGEX.test(result.data)
    ) {
      throw new z.ZodError([
        {
          code: z.ZodIssueCode.custom,
          message: message || '不能包含全角字符，请使用半角字符',
          path: [],
        },
      ]);
    }

    return result.data;
  };

  return baseString;
};
// 基础验证规则（避免循环引用）
export const BaseSchema = {
  STRING: noFullWidthString(),
  EMAIL: noFullWidthString().email('请输入正确的邮箱'),
  PHONE: noFullWidthString().regex(/^1[3-9]\d{9}$/, '请输入正确的手机号'),
  GAJGJGDM: noFullWidthString()
    .length(12)
    .regex(/^\d{12}$/, '请输入12位数字'),
};

// 可选值包装器（函数形式）
export const optional = <T extends z.ZodTypeAny>(schema: T) =>
  schema.optional().or(z.literal(''));

// 枚举式 Zod 模式集合
export const ZodEnum = {
  STRING: BaseSchema.STRING,
  EMAIL: BaseSchema.EMAIL,
  EMAIL_EMPTY: optional(BaseSchema.EMAIL),
  PHONE: BaseSchema.PHONE,
  PHONE_EMPTY: optional(BaseSchema.PHONE),
  /**
   *公安机关机构代码
   */
  GAJGJGDM: BaseSchema.GAJGJGDM,
  /**
   *公安机关机构代码，可为空
   */
  GAJGJGDM_EMPTY: optional(BaseSchema.GAJGJGDM),
} as const;

export type ZodEnumKey = keyof typeof ZodEnum;
