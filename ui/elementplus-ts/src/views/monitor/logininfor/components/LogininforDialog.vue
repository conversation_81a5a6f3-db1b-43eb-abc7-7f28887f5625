<script setup lang="ts" name="deptDialog">
// 接收父组件参数并设置默认值
import { OperLogVO } from '@/api/monitor/operlog/types';

//定义属性并指定默认值
const contentProps = defineProps<{
  formInline?: OperLogVO;
}>();
const newFormInline = ref(contentProps.formInline);
</script>
<template>
  <el-row>
    <el-col :span="24">
      <el-form-item label="登录信息："
        >{{ newFormInline.operName }} / {{ newFormInline.deptName }} / {{ newFormInline.operIp }} /
        {{ newFormInline.operLocation }}
      </el-form-item>
    </el-col>
    <el-col :span="12">
      <el-form-item label="请求信息：">{{ newFormInline.requestMethod }} {{ newFormInline.operUrl }}</el-form-item>
    </el-col>
    <el-col :span="24">
      <el-form-item label="操作方法：">{{ newFormInline.method }}</el-form-item>
    </el-col>
    <el-col :span="24">
      <el-form-item label="请求参数：">{{ newFormInline.operParam }}</el-form-item>
    </el-col>
    <el-col :span="24">
      <el-form-item label="返回参数：">{{ newFormInline.jsonResult }}</el-form-item>
    </el-col>
    <el-col :span="6">
      <el-form-item label="操作状态：">
        <div v-if="newFormInline.status === 0">正常</div>
        <div v-else-if="newFormInline.status === 1">失败</div>
      </el-form-item>
    </el-col>
    <el-col :span="8">
      <el-form-item label="消耗时间：">{{ newFormInline.costTime }}毫秒</el-form-item>
    </el-col>
    <el-col :span="10">
      <el-form-item label="操作时间：">{{ parseTime(newFormInline.operTime) }}</el-form-item>
    </el-col>
    <el-col :span="24">
      <el-form-item v-if="newFormInline.status === 1" label="异常信息：">{{ newFormInline.errorMsg }}</el-form-item>
    </el-col>
  </el-row>
</template>
