package com.ruoyi.system.controller.system;

import com.ruoyi.common.encrypt.annotation.ApiEncrypt;
import com.ruoyi.system.domain.bo.sysuser.SysUserListBo;
import com.ruoyi.system.domain.vo.sysuser.*;
import lombok.RequiredArgsConstructor;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.ruoyi.common.core.core.domain.Result;
import com.ruoyi.common.core.core.domain.PageData;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.web.annotation.RepeatSubmit;
import com.ruoyi.common.web.core.BaseController;
import jakarta.annotation.Resource;
import com.ruoyi.system.domain.bo.sysuser.SysUserAddBo;
import com.ruoyi.system.domain.bo.sysuser.SysUserEditBo;
import com.ruoyi.system.service.SysUserService;

/**
 * 用户信息控制器
 * <p>
 * 提供用户信息地增删改查、导入导出等功能，主要包含：
 * <ul>
 *   <li>用户信息地增删改查</li>
 *   <li>用户信息的分页查询</li>
 *   <li>用户信息的下拉选择</li>
 * </ul>
 *
 * <AUTHOR>
 * @since 2025-04-29 14:00:02
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/user")
public class SysUserController extends BaseController {
    @Resource
    private SysUserService sysUserService;

    /**
     * 分页查询用户信息列表
     * <p>
     * 支持多条件组合查询，返回分页数据
     *
     * @param userBo 查询条件对象，包含分页参数和查询条件
     * @return 包含分页信息和用户信息列表的结果对象
     */
    @SaCheckPermission("system:user:list")
    @GetMapping("/list")
    public Result<PageData<SysUserListVo>> page(SysUserListBo userBo) {
        return sysUserService.page(userBo);
    }



    /**
     * 新增用户信息
     * <p>
     * 包含数据校验和重复提交控制，确保数据的完整性和一致性
     *
     * @param sysUserAddBo 用户信息对象，包含必填字段验证
     * @return 操作结果，成功或失败信息
     */
    @SaCheckPermission("system:user:add")
    @Log(title = "用户信息", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping
    public Result<Void> add(@Validated @RequestBody SysUserAddBo sysUserAddBo) {
        return sysUserService.add(sysUserAddBo);
    }

    /**
     * 修改用户信息
     * <p>
     * 包含数据校验和重复提交控制，确保数据的完整性和一致性
     *
     * @param sysUserEditBo 用户信息对象，包含必填字段验证
     * @return 操作结果，成功或失败信息
     */
    @SaCheckPermission("system:user:edit")
    @Log(title = "用户信息", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping
    public Result<Void> edit(@Validated @RequestBody SysUserEditBo sysUserEditBo) {
        return sysUserService.edit(sysUserEditBo);
    }

    /**
     * 删除用户信息
     * <p>
     * 支持批量删除操作，可同时删除多个用户信息
     *
     * @param ids 用户信息ID数组，包含要删除的ID列表
     * @return 操作结果，成功或失败信息
     */
    @SaCheckPermission("system:user:remove")
    @Log(title = "用户信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result<Void> remove(@PathVariable String[] ids) {
        return sysUserService.removeByIds(ids);
    }

    /**
     * 获取用户信息
     *
     * @return 用户信息
     */
    @GetMapping("/getInfo" )
    public Result<UserInfoVo> getInfo() {
        return sysUserService.getInfo();
    }

    /**
     * 根据用户编号获取详细信息
     */
    @SaCheckPermission("system:user:query" )
    @GetMapping(value = {"/" , "/{id}"})
    public Result<SysUserInfoVo> getInfo(@PathVariable(value = "id" , required = false) String id) {
        return sysUserService.getInfo(id);
    }

    /**
     * 重置密码
     */
    @ApiEncrypt
    @SaCheckPermission("system:user:resetPwd" )
    @Log(title = "用户管理" , businessType = BusinessType.UPDATE)
    @PutMapping("/resetPwd" )
    public Result<Void> resetPwd(@RequestBody SysUserEditBo user) {
        return sysUserService.resetPwd(user);
    }

    /**
     * 状态修改
     */
    @SaCheckPermission("system:user:edit" )
    @Log(title = "用户管理" , businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus" )
    public Result<Void> changeStatus(@RequestBody SysUserEditBo user) {
        return sysUserService.changeStatus(user);
    }

}
