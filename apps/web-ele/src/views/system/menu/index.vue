<script lang="ts" setup>
import { Page } from '@vben/common-ui';

import { useMenuService } from './service';

defineOptions({
  name: 'MenuIndex',
});

const { Form, BasicTable, FormDialog, useToolbarButtons, useRowButtons } =
  useMenuService();
</script>

<template>
  <Page auto-content-height>
    <FormDialog class="w-[600px]" title="菜单权限管理">
      <Form />
    </FormDialog>

    <BasicTable>
      <template #toolbar-actions>
        <ActionButtonGroup :buttons="useToolbarButtons()" />
      </template>

      <template #action="{ row }">
        <ActionButtonGroup :buttons="useRowButtons()" :row="row" />
      </template>
    </BasicTable>
  </Page>
</template>
