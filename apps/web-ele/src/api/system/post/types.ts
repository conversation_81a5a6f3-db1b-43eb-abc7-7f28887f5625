export interface PostVO extends BaseEntity {
  id: number | string;
  code: string;
  name: string;
  sort: number;
  status: string;
  remark: string;
}

export interface PostForm {
  id: number | string | undefined;
  code: string;
  name: string;
  sort: number;
  status: string;
  version: number;
  remark: string;
}

export interface PostQuery extends PageQuery {
  code: string;
  name: string;
  status: string;
}
