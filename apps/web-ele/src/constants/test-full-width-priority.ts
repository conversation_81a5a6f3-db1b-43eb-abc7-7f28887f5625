// 测试全角字符验证优先级
import { BaseSchema } from './zod-enum';

console.log('=== 测试全角字符验证优先级 ===');

// 1. 测试全角数字（应该显示全角字符错误，而不是长度或格式错误）
console.log('\n1. 全角数字测试:');
const fullWidthNumbers = BaseSchema.GAJGJGDM.safeParse('１２３４５６７８９０１２');
console.log('全角数字结果:', fullWidthNumbers);
if (!fullWidthNumbers.success) {
  console.log('全角数字错误信息:', fullWidthNumbers.error.issues[0].message);
  console.log('期望: "不能包含全角字符，请使用半角字符"');
}

// 2. 测试短的全角数字（应该显示全角字符错误，而不是长度错误）
console.log('\n2. 短的全角数字测试:');
const shortFullWidth = BaseSchema.GAJGJGDM.safeParse('１２３');
console.log('短的全角数字结果:', shortFullWidth);
if (!shortFullWidth.success) {
  console.log('短的全角数字错误信息:', shortFullWidth.error.issues[0].message);
  console.log('期望: "不能包含全角字符，请使用半角字符"');
}

// 3. 测试混合字符（全角+半角）
console.log('\n3. 混合字符测试:');
const mixedChars = BaseSchema.GAJGJGDM.safeParse('123４５６789012');
console.log('混合字符结果:', mixedChars);
if (!mixedChars.success) {
  console.log('混合字符错误信息:', mixedChars.error.issues[0].message);
  console.log('期望: "不能包含全角字符，请使用半角字符"');
}

// 4. 测试纯半角但长度不对（应该显示长度错误）
console.log('\n4. 半角长度错误测试:');
const halfWidthShort = BaseSchema.GAJGJGDM.safeParse('12345');
console.log('半角长度错误结果:', halfWidthShort);
if (!halfWidthShort.success) {
  console.log('半角长度错误信息:', halfWidthShort.error.issues[0].message);
  console.log('期望: "请输入12位数字"');
}

// 5. 测试半角但包含字母（应该显示格式错误）
console.log('\n5. 半角格式错误测试:');
const halfWidthLetters = BaseSchema.GAJGJGDM.safeParse('abcdefghijkl');
console.log('半角格式错误结果:', halfWidthLetters);
if (!halfWidthLetters.success) {
  console.log('半角格式错误信息:', halfWidthLetters.error.issues[0].message);
  console.log('期望: "请输入12位数字"');
}

// 6. 测试正确的情况
console.log('\n6. 正确情况测试:');
const correct = BaseSchema.GAJGJGDM.safeParse('123456789012');
console.log('正确结果:', correct);
console.log('期望: success: true');

// 7. 测试中文字符
console.log('\n7. 中文字符测试:');
const chineseChars = BaseSchema.GAJGJGDM.safeParse('一二三四五六七八九十一二');
console.log('中文字符结果:', chineseChars);
if (!chineseChars.success) {
  console.log('中文字符错误信息:', chineseChars.error.issues[0].message);
  console.log('期望: "不能包含全角字符，请使用半角字符"');
}

// 8. 测试全角空格
console.log('\n8. 全角空格测试:');
const fullWidthSpace = BaseSchema.GAJGJGDM.safeParse('123456　89012');
console.log('全角空格结果:', fullWidthSpace);
if (!fullWidthSpace.success) {
  console.log('全角空格错误信息:', fullWidthSpace.error.issues[0].message);
  console.log('期望: "不能包含全角字符，请使用半角字符"');
}

export { 
  fullWidthNumbers, 
  shortFullWidth, 
  mixedChars, 
  halfWidthShort, 
  halfWidthLetters, 
  correct 
};
