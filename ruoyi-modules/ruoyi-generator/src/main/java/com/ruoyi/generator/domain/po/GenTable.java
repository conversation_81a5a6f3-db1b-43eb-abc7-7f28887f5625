package com.ruoyi.generator.domain.po;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.RelationOneToMany;
import com.mybatisflex.annotation.Table;
import com.ruoyi.common.core.constant.GenConstants;
import com.ruoyi.common.core.utils.StringUtils;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import org.apache.commons.lang3.ArrayUtils;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 业务表 gen_table
 *
 * <AUTHOR>
 * <AUTHOR>
 */
@Data
@Table(value = "gen_table")
public class GenTable implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 编号
     */
    @Id
    private String id;

    /**
     * 表名称
     */
    @NotBlank(message = "表名称不能为空")
    private String tableName;

    /**
     * 表描述
     */
    @NotBlank(message = "表描述不能为空")
    private String tableComment;

    /**
     * 关联父表的表名
     */
    private String subTableName;

    /**
     * 本表关联父表的外键名
     */
    private String subTableFkName;

    /**
     * 实体类名称(首字母大写)
     */
    @NotBlank(message = "实体类名称不能为空")
    private String className;

    /**
     * 使用的模板（crud单表操作 tree树表操作 sub主子表操作）
     */
    private String tplCategory;

    /**
     * 生成包路径
     */
    @NotBlank(message = "生成包路径不能为空")
    private String packageName;

    /**
     * 生成模块名
     */
    @NotBlank(message = "生成模块名不能为空")
    private String moduleName;

    /**
     * 生成业务名
     */
    @NotBlank(message = "生成业务名不能为空")
    private String businessName;

    /**
     * 生成功能名
     */
    @NotBlank(message = "生成功能名不能为空")
    private String functionName;

    /**
     * 生成作者
     */
    @NotBlank(message = "作者不能为空")
    private String functionAuthor;

    /**
     * 生成代码方式（0zip压缩包 1自定义路径）
     */
    private String genType;

    /**
     * 生成路径（不填默认项目路径）
     */
    private String genPath;

    /**
     * 是否生成导入功能（1是 0否）
     */
    private String isImport;

    /**
     * 是否生成导出功能（1是 0否）
     */
    private String isExport;

    /**
     * 是否生成下拉选项功能（1是 0否）
     */
    private String isOption;

    /**
     * 弹窗组件类型（0是Dialog弹窗 1是Drawer抽屉）
     */
    private String dialogType;

    /**
     * 组件类型（0Element Plus 1Vben Admin）
     */
    private String componentType;

    /**
     * 主键信息
     */
    @Column(ignore = true)
    private GenTableColumn pkColumn;

    /**
     * 子表信息
     */
    private GenTable subTable;

    /**
     * 表列信息
     */
    @Valid
    @Column(ignore = true)
    @RelationOneToMany(selfField = "id", targetField = "tableId", orderBy = "sort")
    private List<GenTableColumn> columns;

    /**
     * 其它生成选项
     */
    private String options;

    /**
     * 树编码字段
     */
    @Column(ignore = true)
    private String treeCode;

    /**
     * 树父编码字段
     */
    @Column(ignore = true)
    private String treeParentCode;

    /**
     * 树名称字段
     */
    @Column(ignore = true)
    private String treeName;

    /*
     * 菜单id列表
     */
    @Column(ignore = true)
    private List<String> menuIds;

    /**
     * 上级菜单ID字段
     */
    @Column(ignore = true)
    private String parentMenuId;

    /**
     * 上级菜单名称字段
     */
    @Column(ignore = true)
    private String parentMenuName;

    /**
     * 乐观锁
     */
    @Column(version = true)
    private Integer version;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 编辑页列数
     */
    private Integer editColumns;

    /**
     * 请求参数
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @Column(ignore = true)
    private Map<String, Object> params = new HashMap<>();

    public boolean isSub() {
        return isSub(this.tplCategory);
    }

    public static boolean isSub(String tplCategory) {
        return tplCategory != null && StringUtils.equals(GenConstants.TPL_SUB, tplCategory);
    }

    public boolean isTree() {
        return isTree(this.tplCategory);
    }

    public static boolean isTree(String tplCategory) {
        return tplCategory != null && StringUtils.equals(GenConstants.TPL_TREE, tplCategory);
    }

    public boolean isCrud() {
        return isCrud(this.tplCategory);
    }

    public static boolean isCrud(String tplCategory) {
        return tplCategory != null && StringUtils.equals(GenConstants.TPL_CRUD, tplCategory);
    }

    public boolean isSuperColumn(String javaField) {
        return isSuperColumn(this.tplCategory, javaField);
    }

    public static boolean isSuperColumn(String tplCategory, String javaField) {
        if (isTree(tplCategory)) {
            return StringUtils.equalsAnyIgnoreCase(javaField,
                ArrayUtils.addAll(GenConstants.TREE_ENTITY, GenConstants.BASE_ENTITY));
        }
        return StringUtils.equalsAnyIgnoreCase(javaField, GenConstants.BASE_ENTITY);
    }

    /**
     * 判断是否需要导入
     */
    public boolean isNeedImport() {
        return StringUtils.equals("1", this.isImport);
    }

    /**
     * 判断是否需要导出
     */
    public boolean isNeedExport() {
        return StringUtils.equals("1", this.isExport);
    }

    /**
     * 判断是否需要下拉选项
     */
    public boolean isNeedOption() {
        return StringUtils.equals("1", this.isOption) || isTree();
    }

    /**
     * 判断是否使用Dialog弹窗组件
     */
    public boolean isDialogModal() {
        return StringUtils.isEmpty(this.dialogType) || StringUtils.equals("0", this.dialogType);
    }

    /**
     * 判断是否使用Drawer抽屉组件
     */
    public boolean isDrawerModal() {
        return StringUtils.equals("1", this.dialogType);
    }

    /**
     * 判断是否使用Element Plus组件
     */
    public boolean isElementPlus() {
        return StringUtils.isEmpty(this.componentType) || StringUtils.equals("0", this.componentType);
    }

    /**
     * 判断是否使用Vben Admin组件
     */
    public boolean isVbenAdmin() {
        return StringUtils.equals("1", this.componentType);
    }

    /**
     * 获取前端模板目录
     */
    public String getFrontendTemplateDir() {
        return isElementPlus() ? "vue" : "vben";
    }
}
