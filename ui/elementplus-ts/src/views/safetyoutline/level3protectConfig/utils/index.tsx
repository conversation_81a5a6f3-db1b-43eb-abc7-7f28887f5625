import type { ColumnProps, SkTableInstance } from '@/components/SkUi/SkTable/interface';
import type { Level3protectConfigForm, Level3protectConfigVO } from '@/api/safetyoutline/level3protectConfig/types';
import sysLevel3protectConfigDialog from '@/views/safetyoutline/level3protectConfig/components/SysLevel3protectConfigDialog.vue';
import {
  addLevel3protectConfig,
  editLevel3protectConfig,
  getLevel3protectConfig,
  removeLevel3protectConfig
} from '@/api/safetyoutline/level3protectConfig';
import { addDialog } from '@/components/SkUi/SkReDialog';
import { h, reactive, ref } from 'vue';
import { useValidator } from '@/hooks/skUi/web/useValidator';

export function useLevel3protectConfig() {
  const skTable = ref<SkTableInstance>();
  const { required } = useValidator();
  /**
   * 表格配置项
   */
  const columns = reactive<ColumnProps<Level3protectConfigVO>[]>([
    { type: 'index', label: '#', width: 80 },
    {
      prop: 'pzsyzdlmsbz',
      align: 'center',
      label: '是否配置双因子登录模式',
      enum: [
        { label: '是', value: '1' },
        { label: '否', value: '0' }
      ]
    },
    {
      prop: 'zdlxdlsbcs',
      search: {},
      align: 'center',
      label: '最大连续登录失败次数'
    },
    {
      prop: 'lxdlsbsdsj',
      search: {},
      align: 'center',
      label: '连续登录失败锁定分钟'
    },
    {
      prop: 'dlwczzdtcsj',
      search: {},
      align: 'center',
      label: '登录后无操作自动退出的分钟'
    },
    {
      prop: 'kqmmfzdbz',
      align: 'center',
      label: '是否开启密码复杂度',
      enum: [
        { label: '是', value: '1' },
        { label: '否', value: '0' }
      ]
    },
    {
      prop: 'dqxgmmsjjg',
      search: {},
      align: 'center',
      label: '定期修改密码时间间隔(月)'
    },
    {
      prop: 'dqxgmmbxxcfcs',
      search: {},
      align: 'center',
      label: '定期修改密码不允许重复次数'
    },
    {
      prop: 'wjaqjcbz',
      search: {},
      align: 'center',
      label: '是否开启文件安全检测',
      enum: [
        { label: '是', value: '1' },
        { label: '否', value: '0' }
      ]
    },
    {
      prop: 'scwjdxxz',
      search: {},
      align: 'center',
      label: '上传文件大小限制mb(兆)'
    },
    {
      prop: 'createTime',
      label: '创建时间',
      width: 180,
      search: {
        el: 'date-picker',
        span: 2,
        props: {
          type: 'daterange',
          valueFormat: 'YYYY-MM-DD',
          rangeSeparator: '-',
          startPlaceholder: '开始日期',
          endPlaceholder: '结束日期'
        }
      }
    },
    { prop: 'operation', slot: 'operation', label: '操作', width: 300, fixed: 'right' }
  ]);

  const rules = reactive({
    createTime: [required('创建时间')],
    createBy: [required('创建人')],
    pzsyzdlmsbz: [required('配置双因子登录模式')],
    zdlxdlsbcs: [required('最大连续登录失败次数')],
    lxdlsbsdsj: [required('连续登录失败锁定分钟')],
    dlwczzdtcsj: [required('登录后无操作自动退出的分钟')],
    kqmmfzdbz: [required('开启密码复杂度')],
    dqxgmmsjjg: [required('定期修改密码时间间隔')],
    dqxgmmbxxcfcs: [required('定期修改密码不允许重复次数')],
    wjaqjcbz: [required('文件安全检测')],
    scwjdxxz: [required('上传文件大小限制')]
  });
  /**
   * 初始化编辑信息
   * @param level3protectConfigVo
   */
  const initFormProps = async (level3protectConfigVo: Partial<Level3protectConfigVO>) => {
    //初始化默认值
    const formInline = ref<Level3protectConfigForm>({
      id: '',
      pzsyzdlmsbz: '0',
      zdlxdlsbcs: 5,
      lxdlsbsdsj: 30,
      dlwczzdtcsj: 30,
      kqmmfzdbz: '1',
      dqxgmmsjjg: 3,
      dqxgmmbxxcfcs: 3,
      wjaqjcbz: '1',
      scwjdxxz: 50
    });
    if (level3protectConfigVo.id) {
      const res = await getLevel3protectConfig(level3protectConfigVo.id);
      Object.assign(formInline.value, res.data);
    }
    return formInline;
  };

  /***
   * 编辑，新增,数据交换
   */
  const openDialog = async (title: string, row: Partial<Level3protectConfigVO> = {}) => {
    addDialog({
      title: title + '三级等保配置信息',
      width: '1024px',
      props: {
        model: await initFormProps(row),
        labelWidth: '140px',
        rules: rules
      },
      contentRenderer: () => h(sysLevel3protectConfigDialog),
      beforeSure: async (done, { options }, formRef) => {
        const curData = options.props.model as Level3protectConfigForm;
        formRef.validate(async (valid: boolean) => {
          if (valid) {
            const api = title === '新增' ? addLevel3protectConfig : editLevel3protectConfig;
            await api(curData).then((res: any) => {
              if (res.code === 200) {
                window.skynet.alert(`${title}成功！`);
                done();
                skTable.value?.getTableList();
              }
            });
          }
        });
      }
    });
  };
  /** 导出按钮操作 */
  const handleExport = () => {
    window.skynet.download(
      'safetyoutline/level3protectConfig/export',
      {
        ...skTable.value.searchParam
      },
      `level3protectConfig_${new Date().getTime()}.xlsx`
    );
  };

  /** 删除按钮操作 */
  const handleDelete = async (row?: Level3protectConfigVO) => {
    const _ids = row?.id;
    await window.skynet
      .confirm('是否确认删除三级等保配置信息？')
      .then(async () => {
        await removeLevel3protectConfig(_ids);
        window.skynet.alert('删除成功');
        await skTable.value?.getTableList();
      })
      .catch(() => {});
  };
  return {
    skTable,
    columns,
    rules,
    openDialog,
    handleExport,
    handleDelete
  };
}
