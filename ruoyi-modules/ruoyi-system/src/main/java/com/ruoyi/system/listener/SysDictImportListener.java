package com.ruoyi.system.listener;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.idev.excel.context.AnalysisContext;
import cn.idev.excel.event.AnalysisEventListener;
import com.ruoyi.common.core.core.domain.Result;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.MapstructUtils;
import com.ruoyi.common.core.utils.SpringUtils;
import com.ruoyi.common.core.utils.ValidatorUtils;
import com.ruoyi.common.excel.core.ExcelListener;
import com.ruoyi.common.excel.core.ExcelResult;
import com.ruoyi.system.domain.bo.sysdict.SysDictAddBo;
import com.ruoyi.system.domain.bo.sysdict.SysDictEditBo;
import com.ruoyi.system.domain.bo.sysdict.SysDictImportBo;
import com.ruoyi.system.domain.vo.sysdict.SysDicDetailVo;
import com.ruoyi.system.service.SysDictService;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 字典管理自定义导入
 *
 * <AUTHOR>
 */
@Slf4j
public class SysDictImportListener extends AnalysisEventListener<SysDictImportBo> implements ExcelListener<SysDictImportBo> {
    private final SysDictService sysDictService;

    private final Boolean isUpdateSupport;
    private int successNum = 0;
    private int failureNum = 0;
    private final StringBuilder successMsg = new StringBuilder();
    private final StringBuilder failureMsg = new StringBuilder();

    public SysDictImportListener(Boolean isUpdateSupport) {
        this.sysDictService = SpringUtils.getBean(SysDictService.class);
        this.isUpdateSupport = isUpdateSupport;
    }

    @Override
    public void invoke(SysDictImportBo sysDictVo, AnalysisContext context) {
        try {
            //TODO:根据某个字段，查询数据库表中是否存在记录，不存在就新增，存在就更新
            SysDicDetailVo sysDictVo1 = null;

            sysDictVo1 = sysDictService.selectById(sysDictVo.getId());
            if (ObjectUtil.isNull(sysDictVo1)) {
                SysDictAddBo sysDictBo = BeanUtil.toBean(sysDictVo, SysDictAddBo.class);

                ValidatorUtils.validate(sysDictBo);
                boolean inserted = sysDictService.insertWithPk(sysDictBo);//树表需要前台传来主键值

                if (inserted) {
                    successNum++;
                    successMsg.append("<br/>" ).append(successNum).append("、字典管理 记录导入成功" );
                    return;
                } else {
                    failureNum++;
                    failureMsg.append("<br/>" ).append(failureNum).append("、字典管理 记录导入失败" );
                    return;
                }
            } else if (isUpdateSupport) {
                //存在就更新
                SysDictEditBo updateBo = MapstructUtils.convert(sysDictVo, SysDictEditBo.class);
                updateBo.setId(sysDictVo1.getId());//主键
                Result<Void> update = sysDictService.update(updateBo);
                if (update.isSuccess()) {
                    successNum++;
                    successMsg.append("<br/>" ).append(successNum).append("、字典管理 记录更新成功" );
                    return;
                } else {
                    failureNum++;
                    failureMsg.append("<br/>" ).append(failureNum).append("、字典管理 记录更新失败" );
                    return;
                }
            }
        } catch (Exception e) {
            failureNum++;
            String msg = "<br/>" + failureNum + "、字典管理 记录导入失败：";
            failureMsg.append(msg).append(e.getMessage());
            log.error(msg, e);
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {

    }

    @Override
    public ExcelResult<SysDictImportBo> getExcelResult() {
        return new ExcelResult<>() {

            @Override
            public String getAnalysis() {
                if (failureNum > 0) {
                    failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据没有成功导入，错误如下：" );
                    throw new ServiceException(failureMsg.toString());
                } else {
                    successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：" );
                }
                return successMsg.toString();
            }

            @Override
            public List<SysDictImportBo> getList() {
                return null;
            }

            @Override
            public List<String> getErrorList() {
                return null;
            }
        };
    }
}
