package com.ruoyi.system.domain.bo.systenant;

import com.ruoyi.system.domain.po.SysTenant;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import jakarta.validation.constraints.*;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serial;
import java.io.Serializable;

/**
 * 租户业务对象 sys_tenant
 *
 * <AUTHOR>
 * @since 2025-04-28 09:55:52
 */
@Data
@AutoMapper(target = SysTenant.class, reverseConvertGenerate = false)
public class SysTenantListBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 联系人
     */
    @NotBlank(message = "联系人不能为空")
    private String contactUserName;

    /**
     * 联系电话
     */
    @NotBlank(message = "联系电话不能为空")
    private String contactPhone;

    /**
     * 企业名称
     */
    @NotBlank(message = "企业名称不能为空")
    private String companyName;

    /**
     * 过期时间
     */
    @NotNull(message = "过期时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" )
    private LocalDateTime expireTime;


}
