// 测试 GAJGJGDM 错误信息
import { BaseSchema, ZodEnum } from './zod-enum';

console.log('=== 测试 GAJGJGDM 错误信息 ===');

// 1. 测试长度错误
console.log('\n1. 长度错误测试:');
const lengthError = BaseSchema.GAJGJGDM.safeParse('12345');
console.log('长度错误结果:', lengthError);
if (!lengthError.success) {
  console.log('长度错误信息:', lengthError.error.issues.map(issue => issue.message));
}

// 2. 测试格式错误（非数字）
console.log('\n2. 格式错误测试:');
const formatError = BaseSchema.GAJGJGDM.safeParse('abcdefghijkl');
console.log('格式错误结果:', formatError);
if (!formatError.success) {
  console.log('格式错误信息:', formatError.error.issues.map(issue => issue.message));
}

// 3. 测试全角字符错误
console.log('\n3. 全角字符错误测试:');
const fullWidthError = BaseSchema.GAJGJGDM.safeParse('１２３４５６７８９０１２');
console.log('全角字符错误结果:', fullWidthError);
if (!fullWidthError.success) {
  console.log('全角字符错误信息:', fullWidthError.error.issues.map(issue => issue.message));
}

// 4. 测试正确情况
console.log('\n4. 正确情况测试:');
const correctResult = BaseSchema.GAJGJGDM.safeParse('123456789012');
console.log('正确结果:', correctResult);

// 5. 测试 ZodEnum.GAJGJGDM
console.log('\n5. ZodEnum.GAJGJGDM 测试:');
const zodEnumError = ZodEnum.GAJGJGDM.safeParse('12345');
console.log('ZodEnum 长度错误结果:', zodEnumError);
if (!zodEnumError.success) {
  console.log('ZodEnum 长度错误信息:', zodEnumError.error.issues.map(issue => issue.message));
}

// 6. 测试其他字段的错误信息
console.log('\n6. 其他字段错误信息测试:');

// EMAIL 测试
const emailError = BaseSchema.EMAIL.safeParse('invalid-email');
console.log('EMAIL 错误结果:', emailError);
if (!emailError.success) {
  console.log('EMAIL 错误信息:', emailError.error.issues.map(issue => issue.message));
}

// PHONE 测试
const phoneError = BaseSchema.PHONE.safeParse('12345678901');
console.log('PHONE 错误结果:', phoneError);
if (!phoneError.success) {
  console.log('PHONE 错误信息:', phoneError.error.issues.map(issue => issue.message));
}

export { lengthError, formatError, fullWidthError };
