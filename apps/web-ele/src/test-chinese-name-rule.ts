// 测试中文姓名验证规则是否完全符合您提供的规则
import { validateChineseNameByRule } from './utils/validators/name-validator';

console.log('=== 中文姓名验证规则测试 ===');
console.log('原规则：');
console.log('var flag = /^[\\u0391-\\uFFE5]+$|^[\\u0391-\\uFFE5]+·?[\\u0391-\\uFFE5]+$/.test(value);');
console.log('if (flag){ flag = /^[^【】、﹒▪•]+$/.test(value); }');
console.log('return this.optional(element) || flag;');

// 原始的 jQuery Validator 规则实现（用于对比）
function originalIsChinese(value: string, allowEmpty: boolean = false): boolean {
  // 对应 this.optional(element)
  if (allowEmpty && (!value || value.trim() === '')) {
    return true;
  }
  
  if (!value || value.trim() === '') {
    return false;
  }

  const trimmedValue = value.trim();
  
  // 第一步验证
  let flag = /^[\u0391-\uFFE5]+$|^[\u0391-\uFFE5]+·?[\u0391-\uFFE5]+$/.test(trimmedValue);
  
  // 第二步验证
  if (flag) {
    flag = /^[^【】、﹒▪•]+$/.test(trimmedValue);
  }
  
  return flag;
}

// 测试用例
const testCases = [
  // 有效的中文姓名
  { name: '张三', expected: true, description: '普通中文姓名' },
  { name: '李四', expected: true, description: '普通中文姓名' },
  { name: '王小明', expected: true, description: '三字中文姓名' },
  { name: '欧阳修', expected: true, description: '复合姓氏' },
  { name: '司马懿', expected: true, description: '复合姓氏' },
  { name: '诸葛亮', expected: true, description: '复合姓氏' },
  { name: '上官婉儿', expected: true, description: '复合姓氏' },
  
  // 带中文点的姓名
  { name: '爱新觉罗·玄烨', expected: true, description: '满族姓名（带中文点）' },
  { name: '叶赫那拉·慧征', expected: true, description: '满族姓名（带中文点）' },
  { name: '博尔济吉特·布木布泰', expected: true, description: '蒙古族姓名（带中文点）' },
  { name: '钮祜禄·甄嬛', expected: true, description: '满族姓名（带中文点）' },
  
  // 中文点可选的情况（·?表示可选）
  { name: '爱新觉罗玄烨', expected: true, description: '满族姓名（不带中文点）' },
  { name: '叶赫那拉慧征', expected: true, description: '满族姓名（不带中文点）' },
  
  // 无效的姓名 - 包含英文
  { name: '张San', expected: false, description: '包含英文字符' },
  { name: 'Zhang三', expected: false, description: '包含英文字符' },
  { name: '李4', expected: false, description: '包含数字' },
  { name: '王小明123', expected: false, description: '包含数字' },
  
  // 无效的姓名 - 包含禁用字符
  { name: '张【三】', expected: false, description: '包含禁用字符【】' },
  { name: '李、四', expected: false, description: '包含禁用字符、' },
  { name: '王﹒小明', expected: false, description: '包含禁用字符﹒' },
  { name: '赵▪钱', expected: false, description: '包含禁用字符▪' },
  { name: '孙•李', expected: false, description: '包含禁用字符•' },
  
  // 中文点使用错误的情况
  { name: '·张三', expected: false, description: '中文点在开头' },
  { name: '张三·', expected: false, description: '中文点在结尾' },
  { name: '张·三·四', expected: false, description: '多个中文点' },
  { name: '·', expected: false, description: '只有中文点' },
  
  // 空值测试
  { name: '', expected: false, description: '空字符串（不允许空值）' },
  { name: '   ', expected: false, description: '空白字符串（不允许空值）' },
];

// 空值允许的测试用例
const emptyAllowedCases = [
  { name: '', expected: true, description: '空字符串（允许空值）' },
  { name: '   ', expected: true, description: '空白字符串（允许空值）' },
];

console.log('\n=== 测试结果对比 ===');

let passedCount = 0;
let totalCount = testCases.length;

testCases.forEach((testCase, index) => {
  const originalResult = originalIsChinese(testCase.name, false);
  const newResult = validateChineseNameByRule(testCase.name, false);
  const expectedResult = testCase.expected;
  
  const originalMatch = originalResult === expectedResult;
  const newMatch = newResult === expectedResult;
  const bothMatch = originalResult === newResult;
  
  if (originalMatch && newMatch && bothMatch) {
    passedCount++;
  }
  
  console.log(`\n${index + 1}. ${testCase.description}`);
  console.log(`   输入: "${testCase.name}"`);
  console.log(`   期望: ${expectedResult}`);
  console.log(`   原规则: ${originalResult} ${originalMatch ? '✅' : '❌'}`);
  console.log(`   新实现: ${newResult} ${newMatch ? '✅' : '❌'}`);
  console.log(`   一致性: ${bothMatch ? '✅' : '❌'}`);
});

console.log('\n=== 空值允许测试 ===');

emptyAllowedCases.forEach((testCase, index) => {
  const originalResult = originalIsChinese(testCase.name, true);
  const newResult = validateChineseNameByRule(testCase.name, true);
  const expectedResult = testCase.expected;
  
  const originalMatch = originalResult === expectedResult;
  const newMatch = newResult === expectedResult;
  const bothMatch = originalResult === newResult;
  
  console.log(`\n${index + 1}. ${testCase.description}`);
  console.log(`   输入: "${testCase.name}"`);
  console.log(`   期望: ${expectedResult}`);
  console.log(`   原规则: ${originalResult} ${originalMatch ? '✅' : '❌'}`);
  console.log(`   新实现: ${newResult} ${newMatch ? '✅' : '❌'}`);
  console.log(`   一致性: ${bothMatch ? '✅' : '❌'}`);
});

console.log('\n=== 正则表达式测试 ===');

// 直接测试正则表达式
const regex1 = /^[\u0391-\uFFE5]+$|^[\u0391-\uFFE5]+·?[\u0391-\uFFE5]+$/;
const regex2 = /^[^【】、﹒▪•]+$/;

const regexTestCases = [
  '张三',
  '爱新觉罗·玄烨',
  '爱新觉罗玄烨',
  '张San',
  '张【三】',
  '·张三',
  '张三·',
];

regexTestCases.forEach(testCase => {
  const step1 = regex1.test(testCase);
  const step2 = step1 ? regex2.test(testCase) : false;
  
  console.log(`\n"${testCase}"`);
  console.log(`   第一步 (中文字符): ${step1}`);
  console.log(`   第二步 (禁用字符): ${step2}`);
  console.log(`   最终结果: ${step2}`);
});

console.log('\n=== 总结 ===');
console.log(`通过测试: ${passedCount}/${totalCount}`);
console.log(`成功率: ${((passedCount / totalCount) * 100).toFixed(1)}%`);

if (passedCount === totalCount) {
  console.log('🎉 所有测试通过！新实现完全符合原规则！');
} else {
  console.log('⚠️ 部分测试失败，需要调整实现。');
}

// 导出测试函数供其他地方使用
export {
  originalIsChinese,
  validateChineseNameByRule,
  testCases,
  emptyAllowedCases
};
