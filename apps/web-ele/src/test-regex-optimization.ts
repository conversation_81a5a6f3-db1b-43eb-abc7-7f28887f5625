// 测试正则表达式优化后是否与原规则行为一致
console.log('=== 正则表达式优化验证 ===');

// 原始规则（有 ESLint 警告）
const originalRegex = /^[\u0391-\uFFE5]+$|^[\u0391-\uFFE5]+·?[\u0391-\uFFE5]+$/;

// 优化后的规则（无 ESLint 警告）
const optimizedRegex = /^[\u0391-\uFFE5]+(?:·[\u0391-\uFFE5]+)?$/;

// 测试用例
const testCases = [
  // 应该通过的
  '张三',
  '李四',
  '王小明',
  '欧阳修',
  '司马懿',
  '诸葛亮',
  '上官婉儿',
  '爱新觉罗·玄烨',
  '叶赫那拉·慧征',
  '博尔济吉特·布木布泰',
  '钮祜禄·甄嬛',
  
  // 应该失败的
  '张San',
  'Zhang三',
  '李4',
  '王小明123',
  '·张三',      // 开头有中文点
  '张三·',      // 结尾有中文点
  '张·三·四',   // 多个中文点
  '·',          // 只有中文点
  'abc',        // 纯英文
  '123',        // 纯数字
  '',           // 空字符串
];

console.log('\n=== 测试结果对比 ===');

let allMatch = true;
let passedCount = 0;

testCases.forEach((testCase, index) => {
  const originalResult = originalRegex.test(testCase);
  const optimizedResult = optimizedRegex.test(testCase);
  const isMatch = originalResult === optimizedResult;
  
  if (isMatch) {
    passedCount++;
  } else {
    allMatch = false;
  }
  
  console.log(`\n${index + 1}. "${testCase}"`);
  console.log(`   原规则: ${originalResult}`);
  console.log(`   优化后: ${optimizedResult}`);
  console.log(`   一致性: ${isMatch ? '✅' : '❌'}`);
});

console.log('\n=== 总结 ===');
console.log(`测试用例总数: ${testCases.length}`);
console.log(`通过测试: ${passedCount}`);
console.log(`成功率: ${((passedCount / testCases.length) * 100).toFixed(1)}%`);

if (allMatch) {
  console.log('🎉 优化成功！新正则表达式与原规则行为完全一致！');
  console.log('✅ ESLint 警告已解决');
  console.log('✅ 功能保持不变');
} else {
  console.log('⚠️ 优化后的正则表达式与原规则行为不一致，需要调整');
}

// 详细分析原规则的问题
console.log('\n=== 原规则分析 ===');
console.log('原规则: /^[\\u0391-\\uFFE5]+$|^[\\u0391-\\uFFE5]+·?[\\u0391-\\uFFE5]+$/');
console.log('问题: 第二部分 ^[\\u0391-\\uFFE5]+·?[\\u0391-\\uFFE5]+$ 中的 ·? 表示中文点可选');
console.log('当中文点不存在时，第二部分变成 ^[\\u0391-\\uFFE5]+[\\u0391-\\uFFE5]+$');
console.log('这与第一部分 ^[\\u0391-\\uFFE5]+$ 存在重叠，导致 ESLint 警告');

console.log('\n=== 优化方案 ===');
console.log('优化后: /^[\\u0391-\\uFFE5]+(?:·[\\u0391-\\uFFE5]+)?$/');
console.log('说明: 使用非捕获组 (?:·[\\u0391-\\uFFE5]+)? 表示整个"中文点+中文字符"组合可选');
console.log('这样避免了重复匹配，同时保持了相同的验证逻辑');

// 导出用于其他地方使用
export {
  originalRegex,
  optimizedRegex,
  testCases
};
