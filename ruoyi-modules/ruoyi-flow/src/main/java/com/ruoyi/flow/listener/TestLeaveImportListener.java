package com.ruoyi.flow.listener;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.idev.excel.context.AnalysisContext;
import cn.idev.excel.event.AnalysisEventListener;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.SpringUtils;
import com.ruoyi.common.core.utils.ValidatorUtils;
import com.ruoyi.common.excel.core.ExcelListener;
import com.ruoyi.common.excel.core.ExcelResult;
import com.ruoyi.common.core.core.domain.Result;
import com.ruoyi.flow.domain.vo.testleave.*;
import com.ruoyi.flow.domain.bo.testleave.*;
import com.ruoyi.flow.domain.po.TestLeave;
import com.ruoyi.flow.service.*;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 流程测试自定义导入
 *
 * <AUTHOR>
 * @date 2025-01-14 15:32:53
 */
@Slf4j
public class TestLeaveImportListener extends AnalysisEventListener<TestLeaveImportBo> implements ExcelListener<TestLeaveImportBo> {
    private final TestLeaveService testLeaveService;

    private final Boolean isUpdateSupport;
    private int successNum = 0;
    private int failureNum = 0;
    private final StringBuilder successMsg = new StringBuilder();
    private final StringBuilder failureMsg = new StringBuilder();

    public TestLeaveImportListener(Boolean isUpdateSupport) {
        this.testLeaveService = SpringUtils.getBean(TestLeaveService.class);
        this.isUpdateSupport = isUpdateSupport;
    }

    @Override
    public void invoke(TestLeaveImportBo testLeaveImportBo, AnalysisContext context) {
        try {
            TestLeave testLeave= testLeaveService.getById(testLeaveImportBo);
            if (ObjectUtil.isNull(testLeave)) {
                //不存在就新增
                TestLeaveAddBo testLeaveAddBo = BeanUtil.toBean(testLeaveImportBo, TestLeaveAddBo.class);
                ValidatorUtils.validate(testLeaveAddBo);
                  Result<Void> result= testLeaveService.add(testLeaveAddBo);
                if (result.isSuccess()) {
                    successNum++;
                    successMsg.append("<br/>").append(successNum).append("、流程测试 记录导入成功");
                    return;
                } else {
                    failureNum++;
                    failureMsg.append("<br/>").append(failureNum).append("、流程测试 记录导入失败");
                    return;
                }
            } else if (isUpdateSupport) {
                //存在就更新
                TestLeaveEditBo testLeaveEditBo = BeanUtil.toBean(testLeaveImportBo, TestLeaveEditBo.class);
                testLeaveEditBo.setId(testLeaveImportBo.getId());
                Result<Void> result= testLeaveService.edit(testLeaveEditBo);
                if (result.isSuccess()) {
                    successNum++;
                    successMsg.append("<br/>").append(successNum).append("、流程测试 记录更新成功");
                    return;
                } else {
                    failureNum++;
                    failureMsg.append("<br/>").append(failureNum).append("、流程测试 记录更新失败");
                    return;
                }
            }
        } catch (Exception e) {
            failureNum++;
            String msg = "<br/>" + failureNum + "、流程测试 记录导入失败：";
            failureMsg.append(msg).append(e.getMessage());
            log.error(msg, e);
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {

    }

    @Override
    public ExcelResult<TestLeaveImportBo> getExcelResult() {
        return new ExcelResult<>() {

            @Override
            public String getAnalysis() {
                if (failureNum > 0) {
                    failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据没有成功导入，错误如下：");
                    throw new ServiceException(failureMsg.toString());
                } else {
                    successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
                }
                return successMsg.toString();
            }

            @Override
            public List<TestLeaveImportBo> getList() {
                return null;
            }

            @Override
            public List<String> getErrorList() {
                return null;
            }
        };
    }
}
