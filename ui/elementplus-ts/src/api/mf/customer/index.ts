import http from '@/utils/http';
import type { CustomerVO, CustomerForm, CustomerQuery } from '@/api/mf/customer/types';
import type { PageData } from '@/utils/http/interface';

/**
 * 查询客户主表列表
 * @param query
 * @returns {*}
 */
export const listCustomer = (query?: CustomerQuery) => {
  return http.get<PageData<CustomerVO>>('/mf/customer/list', query);
};

/**
 * 查询客户主表详细
 * @param id
 */
export const getCustomer = (id: string | number) => {
  return http.get<CustomerVO>('/mf/customer/' + id);
};

/**
 * 新增客户主表
 * @param data
 */
export const addCustomer = (data: CustomerForm) => {
  return http.post('/mf/customer', data);
};

/**
 * 修改客户主表
 * @param data
 */
export const editCustomer = (data: CustomerForm) => {
  return http.put('/mf/customer', data);
};

/**
 * 删除客户主表
 * @param id
 */
export const removeCustomer = (id: string | number | Array<string | number>) => {
  return http.delete('/mf/customer/' + id);
};
