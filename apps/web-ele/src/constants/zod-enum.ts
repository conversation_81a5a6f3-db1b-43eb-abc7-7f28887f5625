import { z } from '#/adapter/form';
import { RegexEnum } from '#/constants/regex-enum';
import {
  validateIdCardChecksum,
  validateIdCardBirthDate,
  validateIdCardAreaCode,
} from '#/constants/id-card-validator';

/**
 * 为任何 schema 添加全角字符验证的包装器
 * 使用 refine 方法确保全角字符验证优先级最高
 * @param schema 要包装的 schema
 * @param message 自定义错误消息
 * @returns 包装后的 schema
 */
export const withNoFullWidth = <T extends z.ZodTypeAny>(
  schema: T,
  message?: string,
) =>
  z.string().superRefine((val, ctx) => {
    // 首先检查全角字符
    if (val && val !== '' && RegexEnum.FULL_WIDTH_REGEX.test(val)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: message || '不能包含全角字符，请使用半角字符',
      });
      return;
    }

    // 如果没有全角字符，执行原始验证
    const result = schema.safeParse(val);
    if (!result.success) {
      // 添加原始验证的错误
      result.error.issues.forEach((issue) => {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: issue.message,
          path: issue.path,
        });
      });
    }
  });

// 基础验证规则（避免循环引用）
export const BaseSchema = {
  STRING: withNoFullWidth(z.string()),
  EMAIL: withNoFullWidth(z.string().email('请输入正确的邮箱')),
  PHONE: withNoFullWidth(
    z.string().regex(RegexEnum.MOBILE, '请输入正确的手机号'),
  ),
  GAJGJGDM: withNoFullWidth(
    z
      .string()
      .length(12, '请输入12位数字')
      .regex(/^\d{12}$/, '请输入12位数字'),
  ), 
  /**
   * 公民身份号码（身份证号码）
   * 18位，包含地区代码、出生日期、顺序码和校验码的完整验证
   */
  ID_CARD: withNoFullWidth(
    z
      .string()
      .length(18, '身份证号码必须为18位')
      .regex(RegexEnum.ID_CARD_BASIC, '身份证号码格式不正确')
      .refine(
        (val) => validateIdCardAreaCode(val),
        '身份证号码地区代码无效'
      )
      .refine(
        (val) => validateIdCardBirthDate(val),
        '身份证号码出生日期无效'
      )
      .refine(
        (val) => validateIdCardChecksum(val),
        '身份证号码校验码错误'
      )
  ),
};

// 可选值包装器（函数形式）
export const optional = <T extends z.ZodTypeAny>(schema: T) =>
  schema.optional().or(z.literal(''));

// 枚举式 Zod 模式集合
export const ZodEnum = {
  STRING: BaseSchema.STRING,
  EMAIL: BaseSchema.EMAIL,
  EMAIL_EMPTY: optional(BaseSchema.EMAIL),
  PHONE: BaseSchema.PHONE,
  PHONE_EMPTY: optional(BaseSchema.PHONE),
  /**
   * 公安机关机构代码
   */
  GAJGJGDM: BaseSchema.GAJGJGDM,
  /**
   * 公安机关机构代码，可为空
   */
  GAJGJGDM_EMPTY: optional(BaseSchema.GAJGJGDM),
  /**
   * 公民身份号码（身份证号码）
   */
  ID_CARD: BaseSchema.ID_CARD,
  /**
   * 公民身份号码，可为空
   */
  ID_CARD_EMPTY: optional(BaseSchema.ID_CARD),
} as const;

export type ZodEnumKey = keyof typeof ZodEnum;
