package com.ruoyi.system.domain.bo.syssjy;

import cn.idev.excel.annotation.ExcelProperty;
import com.ruoyi.common.excel.annotation.ExcelDictFormat;
import com.ruoyi.common.excel.convert.ExcelDictConvert;
import lombok.Data;
import java.io.Serial;
import java.io.Serializable;
import lombok.NoArgsConstructor;

/**
 * 数据元管理导入视图对象 sys_sjy
 *
 * <AUTHOR>
 * @date 2025-01-03 17:31:57
 */
@Data
@NoArgsConstructor
public class SysSjyImportBo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /** 主键编号 */
    @ExcelProperty(value = "主键编号")
    private String id;
    /** 列名称 */
    @ExcelProperty(value = "列名称")
    private String columnName;
    /** JAVA字段名 */
    @ExcelProperty(value = "JAVA字段名")
    private String javaField;
    /** 列描述 */
    @ExcelProperty(value = "列描述")
    private String columnComment;
    /** 乐观锁 */
    @ExcelProperty(value = "乐观锁")
    private Integer version;
}
