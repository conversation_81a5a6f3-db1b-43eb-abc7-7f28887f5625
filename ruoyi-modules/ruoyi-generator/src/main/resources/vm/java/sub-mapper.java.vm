package ${packageName}.mapper;

import com.mybatisflex.core.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
#if($table.sub)
import ${packageName}.domain.po.${subClassName};
#else
import ${packageName}.domain.po.${ClassName};
#end

/**
 * ${subTable.functionName}Mapper接口
 *
 * <AUTHOR>
 * @since ${datetime}
 */
@Mapper
public interface ${subClassName}Mapper extends BaseMapper<${subClassName}> {

}
