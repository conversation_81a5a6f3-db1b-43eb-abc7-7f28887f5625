package com.ruoyi.system.service;

import cn.hutool.core.util.ObjectUtil;
import com.mybatisflex.core.query.QueryWrapper;
import com.ruoyi.common.core.core.domain.PageData;
import com.ruoyi.common.core.core.domain.Result;
import com.ruoyi.common.core.utils.MapstructUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.orm.core.page.PageQuery;
import com.ruoyi.common.orm.core.page.PageResult;
import com.ruoyi.common.orm.core.service.impl.BaseServiceImpl;
import com.ruoyi.system.domain.bo.sysclient.SysClientAddBo;
import com.ruoyi.system.domain.bo.sysclient.SysClientEditBo;
import com.ruoyi.system.domain.bo.sysclient.SysClientListBo;
import com.ruoyi.system.domain.po.SysClient;
import com.ruoyi.system.domain.vo.sysclient.SysClientDetailVo;
import com.ruoyi.system.domain.vo.sysclient.SysClientListVo;
import com.ruoyi.system.mapper.SysClientMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;

import static com.ruoyi.system.domain.po.table.SysClientTableDef.SYS_CLIENT;

/**
 * 系统授权Service业务层处理
 * <p>
 * 本服务类提供系统授权地增删改查等基础操作，包括：
 * <ul>
 *     <li>分页查询系统授权列表</li>
 *     <li>查询系统授权详情</li>
 *     <li>新增系统授权</li>
 *     <li>修改系统授权</li>
 *     <li>删除系统授权</li>
 * </ul>
 *
 * <AUTHOR>
 * @since 2025-05-10 10:07:06
 * @version 1.0
 */
@Service
public class SysClientService extends BaseServiceImpl<SysClientMapper, SysClient> {
    @Resource
    private SysClientMapper sysClientMapper;

    /**
     * 获取基础查询对象
     *
     * @return 预设了表信息的查询包装器
     */
    public QueryWrapper query() {
        return super.query().from(SYS_CLIENT);
    }

    /**
     * 构建查询条件
     * <p>
     * 根据传入的查询参数构建查询条件，MyBatis Flex会自动处理空值
     *
     * @param bo 查询参数对象
     * @return 构建好地查询条件包装器
     */
    private QueryWrapper buildQueryWrapper(SysClientListBo bo) {
        return super.buildBaseQueryWrapper()
                // 客户端id
                .and(SYS_CLIENT.CLIENT_ID.eq(bo.getClientId()))
                // 客户端key
                .and(SYS_CLIENT.CLIENT_KEY.eq(bo.getClientKey()))
                // 客户端秘钥
                .and(SYS_CLIENT.CLIENT_SECRET.eq(bo.getClientSecret()))
                // 授权类型
                .and(SYS_CLIENT.GRANT_TYPE.eq(bo.getGrantType()))
                // 设备类型
                .and(SYS_CLIENT.DEVICE_TYPE.eq(bo.getDeviceType()))
                // token活跃超时时间
                .and(SYS_CLIENT.ACTIVE_TIMEOUT.eq(bo.getActiveTimeout()))
                // token固定超时
                .and(SYS_CLIENT.TIMEOUT.eq(bo.getTimeout()))
                // 状态（0正常 1停用）
                .and(SYS_CLIENT.STATUS.eq(bo.getStatus()))
                ;
    }

    /**
     * 分页查询系统授权列表
     * <p>
     * 根据查询条件进行分页查询，并将结果转换为前端所需的VO对象
     *
     * @param bo 系统授权查询参数
     * @return 包含分页系统授权集合的结果对象
     */
    public Result<PageData<SysClientListVo>> page(SysClientListBo bo) {
        return PageResult.success(this.pageAs(
            PageQuery.build(),
            buildQueryWrapper(bo),
            SysClientListVo.class
        ));
    }

    /**
     * 查询系统授权列表（不分页）
     * <p>
     * 根据查询条件获取所有符合条件的系统授权
     *
     * @param bo 系统授权查询参数
     * @return 系统授权集合
     */
    public List<SysClientListVo> list(SysClientListBo bo) {
        QueryWrapper queryWrapper = buildQueryWrapper(bo);
        return this.listAs(queryWrapper, SysClientListVo.class);
    }





    /**
     * 新增系统授权
     * <p>
     * 将前端传入的数据转换为实体并保存到数据库
     *
     * @param bo 系统授权新增参数
     * @return 操作结果，成功返回{@code Result.success()}，失败返回包含错误信息的{@code Result.fail()}
     */
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> add(SysClientAddBo bo) {
        SysClient entity = MapstructUtils.convert(bo, SysClient.class);
        boolean inserted = this.save(entity);
        return inserted
            ? Result.success()
            : Result.fail("新增系统授权记录失败！");
    }

    /**
     * 修改系统授权
     * <p>
     * 更新数据库中已存在的系统授权
     *
     * @param bo 系统授权编辑参数
     * @return 操作结果，成功返回{@code Result.success()}，失败返回包含错误信息的{@code Result.fail()}
     */
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> edit(SysClientEditBo bo) {
        SysClient entity = MapstructUtils.convert(bo, SysClient.class);
        if (ObjectUtil.isNull(entity) || ObjectUtil.isNull(entity.getId())) {
            return Result.fail("系统授权ID不能为空");
        }

        boolean updated = this.updateById(entity);
        return updated
            ? Result.success()
            : Result.fail("修改系统授权记录失败!");
    }


    /**
     * 批量删除系统授权
     * <p>
     * 根据ID数组删除多条系统授权记录
     *
     * @param ids 需要删除的系统授权主键数组
     * @return 操作结果，成功返回{@code Result.success()}，失败返回包含错误信息的{@code Result.fail()}
     * @throws IllegalArgumentException 当ids为null或空数组时抛出
     */
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> removeByIds(String[] ids) {
        // 参数校验
        if (ids == null || ids.length == 0) {
            return Result.fail("删除ID不能为空");
        }

        // 执行批量删除
        return this.removeByIds(Arrays.asList(ids))
            ? Result.success()
            : Result.fail("删除系统授权记录失败!");
    }

    /**
     * 查询系统授权详情
     * <p>
     * 根据ID获取单个系统授权的详细信息
     *
     * @param id 系统授权主键
     * @return 系统授权详细信息，如果id为空则返回null
     */
    public SysClientDetailVo detail(String id) {
        if (StringUtils.isEmpty(id)) {
            return null;
        }

        return this.getOneAs(
            query().where(SYS_CLIENT.ID.eq(id)),
            SysClientDetailVo.class
        );
    }

    /**
     * 新增系统授权（带主键）
     * <p>
     * 使用前端提供的主键值新增记录，通常用于数据导入场景
     *
     * @param bo 系统授权新增参数（含主键）
     * @return 操作结果，成功返回{@code Result.success()}，失败返回包含错误信息的{@code Result.fail()}
     */
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> insertWithPk(SysClientAddBo bo) {
        SysClient entity = MapstructUtils.convert(bo, SysClient.class);
        // 使用特定的mapper方法执行带主键的插入
        boolean inserted = sysClientMapper.insertWithPk(entity) > 0;
        return inserted
            ? Result.success()
            : Result.fail("新增系统授权记录失败！");
    }


}
