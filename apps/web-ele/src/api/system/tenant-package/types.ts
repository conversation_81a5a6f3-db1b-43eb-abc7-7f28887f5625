export interface TenantPackageVO extends BaseEntity {
  /**
   * 租户套餐id
   */
  id: number | string;
  /**
   * 套餐名称
   */
  packageName: string;
  /**
   * 关联菜单id
   */
  menuIds: number | string;
  /**
   * 备注
   */
  remark: string;
  /**
   * 菜单树选择项是否关联显示
   */
  menuCheckStrictly: boolean;
  /**
   * 状态（0正常 1停用）
   */
  status: string;
}

export interface TenantPackageForm {
  /**
   * 租户套餐id
   */
  id?: number | string;
  /**
   * 套餐名称
   */
  packageName?: string;
  /**
   * 关联菜单id
   */
  menuIds?: Array<string>;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 菜单树选择项是否关联显示
   */
  menuCheckStrictly?: boolean;
  /**
   * 状态（0正常 1停用）
   */
  status?: string;
  /**
   * 乐观锁
   */
  version?: number;
}

/**
 * 租户套餐选项数据
 */
export type TenantPackageDataOption = BaseDataOption;

export interface TenantPackageQuery extends PageQuery {
  /**
   * 套餐名称
   */
  packageName?: string;
  /**
   * 关联菜单id
   */
  menuIds?: Array<string>;
  /**
   * 菜单树选择项是否关联显示
   */
  menuCheckStrictly?: boolean;
  /**
   * 状态（0正常 1停用）
   */
  status?: string;
  /**
   * 日期范围参数
   */
  params?: any;
}
