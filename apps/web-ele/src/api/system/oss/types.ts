export interface OssVO extends BaseEntity {
  /**
   * 对象存储主键
   */
  id: number | string;
  /**
   * 文件名
   */
  fileName: string;
  /**
   * 原名
   */
  originalName: string;
  /**
   * 扩展名
   */
  fileSuffix: string;
  /**
   * 文件预览
   */
  url: string;
  /**
   * 服务商
   */
  service: string;
  /**
   * 创建时间
   */
  createTime: string;
}

export interface OssForm {
  /**
   * 对象存储主键
   */
  id?: number | string;
  /**
   * 文件名
   */
  fileName?: string;
  /**
   * 原名
   */
  originalName?: string;
  /**
   * 扩展名
   */
  fileSuffix?: string;
  /**
   * 文件预览
   */
  url?: string;
  /**
   * 服务商
   */
  service?: string;
  /**
   * 乐观锁
   */
  version?: number;
}

export interface OssQuery extends PageQuery {
  /**
   * 文件名
   */
  fileName?: string;
  /**
   * 原名
   */
  originalName?: string;
  /**
   * 扩展名
   */
  fileSuffix?: string;
  /**
   * 服务商
   */
  service?: string;
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 日期范围参数
   */
  params?: any;
}
