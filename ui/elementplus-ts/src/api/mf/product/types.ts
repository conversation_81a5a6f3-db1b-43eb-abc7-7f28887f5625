export interface ProductVO extends BaseEntity {
  /**
   * 产品id
   */
  id: string | number;
  /**
   * 父产品id
   */
  parentId: string | number;
  /**
   * 产品名称
   */
  productName: string;
  /**
   * 显示顺序
   */
  orderNum: number;
  /**
   * 产品状态（0正常 1停用）
   */
  status: string;
  /**
   * 子树对象
   */
  children: ProductVO[];
}

export interface ProductForm {
  /**
   * 产品id
   */
  id?: string | number;
  /**
   * 父产品id
   */
  parentId?: string | number;
  /**
   * 产品名称
   */
  productName?: string;
  /**
   * 显示顺序
   */
  orderNum?: number;
  /**
   * 产品状态（0正常 1停用）
   */
  status?: string;
  /**
   * 乐观锁
   */
  version?: number;
}

/**
 * 产品树选项数据
 */
export interface ProductDataOption extends BaseDataOption {
  children?: ProductDataOption[];
}

export interface ProductQuery {
  /**
   * 父产品id
   */
  parentId?: string | number;
  /**
   * 产品名称
   */
  productName?: string;
  /**
   * 显示顺序
   */
  orderNum?: number;
  /**
   * 产品状态（0正常 1停用）
   */
  status?: string;

  /**
   * 是否树形结构
   */
  isTree?: boolean;
  /**
   * 日期范围参数
   */
  params?: any;
}
