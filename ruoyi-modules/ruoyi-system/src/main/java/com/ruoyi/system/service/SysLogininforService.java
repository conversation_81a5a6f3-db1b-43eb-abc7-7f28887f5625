package com.ruoyi.system.service;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.useragent.UserAgent;
import cn.hutool.http.useragent.UserAgentUtil;
import com.mybatisflex.core.query.QueryWrapper;
import com.ruoyi.common.core.constant.CacheConstants;
import com.ruoyi.common.core.constant.Constants;
import com.ruoyi.common.core.core.domain.PageData;
import com.ruoyi.common.core.core.domain.Result;
import com.ruoyi.common.core.utils.MapstructUtils;
import com.ruoyi.common.core.utils.ServletUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.utils.ip.AddressUtils;
import com.ruoyi.common.log.event.LogininforEvent;
import com.ruoyi.common.orm.core.page.PageQuery;
import com.ruoyi.common.orm.core.page.PageResult;
import com.ruoyi.common.orm.core.service.impl.BaseServiceImpl;
import com.ruoyi.common.redis.utils.RedisUtils;
import com.ruoyi.common.satoken.utlis.LoginHelper;
import com.ruoyi.common.tenant.helper.TenantHelper;
import com.ruoyi.system.domain.bo.syslogininfor.SysLogininforAddBo;
import com.ruoyi.system.domain.bo.syslogininfor.SysLogininforListBo;
import com.ruoyi.system.domain.po.SysClient;
import com.ruoyi.system.domain.po.SysLogininfor;
import com.ruoyi.system.domain.vo.syslogininfor.SysLogininforDetailVo;
import com.ruoyi.system.domain.vo.syslogininfor.SysLogininforListVo;
import com.ruoyi.system.mapper.SysLogininforMapper;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static com.ruoyi.system.domain.po.table.SysLogininforTableDef.SYS_LOGININFOR;

/**
 * 系统访问记录Service业务层处理
 * <p>
 * 本服务类提供系统访问记录地增删改查等基础操作，包括：
 * <ul>
 *     <li>分页查询系统访问记录列表</li>
 *     <li>查询系统访问记录详情</li>
 *     <li>新增系统访问记录</li>
 *     <li>修改系统访问记录</li>
 *     <li>删除系统访问记录</li>
 * </ul>
 *
 * <AUTHOR>
 * @since 2025-05-08 10:26:02
 * @version 1.0
 */
@Service
@Slf4j
public class SysLogininforService extends BaseServiceImpl<SysLogininforMapper, SysLogininfor> {
    @Resource
    private SysClientService clientService;

    /**
     * 获取基础查询对象
     *
     * @return 预设了表信息的查询包装器
     */
    public QueryWrapper query() {
        return super.query().from(SYS_LOGININFOR);
    }

    /**
     * 构建查询条件
     * <p>
     * 根据传入的查询参数构建查询条件，MyBatis Flex会自动处理空值
     *
     * @param bo 查询参数对象
     * @return 构建好地查询条件包装器
     */
    private QueryWrapper buildQueryWrapper(SysLogininforListBo bo) {
        return super.buildBaseQueryWrapper()
                // 用户账号
                .and(SYS_LOGININFOR.USER_NAME.like(bo.getUserName()))
                // IP地址
                .and(SYS_LOGININFOR.IPADDR.eq(bo.getIpaddr()))
                // 登录状态（0成功 1失败）
                .and(SYS_LOGININFOR.STATUS.eq(bo.getStatus()))
                // 访问时间
                .and(SYS_LOGININFOR.LOGIN_TIME.between(bo.getParams().get("beginLoginTime"), bo.getParams().get("endLoginTime")))
                .orderBy(SYS_LOGININFOR.LOGIN_TIME.desc())
                ;
    }

    /**
     * 分页查询系统访问记录列表
     * <p>
     * 根据查询条件进行分页查询，并将结果转换为前端所需的VO对象
     *
     * @param bo 系统访问记录查询参数
     * @return 包含分页系统访问记录集合的结果对象
     */
    public Result<PageData<SysLogininforListVo>> page(SysLogininforListBo bo) {
        return PageResult.success(this.pageAs(
            PageQuery.build(),
            buildQueryWrapper(bo),
            SysLogininforListVo.class
        ));
    }

    /**
     * 查询系统访问记录列表（不分页）
     * <p>
     * 根据查询条件获取所有符合条件的系统访问记录
     *
     * @param bo 系统访问记录查询参数
     * @return 系统访问记录集合
     */
    public List<SysLogininforListVo> list(SysLogininforListBo bo) {
        QueryWrapper queryWrapper = buildQueryWrapper(bo);
        return this.listAs(queryWrapper, SysLogininforListVo.class);
    }

    /**
     * 新增系统访问记录
     * <p>
     * 将前端传入的数据转换为实体并保存到数据库
     *
     * @param bo 系统访问记录新增参数
     * @return 操作结果，成功返回{@code Result.success()}，失败返回包含错误信息的{@code Result.fail()}
     */
    private Result<Void> add(SysLogininforAddBo bo) {
        SysLogininfor entity = MapstructUtils.convert(bo, SysLogininfor.class);
        boolean inserted = this.save(entity);
        return inserted
                ? Result.success()
                : Result.fail("新增系统访问记录记录失败！");
    }

    /**
     * 批量删除系统访问记录
     * <p>
     * 根据ID数组删除多条系统访问记录记录
     *
     * @param ids 需要删除的系统访问记录主键数组
     * @return 操作结果，成功返回{@code Result.success()}，失败返回包含错误信息的{@code Result.fail()}
     * @throws IllegalArgumentException 当ids为null或空数组时抛出
     */
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> removeByIds(String[] ids) {
        // 参数校验
        if (ids == null || ids.length == 0) {
            return Result.fail("删除ID不能为空");
        }

        // 执行批量删除
        return this.removeByIds(Arrays.asList(ids))
            ? Result.success()
            : Result.fail("删除系统访问记录记录失败!");
    }

    /**
     * 查询系统访问记录详情
     * <p>
     * 根据ID获取单个系统访问记录的详细信息
     *
     * @param id 系统访问记录主键
     * @return 系统访问记录详细信息，如果id为空则返回null
     */
    public SysLogininforDetailVo detail(String id) {
        if (StringUtils.isEmpty(id)) {
            return null;
        }

        return this.getOneAs(
            query().where(SYS_LOGININFOR.ID.eq(id)),
            SysLogininforDetailVo.class
        );
    }

    /**
     * 账户解锁
     *
     * @param userName 用户名
     */
    public Result<Void> unlock(String userName) {
        String loginName = CacheConstants.PWD_ERR_CNT_KEY + userName;
        if (RedisUtils.hasKey(loginName)) {
            RedisUtils.deleteObject(loginName);
        }
        return Result.success();
    }

    /**
     * 记录登录信息
     *
     * @param logininforEvent 登录事件
     */
    @Async
    @EventListener
    public void recordLogininfor(LogininforEvent logininforEvent) {
        HttpServletRequest request = logininforEvent.getRequest();
        final UserAgent userAgent = UserAgentUtil.parse(request.getHeader("User-Agent"));
        final String ip = ServletUtils.getClientIP(request);
        // 客户端信息
        String clientId = request.getHeader(LoginHelper.CLIENT_KEY);
        SysClient client = null;
        if (StringUtils.isNotBlank(clientId)) {
            client = clientService.getById(clientId);
        }

        String address = AddressUtils.getRealAddressByIP(ip);
        String s = getBlock(ip) +
                address +
                getBlock(logininforEvent.getUsername()) +
                getBlock(logininforEvent.getStatus()) +
                getBlock(logininforEvent.getMessage());
        // 打印信息到日志
        log.info(s, logininforEvent.getArgs());
        // 获取客户端操作系统
        String os = userAgent.getOs().getName();
        // 获取客户端浏览器
        String browser = userAgent.getBrowser().getName();
        // 封装对象
        SysLogininforAddBo logininfor = new SysLogininforAddBo();
        logininfor.setTenantId(logininforEvent.getTenantId());
        logininfor.setUserName(logininforEvent.getUsername());
        if (ObjectUtil.isNotNull(client)) {
            logininfor.setClientKey(client.getClientKey());
            logininfor.setDeviceType(client.getDeviceType());
        }
        logininfor.setIpaddr(ip);
        logininfor.setLoginLocation(address);
        logininfor.setBrowser(browser);
        logininfor.setOs(os);
        logininfor.setMsg(logininforEvent.getMessage());
        // 日志状态
        if (StringUtils.equalsAny(logininforEvent.getStatus(), Constants.LOGIN_SUCCESS, Constants.LOGOUT, Constants.REGISTER)) {
            logininfor.setStatus(Constants.SUCCESS);
        } else if (Constants.LOGIN_FAIL.equals(logininforEvent.getStatus())) {
            logininfor.setStatus(Constants.FAIL);
        }
        logininfor.setLoginTime(LocalDateTime.now());
        // 插入数据
        TenantHelper.dynamic(logininforEvent.getTenantId(), () -> add(logininfor));//支持注册时的动态多租户
    }

    private String getBlock(Object msg) {
        if (msg == null) {
            msg = "";
        }
        return "[" + msg + "]";
    }

}
