/**
 * Zod 验证枚举
 * 只包含枚举定义，验证逻辑已移动到 utils/validators 目录
 */

import { BaseSchema, ValidationSchemas } from '#/utils/validators/schemas';

// 枚举式 Zod 模式集合
export const ZodEnum = {
  STRING: BaseSchema.STRING,
  EMAIL: BaseSchema.EMAIL,
  EMAIL_EMPTY: optional(BaseSchema.EMAIL),
  PHONE: BaseSchema.PHONE,
  PHONE_EMPTY: optional(BaseSchema.PHONE),
  /**
   * 公安机关机构代码
   */
  GAJGJGDM: BaseSchema.GAJGJGDM,
  /**
   * 公安机关机构代码，可为空
   */
  GAJGJGDM_EMPTY: optional(BaseSchema.GAJGJGDM),
  /**
   * 公民身份号码（身份证号码）
   */
  ID_CARD: BaseSchema.ID_CARD,
  /**
   * 公民身份号码，可为空
   */
  ID_CARD_EMPTY: optional(BaseSchema.ID_CARD),
  /**
   * 中文姓名
   */
  CHINESE_NAME: BaseSchema.CHINESE_NAME,
  /**
   * 中文姓名，可为空
   */
  CHINESE_NAME_EMPTY: optional(BaseSchema.CHINESE_NAME),
} as const;
