import type {
  ConfigForm,
  ConfigQuery,
  ConfigVO,
} from '#/api/system/config/types';

import { requestClient } from '#/api/request';

/**
 * 查询参数配置列表
 * @param query
 */
export const listConfig = (query?: ConfigQuery) => {
  return requestClient.get<PageData<ConfigVO>>('/system/config/list', query);
};

/**
 * 查询参数配置详细
 * @param id
 */
export const getConfig = (id: number | string) => {
  return requestClient.get<ConfigVO>(`/system/config/${id}`);
};

/**
 * 新增参数配置
 * @param data
 */
export const addConfig = (data: ConfigForm) => {
  return requestClient.post('/system/config', data);
};

/**
 * 修改参数配置
 * @param data
 */
export const editConfig = (data: ConfigForm) => {
  return requestClient.put('/system/config', data);
};

/**
 * 删除参数配置权限
 * @param id
 */
export const removeConfig = (id: Array<number | string> | number | string) => {
  return requestClient.delete(`/system/config/${id}`);
};

/**
 * 刷新参数缓存
 */
export const refreshCache = () => {
  return requestClient.delete('/system/config/refreshCache');
};
