<script lang="ts" setup>
import { Page } from '@vben/common-ui';

import { use${BusinessName}Service } from './service';

defineOptions({
  name: '${BusinessName}Index',
});

const { Form, BasicTable, FormDialog, useToolbarButtons, useRowButtons } =
  use${BusinessName}Service();
</script>

<template>
  <Page auto-content-height>
    <FormDialog class="#if(${editColumns} == 1)w-[500px]#else w-[1024px]#end" title="${functionName}管理">
      <Form />
    </FormDialog>

    <BasicTable>
      <template #toolbar-actions>
        <ActionButtonGroup :buttons="useToolbarButtons()" />
      </template>

      <template #action="{ row }">
        <ActionButtonGroup :buttons="useRowButtons()" :row="row" />
      </template>
    </BasicTable>
  </Page>
</template>
