import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { MenuDataOption } from '#/api/system/menu/types';
import type {
  TenantPackageForm,
  TenantPackageVO,
} from '#/api/system/tenant-package/types';
import type { ButtonConfig } from '#/components/action-button-group/types';

import { useVbenDrawer, useVbenForm } from '@vben/common-ui';
import { Delete, Edit, Plus } from '@vben/icons';

import { useVbenVxeGrid, vxeCheckboxChecked } from '#/adapter/vxe-table';
import { listMenuOption } from '#/api/system/menu';
import {
  addTenantPackage,
  changePackageStatus,
  editTenantPackage,
  getTenantPackage,
  listTenantPackage,
  removeTenantPackage,
} from '#/api/system/tenant-package';
import MenuPermission from '#/components/system/menu-permission/index.vue';

import { useFormSchema, useGridColumns, useGridFormSchema } from './data';

export const useTenantPackageService = () => {
  const menuPermissionRef = ref<InstanceType<typeof MenuPermission>>();
  const menuTree = ref<MenuDataOption[]>([]);
  // 查询表单和表格配置
  const [BasicTable, gridApi] = useVbenVxeGrid({
    formOptions: {
      schema: useGridFormSchema(),
      commonConfig: {
        labelWidth: 120,
        componentProps: {
          allowClear: true,
        },
      },
    },
    gridOptions: {
      columns: useGridColumns(),
      pagerConfig: { enabled: true },
      proxyConfig: {
        ajax: {
          query: async (_, formValues = {}) => {
            return await listTenantPackage({
              ...formValues,
            });
          },
        },
      },
    } as VxeTableGridOptions,
  });

  // 编辑表单配置
  const [Form, formApi] = useVbenForm({
    schema: useFormSchema(),
    commonConfig: {
      labelWidth: 80,
    },
    showDefaultActions: false,
    wrapperClass: 'md:grid-cols-1',
  });

  // 编辑抽屉配置
  const [FormDialog, formDialogApi] = useVbenDrawer({
    async onConfirm() {
      const { valid } = await formApi.validate();
      if (!valid) return;
      const data = await formApi.getValues<TenantPackageForm>();
      // 这个用于提交
      data.menuIds = menuPermissionRef.value?.getCheckedKeys?.() ?? [];
      formDialogApi.lock();
      (data?.id ? editTenantPackage(data) : addTenantPackage(data))
        .then(() => {
          onRefresh();
          formDialogApi.close();
        })
        .catch(() => {
          formDialogApi.unlock();
        });
    },
    async onOpenChange(isOpen) {
      if (isOpen) {
        const data = formDialogApi.getData<TenantPackageForm>();
        if (data) {
          formApi.setValues(data).then(() => {});
        } else {
          formApi.resetForm().then(() => {});
        }

        const id = data.id;
        // 设置菜单信息
        menuTree.value = await listMenuOption({ isTree: true });
        const menuIds = id ? data.menuIds : [];
        await nextTick();
        await formApi.setFieldValue('menuIds', menuIds);
      }
    },
    destroyOnClose: true,
  });

  /**
   * 刷新表格
   */
  const onRefresh = async () => {
    await gridApi.query();
  };

  /**
   * 创建租户套餐
   */
  const onCreate = () => {
    formDialogApi.setData(null).open();
  };

  /**
   * 编辑租户套餐
   */
  const handleEdit = async (row: TenantPackageVO) => {
    const data = await getTenantPackage(row.id);
    formDialogApi.setData(data).open();
  };

  /**
   * 删除租户套餐
   */
  const handleDelete = async (row: TenantPackageVO) => {
    await ElMessageBox.confirm('确认删除该租户套餐吗？', '提示', {
      type: 'warning',
    })
      .then(async () => {
        await removeTenantPackage(row.id).then(() => {
          onRefresh();
        });
      })
      .catch(() => {});
  };

  /**
   * 批量删除
   */
  const handleMultipleDelete = async function () {
    const rows = gridApi.grid.getCheckboxRecords();
    const ids = rows.map((row: TenantPackageVO) => row.id);

    await window.skynet
      .confirm('是否要删除选中的套餐信息?')
      .then(async () => {
        await removeTenantPackage(ids);
        await onRefresh();
        await gridApi.grid.clearCheckboxRow();
      })
      .catch(() => {});
  };

  // 租户套餐状态修改
  const handleStatusChange = async (row: TenantPackageVO) => {
    const text = row.status === '0' ? '启用' : '停用';
    await window.skynet
      .confirm(`确认要"${text}""${row.packageName}"用户吗?`)
      .then(async () => {
        await changePackageStatus(row).then(() => onRefresh());
      })
      .catch(() => {
        row.status = row.status === '0' ? '1' : '0';
      });
  };

  /**
   * 通过回调更新 无法通过v-model
   * @param value 菜单选择是否严格模式
   */
  function handleMenuCheckStrictlyChange(value: boolean) {
    formApi.setFieldValue('menuCheckStrictly', value).then();
  }

  /**
   * 工具栏按钮配置
   */
  const useToolbarButtons = (): ButtonConfig[] => {
    return [
      {
        label: '新增租户套餐',
        type: 'primary',
        icon: Plus,
        onClick: onCreate,
        auth: ['system:tenantPackage:add'],
      },
      {
        label: '删除',
        icon: Delete,
        onClick: handleMultipleDelete,
        auth: ['system:tenantPackage:remove'],
        disabled: !vxeCheckboxChecked(gridApi),
      },
    ];
  };

  /**
   * 行操作按钮配置
   */
  const useRowButtons = (): ButtonConfig[] => {
    return [
      {
        label: '编辑',
        icon: Edit,
        link: true,
        onClick: handleEdit,
        auth: ['system:tenantPackage:edit'],
      },
      {
        label: '删除',
        icon: Delete,
        link: true,
        onClick: handleDelete,
        auth: ['system:tenantPackage:remove'],
      },
    ];
  };

  return {
    BasicTable,
    Form,
    FormDialog,
    handleDelete,
    handleEdit,
    handleStatusChange,
    onCreate,
    onRefresh,
    useToolbarButtons,
    useRowButtons,
    handleMenuCheckStrictlyChange,
    menuPermissionRef,
    formApi,
    menuTree,
  };
};
