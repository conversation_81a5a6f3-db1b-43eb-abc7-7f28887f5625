// 测试一致的包装器实现
import { BaseSchema, withNoFullWidth } from './zod-enum';
import { z } from '#/adapter/form';

console.log('=== 测试一致的包装器实现 ===');

// 1. 测试 GAJGJGDM 全角字符优先级
console.log('\n1. GAJGJGDM 全角字符测试:');
const gajgjgdmFullWidth = BaseSchema.GAJGJGDM.safeParse('１２３４５６７８９０１２');
console.log('GAJGJGDM 全角数字结果:', gajgjgdmFullWidth);
if (!gajgjgdmFullWidth.success) {
  console.log('GAJGJGDM 全角数字错误信息:', gajgjgdmFullWidth.error.issues[0].message);
  console.log('期望: "不能包含全角字符，请使用半角字符"');
}

// 2. 测试 GAJGJGDM 长度错误
console.log('\n2. GAJGJGDM 长度错误测试:');
const gajgjgdmLength = BaseSchema.GAJGJGDM.safeParse('12345');
console.log('GAJGJGDM 长度错误结果:', gajgjgdmLength);
if (!gajgjgdmLength.success) {
  console.log('GAJGJGDM 长度错误信息:', gajgjgdmLength.error.issues[0].message);
  console.log('期望: "请输入12位数字"');
}

// 3. 测试 EMAIL 全角字符
console.log('\n3. EMAIL 全角字符测试:');
const emailFullWidth = BaseSchema.EMAIL.safeParse('测试@example.com');
console.log('EMAIL 全角字符结果:', emailFullWidth);
if (!emailFullWidth.success) {
  console.log('EMAIL 全角字符错误信息:', emailFullWidth.error.issues[0].message);
  console.log('期望: "不能包含全角字符，请使用半角字符"');
}

// 4. 测试 EMAIL 格式错误
console.log('\n4. EMAIL 格式错误测试:');
const emailFormat = BaseSchema.EMAIL.safeParse('invalid-email');
console.log('EMAIL 格式错误结果:', emailFormat);
if (!emailFormat.success) {
  console.log('EMAIL 格式错误信息:', emailFormat.error.issues[0].message);
  console.log('期望: "请输入正确的邮箱"');
}

// 5. 测试 PHONE 全角字符
console.log('\n5. PHONE 全角字符测试:');
const phoneFullWidth = BaseSchema.PHONE.safeParse('１３８１２３４５６７８');
console.log('PHONE 全角字符结果:', phoneFullWidth);
if (!phoneFullWidth.success) {
  console.log('PHONE 全角字符错误信息:', phoneFullWidth.error.issues[0].message);
  console.log('期望: "不能包含全角字符，请使用半角字符"');
}

// 6. 测试 PHONE 格式错误
console.log('\n6. PHONE 格式错误测试:');
const phoneFormat = BaseSchema.PHONE.safeParse('12345678901');
console.log('PHONE 格式错误结果:', phoneFormat);
if (!phoneFormat.success) {
  console.log('PHONE 格式错误信息:', phoneFormat.error.issues[0].message);
  console.log('期望: "请输入正确的手机号"');
}

// 7. 测试自定义包装器
console.log('\n7. 自定义包装器测试:');
const customSchema = withNoFullWidth(
  z.string().min(5, '至少5个字符'),
  '自定义：不能包含全角字符'
);

const customFullWidth = customSchema.safeParse('你好');
console.log('自定义全角字符结果:', customFullWidth);
if (!customFullWidth.success) {
  console.log('自定义全角字符错误信息:', customFullWidth.error.issues[0].message);
  console.log('期望: "自定义：不能包含全角字符"');
}

const customLength = customSchema.safeParse('abc');
console.log('自定义长度错误结果:', customLength);
if (!customLength.success) {
  console.log('自定义长度错误信息:', customLength.error.issues[0].message);
  console.log('期望: "至少5个字符"');
}

// 8. 测试正确情况
console.log('\n8. 正确情况测试:');
console.log('GAJGJGDM 正确:', BaseSchema.GAJGJGDM.safeParse('123456789012'));
console.log('EMAIL 正确:', BaseSchema.EMAIL.safeParse('<EMAIL>'));
console.log('PHONE 正确:', BaseSchema.PHONE.safeParse('13812345678'));
console.log('STRING 正确:', BaseSchema.STRING.safeParse('hello'));

export {
  gajgjgdmFullWidth,
  gajgjgdmLength,
  emailFullWidth,
  emailFormat,
  phoneFullWidth,
  phoneFormat,
  customSchema
};
