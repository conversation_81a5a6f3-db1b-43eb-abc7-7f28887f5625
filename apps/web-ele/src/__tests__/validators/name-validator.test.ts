// 姓名验证器测试
import {
  validateChineseName,
  validateChineseChars,
  validateChineseDot,
  validateForbiddenChars,
  validateNameLength,
  parseNameInfo,
  formatName,
} from '@/utils/validators';

describe('姓名验证器测试', () => {
  describe('validateChineseName', () => {
    test('有效的中文姓名', () => {
      const validNames = [
        '张三',
        '李四',
        '王小明',
        '赵钱孙李',
        '欧阳修',
        '司马懿',
        '诸葛亮',
        '上官婉儿',
      ];

      validNames.forEach(name => {
        const result = validateChineseName(name);
        expect(result.isValid).toBe(true);
      });
    });

    test('有效的带中文点的姓名', () => {
      const validNames = [
        '爱新觉罗·玄烨',
        '叶赫那拉·慧征',
        '博尔济吉特·布木布泰',
        '钮祜禄·甄嬛',
      ];

      validNames.forEach(name => {
        const result = validateChineseName(name);
        expect(result.isValid).toBe(true);
      });
    });

    test('无效的姓名 - 长度错误', () => {
      const invalidNames = [
        '', // 空字符串
        '李', // 太短
        '这是一个非常非常长的姓名超过了限制', // 太长
      ];

      invalidNames.forEach(name => {
        const result = validateChineseName(name);
        expect(result.isValid).toBe(false);
        expect(['empty', 'length']).toContain(result.errorType);
      });
    });

    test('无效的姓名 - 包含英文字符', () => {
      const invalidNames = [
        '张San',
        'Zhang三',
        '李4',
        '王小明123',
      ];

      invalidNames.forEach(name => {
        const result = validateChineseName(name);
        expect(result.isValid).toBe(false);
        expect(result.errorType).toBe('format');
      });
    });

    test('无效的姓名 - 包含禁用字符', () => {
      const invalidNames = [
        '张【三】',
        '李、四',
        '王﹒小明',
        '赵▪钱',
        '孙•李',
      ];

      invalidNames.forEach(name => {
        const result = validateChineseName(name);
        expect(result.isValid).toBe(false);
        expect(result.errorType).toBe('forbiddenChars');
      });
    });

    test('无效的姓名 - 中文点使用错误', () => {
      const invalidNames = [
        '·张三', // 开头有中文点
        '张三·', // 结尾有中文点
        '张·三·四', // 多个中文点
        '·', // 只有中文点
        'a·b', // 中文点前后不是中文
      ];

      invalidNames.forEach(name => {
        const result = validateChineseName(name);
        expect(result.isValid).toBe(false);
        expect(['format', 'invalidDot']).toContain(result.errorType);
      });
    });
  });

  describe('validateChineseDot', () => {
    test('正确使用中文点', () => {
      const validNames = [
        '爱新觉罗·玄烨',
        '叶赫那拉·慧征',
        '张三', // 没有中文点也是有效的
      ];

      validNames.forEach(name => {
        expect(validateChineseDot(name)).toBe(true);
      });
    });

    test('错误使用中文点', () => {
      const invalidNames = [
        '·张三', // 开头
        '张三·', // 结尾
        '张·三·四', // 多个
        '·', // 只有中文点
      ];

      invalidNames.forEach(name => {
        expect(validateChineseDot(name)).toBe(false);
      });
    });
  });

  describe('validateForbiddenChars', () => {
    test('不包含禁用字符', () => {
      const validNames = [
        '张三',
        '爱新觉罗·玄烨',
        '王小明',
      ];

      validNames.forEach(name => {
        expect(validateForbiddenChars(name)).toBe(true);
      });
    });

    test('包含禁用字符', () => {
      const invalidNames = [
        '张【三】',
        '李、四',
        '王﹒小明',
        '赵▪钱',
        '孙•李',
      ];

      invalidNames.forEach(name => {
        expect(validateForbiddenChars(name)).toBe(false);
      });
    });
  });

  describe('parseNameInfo', () => {
    test('解析普通中文姓名', () => {
      const name = '张三';
      const info = parseNameInfo(name);

      expect(info).not.toBeNull();
      expect(info?.fullName).toBe('张三');
      expect(info?.firstName).toBe('张');
      expect(info?.lastName).toBe('三');
      expect(info?.hasChineseDot).toBe(false);
      expect(info?.length).toBe(2);
    });

    test('解析带中文点的姓名', () => {
      const name = '爱新觉罗·玄烨';
      const info = parseNameInfo(name);

      expect(info).not.toBeNull();
      expect(info?.fullName).toBe('爱新觉罗·玄烨');
      expect(info?.firstName).toBe('爱新觉罗');
      expect(info?.lastName).toBe('玄烨');
      expect(info?.hasChineseDot).toBe(true);
      expect(info?.length).toBe(7);
    });

    test('解析复合姓名', () => {
      const name = '欧阳修';
      const info = parseNameInfo(name);

      expect(info).not.toBeNull();
      expect(info?.fullName).toBe('欧阳修');
      expect(info?.firstName).toBe('欧');
      expect(info?.lastName).toBe('阳修');
      expect(info?.hasChineseDot).toBe(false);
      expect(info?.length).toBe(3);
    });

    test('解析无效姓名返回null', () => {
      const invalidNames = ['', 'Zhang三', '张【三】'];

      invalidNames.forEach(name => {
        const info = parseNameInfo(name);
        expect(info).toBeNull();
      });
    });
  });

  describe('formatName', () => {
    test('格式化姓名', () => {
      expect(formatName('  张三  ')).toBe('张三');
      expect(formatName('张 三')).toBe('张三');
      expect(formatName('  张  三  ')).toBe('张三');
      expect(formatName('')).toBe('');
    });
  });

  describe('边界情况测试', () => {
    test('自定义长度限制', () => {
      const options = { minLength: 3, maxLength: 5 };
      
      expect(validateChineseName('张三', options).isValid).toBe(false); // 太短
      expect(validateChineseName('张三李', options).isValid).toBe(true); // 正好
      expect(validateChineseName('张三李四王五', options).isValid).toBe(true); // 正好
      expect(validateChineseName('张三李四王五赵', options).isValid).toBe(false); // 太长
    });

    test('允许空值选项', () => {
      const options = { allowEmpty: true };
      
      expect(validateChineseName('', options).isValid).toBe(true);
      expect(validateChineseName('   ', options).isValid).toBe(true);
    });

    test('特殊中文字符', () => {
      const specialNames = [
        '龘龘', // 特殊汉字
        '㐀㐁', // 扩展汉字
      ];

      specialNames.forEach(name => {
        const result = validateChineseName(name);
        expect(result.isValid).toBe(true);
      });
    });
  });
});

export {};
