package com.ruoyi.system.domain.po;

import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import com.ruoyi.common.orm.core.domain.BaseEntity;

/**
 * 通知公告对象 sys_notice
 *
 * <AUTHOR>
 * @since 2025-05-10 18:01:12
 */
@Data
@Table(value = "sys_notice")
public class SysNotice extends BaseEntity {

    /** 公告ID */
    @Id
    private String id;

    /** 公告标题 */
    private String title;

    /** 公告类型（1通知 2公告） */
    private String type;

    /** 公告内容 */
    private String content;

    /** 公告状态（0正常 1关闭） */
    private String status;

    /** 备注 */
    private String remark;


}
