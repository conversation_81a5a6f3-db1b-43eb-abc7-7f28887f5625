<script lang="ts" setup>
import type { VxeTableGridOptions } from '#/adapter/vxe-table';

import { useVbenModal } from '@vben/common-ui';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { authUserSelectAll, unallocatedUserList } from '#/api/system/role';
import {
  useAddUserGridColumns,
  useGridFormSchema,
} from '#/views/system/role-assign/data';

const emit = defineEmits<{ reload: [] }>();
const route = useRoute();
const roleId = route.params.roleId as string;
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    schema: useGridFormSchema(),
    commonConfig: {
      componentProps: {
        allowClear: true,
      },
    },
  },
  gridOptions: {
    columns: useAddUserGridColumns(),
    pagerConfig: { enabled: true },
    proxyConfig: {
      ajax: {
        query: async ({ page }, formValues) => {
          return await unallocatedUserList({
            pageNum: page.currentPage,
            pageSize: page.pageSize,
            ...formValues,
            roleId,
          });
        },
      },
    },
  } as VxeTableGridOptions,
});

const [Modal, modalApi] = useVbenModal({
  async onConfirm() {
    const records = gridApi.grid.getCheckboxRecords();
    const userIds = records.map((item) => item.id);
    if (userIds.length === 0) {
      await window.skynet.alertError('请选择要添加的用户');
      return;
    }
    modalApi.lock();
    await authUserSelectAll({ roleId, userIds })
      .then(() => {
        emit('reload');
        modalApi.close();
      })
      .catch(() => {
        modalApi.unlock();
      });
  },
});
defineExpose(modalApi);
</script>
<template>
  <div>
    <Modal class="h-[750px] w-[60%]" title="添加用户">
      <Grid />
    </Modal>
  </div>
</template>
