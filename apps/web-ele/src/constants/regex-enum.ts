export const RegexEnum = {
  /**
   * 手机号码
   */
  MOBILE: /^1[3-9]\d{9}$/,
  /**
   * 身份证号码基础格式验证
   * 18位：前17位数字，最后一位数字或X（大小写不敏感）
   */
  ID_CARD_BASIC: /^\d{17}[\dX]$/i,
  /**
   * 全角字符检测正则表达式
   * 包括：全角空格(\u3000)、全角标点符号(\uFF00-\uFFEF)、中日韩字符(\u4E00-\u9FFF)、
   * 平假名(\u3040-\u309F)、片假名(\u30A0-\u30FF)等
   */
  FULL_WIDTH_REGEX:
    /[\u3000\uFF00-\uFFEF\u4E00-\u9FFF\u3040-\u309F\u30A0-\u30FF]/,
  /**
   * 中文姓名验证正则表达式
   * 支持纯中文字符或中文字符+中文点+中文字符的组合
   * 优化后避免 ESLint 重复匹配警告
   */
  CHINESE_NAME: /^[\u0391-\uFFE5]+(?:·[\u0391-\uFFE5]+)?$/,
  /**
   * 禁用字符检测正则表达式
   * 不允许包含：【】、﹒▪• 等特殊符号
   */
  FORBIDDEN_CHARS: /^[^【】、﹒▪•]+$/,
} as const;

export type RegexEnumKey = keyof typeof RegexEnum;
