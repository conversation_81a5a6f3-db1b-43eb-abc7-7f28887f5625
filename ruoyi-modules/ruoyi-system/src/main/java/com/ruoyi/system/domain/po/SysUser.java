package com.ruoyi.system.domain.po;

import java.time.LocalDateTime;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import com.ruoyi.common.orm.core.domain.BaseEntity;

/**
 * 用户信息对象 sys_user
 *
 * <AUTHOR>
 * @since 2025-04-29 14:00:02
 */
@Data
@Table(value = "sys_user")
public class SysUser extends BaseEntity {

    /** 用户ID */
    @Id
    private String id;

    /** 部门ID */
    private String deptId;

    /** 用户账号 */
    private String userName;

    /** 用户昵称 */
    private String nickName;

    /** 用户类型（sys_user系统用户） */
    private String userType;

    /** 用户邮箱 */
    private String email;

    /** 手机号码 */
    private String phonenumber;

    /** 用户性别（0男 1女 2未知） */
    private String gender;

    /** 头像地址 */
    private String avatar;

    /** 密码 */
    private String password;

    /** 帐号状态（0正常 1停用） */
    private String status;

    /** 删除标志（1代表存在 0代表删除） */
    @Column(isLogicDelete = true)
    private Integer jlzt;

    /** 最后登陆IP */
    private String loginIp;

    /** 最后登陆时间 */
    private LocalDateTime loginDate;

    /** 备注 */
    private String remark;

}
