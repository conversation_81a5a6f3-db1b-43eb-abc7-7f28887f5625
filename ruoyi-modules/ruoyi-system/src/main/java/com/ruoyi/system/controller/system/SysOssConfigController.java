package com.ruoyi.system.controller.system;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.ruoyi.common.core.core.domain.PageData;
import com.ruoyi.common.core.core.domain.Result;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.web.annotation.RepeatSubmit;
import com.ruoyi.common.web.core.BaseController;
import com.ruoyi.system.domain.bo.sysossconfig.SysOssConfigAddBo;
import com.ruoyi.system.domain.bo.sysossconfig.SysOssConfigEditBo;
import com.ruoyi.system.domain.bo.sysossconfig.SysOssConfigListBo;
import com.ruoyi.system.domain.vo.sysossconfig.SysOssConfigDetailVo;
import com.ruoyi.system.domain.vo.sysossconfig.SysOssConfigListVo;
import com.ruoyi.system.service.SysOssConfigService;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 对象存储配置控制器
 * <p>
 * 提供对象存储配置地增删改查、导入导出等功能，主要包含：
 * <ul>
 *   <li>对象存储配置地增删改查</li>
 *   <li>对象存储配置的分页查询</li>
 *   <li>对象存储配置的下拉选择</li>
 * </ul>
 *
 * <AUTHOR>
 * @since 2025-05-08 21:55:46
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/resource/oss/config")
public class SysOssConfigController extends BaseController {
    @Resource
    private SysOssConfigService sysOssConfigService;

    /**
     * 分页查询对象存储配置列表
     * <p>
     * 支持多条件组合查询，返回分页数据
     *
     * @param sysOssConfigListBo 查询条件对象，包含分页参数和查询条件
     * @return 包含分页信息和对象存储配置列表的结果对象
     */
    @SaCheckPermission("system:ossConfig:list")
    @GetMapping("/list")
    public Result<PageData<SysOssConfigListVo>> page(SysOssConfigListBo sysOssConfigListBo) {
        return sysOssConfigService.page(sysOssConfigListBo);
    }



    /**
     * 新增对象存储配置
     * <p>
     * 包含数据校验和重复提交控制，确保数据的完整性和一致性
     *
     * @param sysOssConfigAddBo 对象存储配置对象，包含必填字段验证
     * @return 操作结果，成功或失败信息
     */
    @SaCheckPermission("system:ossConfig:add")
    @Log(title = "对象存储配置", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping
    public Result<Void> add(@Validated @RequestBody SysOssConfigAddBo sysOssConfigAddBo) {
        return sysOssConfigService.add(sysOssConfigAddBo);
    }

    /**
     * 修改对象存储配置
     * <p>
     * 包含数据校验和重复提交控制，确保数据的完整性和一致性
     *
     * @param sysOssConfigEditBo 对象存储配置对象，包含必填字段验证
     * @return 操作结果，成功或失败信息
     */
    @SaCheckPermission("system:ossConfig:edit")
    @Log(title = "对象存储配置", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping
    public Result<Void> edit(@Validated @RequestBody SysOssConfigEditBo sysOssConfigEditBo) {
        return sysOssConfigService.edit(sysOssConfigEditBo);
    }

    /**
     * 删除对象存储配置
     * <p>
     * 支持批量删除操作，可同时删除多个对象存储配置
     *
     * @param ids 对象存储配置ID数组，包含要删除的ID列表
     * @return 操作结果，成功或失败信息
     */
    @SaCheckPermission("system:ossConfig:remove")
    @Log(title = "对象存储配置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result<Void> remove(@PathVariable String[] ids) {
        return sysOssConfigService.removeByIds(ids);
    }

    /**
     * 获取对象存储配置详细信息
     * <p>
     * 根据ID查询单个对象存储配置的完整信息，包含所有字段
     *
     * @param id 对象存储配置ID
     * @return 包含对象存储配置所有字段的详细信息对象
     */
    @SaCheckPermission("system:ossConfig:query")
    @GetMapping(value = "/{id}")
    public Result<SysOssConfigDetailVo> detail(@PathVariable String id) {
        return Result.success(sysOssConfigService.detail(id));
    }
    /**
     * 状态修改
     */
    @SaCheckPermission("system:ossConfig:edit")
    @Log(title = "对象存储状态修改", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public Result<Void> changeStatus(@RequestBody SysOssConfigEditBo bo) {
        return sysOssConfigService.updateOssConfigStatus(bo);
    }

}
