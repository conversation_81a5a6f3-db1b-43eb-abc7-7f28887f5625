package com.ruoyi.safetyoutline.domain.vo.syslevel3protectconfig;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 三级等保配置信息视图对象 sys_level3protect_config
 *
 * <AUTHOR>
 * @date 2025-01-13 15:56:35
 */
@Data
public class SysLevel3protectConfigListVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private String id;
    /**
     * 配置双因子登录模式(0关闭1开启)
     */
    private String pzsyzdlmsbz;
    /**
     * 最大连续登录失败次数
     */
    private Integer zdlxdlsbcs;
    /**
     * 连续登录失败锁定分钟
     */
    private Integer lxdlsbsdsj;
    /**
     * 登录后无操作自动退出的分钟
     */
    private Integer dlwczzdtcsj;
    /**
     * 开启密码复杂度(0关闭1开启)
     */
    private String kqmmfzdbz;
    /**
     * 定期修改密码时间间隔
     */
    private Integer dqxgmmsjjg;
    /**
     * 定期修改密码不允许重复次数
     */
    private Integer dqxgmmbxxcfcs;
    /**
     * 文件安全检测(0关闭1开启)
     */
    private String wjaqjcbz;
    /**
     * 上传文件大小限制
     */
    private Integer scwjdxxz;

    /**
     * 创建时间
     */
    private Date createTime;
}
