<script lang="ts" setup>
import { Page } from '@vben/common-ui';

import Form from '#/views/system/notice/form.vue';

import { useNoticeService } from './service';

defineOptions({
  name: 'NoticeIndex',
});

const { formRef, BasicTable, handleQuery, useToolbarButtons, useRowButtons } =
  useNoticeService();
</script>

<template>
  <Page auto-content-height>
    <BasicTable>
      <template #toolbar-actions>
        <ActionButtonGroup :buttons="useToolbarButtons()" />
      </template>

      <template #action="{ row }">
        <ActionButtonGroup :buttons="useRowButtons()" :row="row" />
      </template>
    </BasicTable>
    <Form ref="formRef" @reload="handleQuery()" />
  </Page>
</template>
