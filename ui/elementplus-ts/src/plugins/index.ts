import tab from './tab';
import download from './download';
import cache from './cache';
import auth from './auth';
import { download as dl } from '@/utils/http/com';
// 预设动画
import animate from '@/animate';
import { useDict } from '@/utils/dict';
import { getConfigKey, updateConfigByKey } from '@/api/system/config';
import { addDateRange, handleTree, parseTime, selectDictLabel, selectDictLabels } from '@/utils/ruoyi';
import { alert, alertError, alertWarning, skConfirm } from '@/utils/message';

import type { App } from 'vue';
import { endLoading, startLoading } from '@/components/Loading/fullScreen';

export default function installPlugin(app: App) {
  // 全局方法挂载
  app.config.globalProperties.download = dl;

  //通过window挂载常用全局变量
  window.skynet = {
    tab: tab,
    cache: cache,
    download: dl,
    downloadOss: download.oss,
    downloadZip: download.zip,
    auth: auth,
    useDict: useDict,
    getConfigKey: getConfigKey,
    updateConfigByKey: updateConfigByKey,
    parseTime: parseTime,
    handleTree: handleTree,
    addDateRange: addDateRange,
    selectDictLabel: selectDictLabel,
    selectDictLabels: selectDictLabels,
    animate: animate,
    alert: alert,
    alertWarning: alertWarning,
    alertError: alertError,
    confirm: skConfirm,
    loading: startLoading,
    closeLoading: endLoading
  };
}
