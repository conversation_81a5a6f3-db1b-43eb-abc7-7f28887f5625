import type { ElMessageBoxOptions } from 'element-plus';

import type { Component } from 'vue';

export interface ButtonConfig {
  /** 按钮文本 */
  label: string;
  /** 按钮图标 */
  icon?: Component;
  /** 按钮类型 */
  type?: 'danger' | 'info' | 'primary' | 'success' | 'warning';
  /** 是否为链接按钮 */
  link?: boolean;
  /** 是否禁用 */
  disabled?: boolean;
  /** 是否显示 */
  show?: ((row?: any) => boolean) | boolean;
  /** 按钮提示文本 */
  tooltip?: string;
  /** 权限编码控制是否显示 */
  auth?: string[];
  /** 确认对话框配置 */
  confirm?: {
    /** 取消按钮文本 */
    cancelButtonText?: string;
    /** 确认按钮文本 */
    confirmButtonText?: string;
    /** 确认对话框内容 */
    content?: string;
    /** 确认对话框标题 */
    title?: string;
    /** 确认按钮类型 */
    type?: 'error' | 'info' | 'success' | 'warning';
  } & Partial<ElMessageBoxOptions>;
  /** 点击事件处理函数 */
  onClick?: (row?: any, event?: MouseEvent) => Promise<void> | void;
}
