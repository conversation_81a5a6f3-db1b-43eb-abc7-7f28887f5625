<script setup lang="ts" name="Role">
import { listRole } from '@/api/system/role';
import { useRole } from '@/views/system/role/utils';

const { skTable, columns, handleStatusChange, handleAuthUser, handleExport, openDialog, openDataScopeDialog, handleDelete } =
  useRole();
</script>
<template>
  <div class="table-box">
    <SkTable ref="skTable" :columns="columns" :request-api="listRole" :search-col="{ xs: 1, sm: 1, md: 2, lg: 3, xl: 3 }">
      <!-- 表格 header 按钮 -->
      <template #tableHeader>
        <el-button v-hasPermi="['system:role:add']" type="primary" icon="CirclePlus" @click="openDialog('新增')">
          新增
        </el-button>
        <el-button v-hasPermi="['system:role:export']" type="primary" plain icon="Download" @click="handleExport()">
          导出
        </el-button>
      </template>
      <!-- 表格操作 -->
      <template #operation="scope">
        <div v-if="scope.row.id !== 'f79b0d5389bd4d2699e7141f13e35429'">
          <el-button v-hasPermi="['system:role:edit']" type="primary" link icon="EditPen" @click="openDialog('编辑', scope.row)"
            >编辑
          </el-button>
          <el-button
            v-hasPermi="['system:role:edit']"
            type="primary"
            link
            icon="CircleCheck"
            @click="openDataScopeDialog('数据权限', scope.row)"
            >数据权限
          </el-button>
          <el-button v-hasPermi="['system:role:edit']" type="primary" link icon="User" @click="handleAuthUser(scope.row)"
            >分配用户
          </el-button>
          <el-button v-hasPermi="['system:role:remove']" type="danger" link icon="Delete" @click="handleDelete(scope.row)"
            >删除
          </el-button>
        </div>
        <div v-else class="table-empty text-muted">暂无权限操作</div>
      </template>
      <template #status="scope">
        <el-switch
          v-model="scope.row.status"
          active-value="0"
          inactive-value="1"
          @click="handleStatusChange(scope.row)"
        ></el-switch>
      </template>
    </SkTable>
  </div>
</template>
