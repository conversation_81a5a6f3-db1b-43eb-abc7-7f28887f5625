package com.ruoyi.system.domain.vo.sysoss;

import lombok.Data;
import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * OSS对象存储视图对象 sys_oss
 *
 * <AUTHOR>
 * @since 2025-05-06 15:53:19
 */
@Data
public class SysOssListVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 对象存储主键 */
    private String id;
    /** 文件名 */
    private String fileName;
    /** 原名 */
    private String originalName;
    /** 扩展名 */
    private String fileSuffix;
    /** 文件预览 */
    private String url;
    /** 服务商 */
    private String service;
    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" )
    private LocalDateTime createTime;
}
