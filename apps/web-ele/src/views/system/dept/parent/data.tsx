import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { VbenFormSchema } from '#/adapter/form';

import { renderDictTag } from '#/utils/dict';

/**
 * 获取编辑表单的字段配置
 * @returns VbenFormSchema[] 表单配置
 */
export function useFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      dependencies: {
        show: () => false,
        triggerFields: [''],
      },
      fieldName: 'id',
      label: '部门ID',
    },
    {
      component: 'Input',
      dependencies: {
        show: () => false,
        triggerFields: [''],
      },
      fieldName: 'version',
      label: '乐观锁版本号',
    },
    {
      component: 'Input',
      fieldName: 'deptId',
      label: '子部门ID',
      dependencies: {
        show: () => false,
        triggerFields: [''],
      },
    },
    {
      component: 'SkDict',
      fieldName: 'jzfldm',
      label: '警种分类',
      rules: 'required',
      componentProps: {
        data: 'jzfl',
        placeholder: '请选择警种分类',
        clearable: true,
        in: '03,38,96',
      },
    },
    {
      component: 'SkDept',
      fieldName: 'parentId',
      label: '上级业务部门名称',
      rules: 'required',
      formItemClass: 'col-span-2 items-baseline',
      componentProps: {
        type: 'tree-select',
        level: 3,
        jzType: 'za',
        checkStrictly: true,
      },
    },
  ];
}

/**
 * 获取表格列配置
 * @returns VxeTableGridOptions['columns'] 表格列配置
 * @description 使用函数的形式返回列数据而不是直接export一个Array常量，是为了响应语言切换时重新翻译表头
 */
export function useGridColumns(): VxeTableGridOptions['columns'] {
  return [
    {
      type: 'checkbox',
      width: 60,
    },
    {
      title: '#',
      type: 'seq',
      width: 60,
      fixed: 'left',
    },
    {
      field: 'jzfldm',
      title: '警种分类',
      align: 'center',
      slots: {
        default: ({ row }) => {
          return renderDictTag(row.jzfldm, 'jzfl');
        },
      },
    },
    {
      field: 'parentName',
      title: '上级业务部门名称',
      align: 'center',
    },
    {
      title: '操作',
      field: 'action',
      fixed: 'right',
      slots: { default: 'action' },
      width: 240,
    },
  ];
}
