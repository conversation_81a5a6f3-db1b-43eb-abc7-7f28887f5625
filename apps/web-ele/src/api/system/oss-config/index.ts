import type {
  OssConfigForm,
  OssConfigQuery,
  OssConfigVO,
} from '#/api/system/oss-config/types';

import { requestClient } from '#/api/request';

/**
 * 查询对象存储配置列表
 * @param query
 */
export const listOssConfig = (query?: OssConfigQuery) => {
  return requestClient.get<PageData<OssConfigVO>>(
    '/resource/oss/config/list',
    query,
  );
};

/**
 * 查询对象存储配置详细
 * @param id
 */
export const getOssConfig = (id: number | string) => {
  return requestClient.get<OssConfigVO>(`/resource/oss/config/${id}`);
};

/**
 * 新增对象存储配置
 * @param data
 */
export const addOssConfig = (data: OssConfigForm) => {
  return requestClient.post('/resource/oss/config', data);
};

/**
 * 修改对象存储配置
 * @param data
 */
export const editOssConfig = (data: OssConfigForm) => {
  return requestClient.put('/resource/oss/config', data);
};

/**
 * 删除对象存储配置权限
 * @param id
 */
export const removeOssConfig = (
  id: Array<number | string> | number | string,
) => {
  return requestClient.delete(`/resource/oss/config/${id}`);
};

/**
 * 对象存储状态修改
 * @param id
 * @param version
 * @param status
 * @param configKey
 */
export const changeOssConfigStatus = (
  id: number | string,
  version: number,
  status: string,
  configKey: string,
) => {
  const data = {
    id,
    version,
    status,
    configKey,
  };
  return requestClient.put('/resource/oss/config/changeStatus', data);
};
