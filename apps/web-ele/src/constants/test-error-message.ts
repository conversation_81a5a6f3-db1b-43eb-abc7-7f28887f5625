// 测试错误信息是否正常工作
import { withNoFullWidth, BaseSchema, ZodEnum } from './zod-enum';
import { z } from '#/adapter/form';

console.log('=== 测试错误信息 ===');

// 1. 测试基础 withNoFullWidth 包装器
console.log('\n1. 基础包装器测试:');
const basicSchema = withNoFullWidth(z.string());
const basicResult = basicSchema.safeParse('你好');
console.log('基础包装器结果:', basicResult);
if (!basicResult.success) {
  console.log('错误信息:', basicResult.error.issues[0].message);
}

// 2. 测试自定义错误消息
console.log('\n2. 自定义错误消息测试:');
const customSchema = withNoFullWidth(z.string(), '用户名不能包含中文字符');
const customResult = customSchema.safeParse('用户名');
console.log('自定义消息结果:', customResult);
if (!customResult.success) {
  console.log('自定义错误信息:', customResult.error.issues[0].message);
}

// 3. 测试 BaseSchema
console.log('\n3. BaseSchema 测试:');
const stringResult = BaseSchema.STRING.safeParse('你好');
console.log('BaseSchema.STRING 结果:', stringResult);
if (!stringResult.success) {
  console.log('STRING 错误信息:', stringResult.error.issues[0].message);
}

const emailResult = BaseSchema.EMAIL.safeParse('测试@example.com');
console.log('BaseSchema.EMAIL 结果:', emailResult);
if (!emailResult.success) {
  console.log('EMAIL 错误信息:', emailResult.error.issues);
}

// 4. 测试 ZodEnum
console.log('\n4. ZodEnum 测试:');
const zodStringResult = ZodEnum.STRING.safeParse('你好');
console.log('ZodEnum.STRING 结果:', zodStringResult);
if (!zodStringResult.success) {
  console.log('ZodEnum.STRING 错误信息:', zodStringResult.error.issues[0].message);
}

// 5. 测试邮箱格式错误 vs 全角字符错误
console.log('\n5. 邮箱错误类型测试:');
const emailFormatError = BaseSchema.EMAIL.safeParse('invalid-email');
console.log('邮箱格式错误:', emailFormatError);
if (!emailFormatError.success) {
  console.log('邮箱格式错误信息:', emailFormatError.error.issues);
}

const emailFullWidthError = BaseSchema.EMAIL.safeParse('测试@example.com');
console.log('邮箱全角字符错误:', emailFullWidthError);
if (!emailFullWidthError.success) {
  console.log('邮箱全角字符错误信息:', emailFullWidthError.error.issues);
}

// 6. 测试手机号错误
console.log('\n6. 手机号错误测试:');
const phoneFormatError = BaseSchema.PHONE.safeParse('12345678901');
console.log('手机号格式错误:', phoneFormatError);
if (!phoneFormatError.success) {
  console.log('手机号格式错误信息:', phoneFormatError.error.issues);
}

const phoneFullWidthError = BaseSchema.PHONE.safeParse('１３８１２３４５６７８');
console.log('手机号全角字符错误:', phoneFullWidthError);
if (!phoneFullWidthError.success) {
  console.log('手机号全角字符错误信息:', phoneFullWidthError.error.issues);
}

// 7. 测试正常情况
console.log('\n7. 正常情况测试:');
const normalString = BaseSchema.STRING.safeParse('hello');
console.log('正常字符串:', normalString);

const normalEmail = BaseSchema.EMAIL.safeParse('<EMAIL>');
console.log('正常邮箱:', normalEmail);

const normalPhone = BaseSchema.PHONE.safeParse('13812345678');
console.log('正常手机号:', normalPhone);

export {
  basicSchema,
  customSchema
};
