<script setup lang="ts">
import type { VxeGridProps } from '#/adapter/vxe-table';
import type { DbColumnVO, DbTableVO, GenTableVO } from '#/api/tool/gen/types';

import { useVbenForm, useVbenModal } from '@vben/common-ui';
import { listToTree } from '@vben/utils';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { listMenu } from '#/api/system/menu';
import { updateGenTable } from '#/api/tool/gen';
import { formSchema } from '#/views/tool/gen/modules/basic';
import { validRules, vxeTableColumns } from '#/views/tool/gen/modules/gen-data';

const emit = defineEmits<{ reload: [] }>();
const columns = ref<DbColumnVO[]>();
const info = ref<DbTableVO>();
const tables = ref<DbTableVO[]>();
const activeName = ref('basic');

/**
 * 树表需要用到的数据
 */
async function initTreeSelect(columns: DbColumnVO[]) {
  const options = columns.map((item) => {
    const label = `${item.columnName} | ${item.columnComment}`;
    return { label, value: item.columnName };
  });
  formApi.updateSchema([
    {
      componentProps: {
        options,
      },
      fieldName: 'treeCode',
    },
    {
      componentProps: {
        options,
      },
      fieldName: 'treeParentCode',
    },
    {
      componentProps: {
        options,
      },
      fieldName: 'treeName',
    },
  ]);
}
function getSubColumns(tables: DbTableVO[], tableName: string) {
  for (const item of tables) {
    if (item.tableName === tableName) {
      return {
        options: item.columns.map((col) => ({
          label: `${col.columnName} | ${col.columnComment}`,
          value: col.columnName,
        })),
      };
    }
  }
  return { options: [] };
}

/**
 *加载子表信息
 * @param tables
 */
async function initSubTables(tables: DbTableVO[]) {
  const options = tables.map((item) => {
    const label = `${item.tableName} | ${item.tableComment}`;
    return { label, value: item.tableName };
  });
  formApi.updateSchema([
    {
      componentProps: {
        options,
      },
      fieldName: 'subTableName',
    },
    {
      dependencies: {
        triggerFields: ['subTableName'],
        componentProps(values) {
          return getSubColumns(tables, values.subTableName);
        },
      },
      fieldName: 'subTableFkName',
    },
  ]);
}

/**
 * 加载菜单选择
 */
async function initMenuSelect() {
  const list = await listMenu();
  const treeData = listToTree(list, { id: 'id', pid: 'parentId' });

  formApi.updateSchema([
    {
      componentProps: {
        data: treeData,
        props: { label: 'menuName', children: 'children' },
        nodeKey: 'id',
        labelField: 'menuName',
        checkStrictly: true,
        filterable: true,
        clearable: true,
        highlightCurrent: true,
      },
      fieldName: 'parentMenuId',
    },
  ]);
}

const gridOptions: VxeGridProps = {
  columns: vxeTableColumns,
  keepSource: true,
  editConfig: {
    trigger: 'click',
    mode: 'cell',
    showStatus: true,
    autoClear: false,
    autoFocus: true,
  },
  editRules: validRules,
  rowConfig: {
    keyField: 'id',
    isCurrent: true, // 高亮当前行
  },
  columnConfig: {
    resizable: true,
  },
  proxyConfig: {
    enabled: true,
  },
  toolbarConfig: {
    enabled: false,
  },
  height: `${window.innerHeight - 180}`,
  pagerConfig: {
    enabled: false,
  },
  data: columns.value,
};

const [BasicTable, tableApi] = useVbenVxeGrid({ gridOptions });

const [BasicForm, formApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
      formItemClass: 'col-span-1',
    },
    labelWidth: 150,
  },
  schema: formSchema(),
  showDefaultActions: false,
  wrapperClass: 'grid-cols-2',
});
const [Modal, modalApi] = useVbenModal({
  async onOpenChange(isOpen: boolean) {
    if (isOpen) {
      const data = modalApi.getData<GenTableVO>();
      columns.value = data.rows;
      info.value = data.info;
      tables.value = data.tables;
      await initTreeSelect(info.value.columns);
      await initMenuSelect();
      await initSubTables(tables.value);

      await formApi.setValues(info.value);

      tableApi.setGridOptions({
        data: columns.value,
      });
    }
  },
  async onConfirm() {
    const { valid } = await formApi.validate();
    if (!valid) {
      activeName.value = 'basic';
      return;
    }
    const hasError = await tableApi.grid.validate();
    if (hasError) {
      activeName.value = 'columnInfo';
      return;
    }
    modalApi.lock();
    const genTable: any = Object.assign({}, info.value);
    const formValues = await formApi.getValues<DbTableVO>();
    Object.assign(genTable, formValues);
    genTable.columns = tableApi?.grid?.getData?.() ?? [];
    // 树表需要添加这个参数
    if (genTable && genTable.tplCategory === 'tree') {
      const { treeCode, treeName, treeParentCode } = genTable;
      genTable.params = {
        treeCode,
        treeName,
        treeParentCode,
      };
    }
    // 需要进行参数转化
    if (genTable) {
      const transform = (ret: boolean) => (ret ? '1' : '0');
      genTable.columns.forEach((column: DbColumnVO) => {
        const { edit, insert, query, required, list } = column;
        column.isInsert = transform(insert);
        column.isEdit = transform(edit);
        column.isList = transform(list);
        column.isQuery = transform(query);
        column.isRequired = transform(required);
      });
      // 需要手动添加父级菜单 弹窗类型
      genTable.params = {
        ...genTable.params,
        parentMenuId: genTable.parentMenuId,
        popupComponent: genTable.popupComponent,
        formComponent: genTable.formComponent,
      };
    }
    await updateGenTable(genTable)
      .then(() => {
        emit('reload');
        modalApi.close();
        formApi.resetForm();
      })
      .catch(() => {
        modalApi.unlock();
      });
  },
});

watch(
  () => columns.value,
  (newVal) => {
    if (newVal) {
      tableApi.setGridOptions({ data: newVal });
    }
  },
  { deep: true },
);
</script>

<template>
  <Modal title="修改配置信息" :fullscreen="true" :fullscreen-button="false">
    <el-tabs v-model="activeName">
      <el-tab-pane label="基本信息" name="basic">
        <BasicForm />
      </el-tab-pane>
      <el-tab-pane label="字段信息" name="columnInfo">
        <BasicTable />
      </el-tab-pane>
    </el-tabs>
  </Modal>
</template>

<style scoped lang="scss">
:deep(.el-form-item__content) {
  margin-left: 0 !important;
}

:deep(.el-table .el-form-item--large) {
  margin-bottom: 0 !important;
}
</style>
