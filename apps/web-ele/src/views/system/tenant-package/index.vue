<script lang="ts" setup>
import { Page } from '@vben/common-ui';

import MenuPermission from '#/components/system/menu-permission/index.vue';

import { useTenantPackageService } from './service';

defineOptions({
  name: 'TenantPackageIndex',
});

const {
  Form,
  BasicTable,
  FormDialog,
  useToolbarButtons,
  useRowButtons,
  handleStatusChange,
  handleMenuCheckStrictlyChange,
  menuPermissionRef,
  formApi,
  menuTree,
} = useTenantPackageService();
</script>

<template>
  <Page auto-content-height>
    <FormDialog class="w-[1024px]" title="租户套餐管理">
      <Form>
        <template #menuIds="slotProps">
          <div class="h-[600px] w-full">
            <!-- association为readonly 不能通过v-model绑定 -->
            <MenuPermission
              ref="menuPermissionRef"
              :checked-keys="
                typeof slotProps.value === 'string'
                  ? slotProps.value.split(',')
                  : []
              "
              :menus="menuTree"
              :association="formApi.form.values.menuCheckStrictly"
              @update:association="handleMenuCheckStrictlyChange"
            />
          </div>
        </template>
      </Form>
    </FormDialog>

    <BasicTable>
      <template #status="{ row }">
        <ElSwitch
          v-model="row.status"
          inline-prompt
          active-text="正常"
          inactive-text="停用"
          active-value="0"
          inactive-value="1"
          style="

--el-switch-off-color: #ff4949"
          @change="handleStatusChange(row)"
        />
      </template>
      <template #toolbar-actions>
        <ActionButtonGroup :buttons="useToolbarButtons()" />
      </template>

      <template #action="{ row }">
        <ActionButtonGroup :buttons="useRowButtons()" :row="row" />
      </template>
    </BasicTable>
  </Page>
</template>
