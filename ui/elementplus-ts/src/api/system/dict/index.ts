import type { DictDataOptionTree, DictForm, DictQuery, DictVO } from '@/api/system/dict/types';
import http from '@/utils/http';
import type { PageData } from '@/utils/http/interface';

/**
 * 查询字典管理列表
 * @param query
 * @returns {*}
 */

export const listDict = (query?: DictQuery) => {
  return http.get<PageData<DictVO>>('/system/dict/list', query);
};

/**
 * 查询字典管理选择列表
 * @param query
 * @returns {*}
 */

export const listDictOptionSelect = (query?: DictQuery) => {
  let url = '/system/dict/listOptionSelect';
  if (query.isTree) {
    url = '/system/dict/listOptionSelectTree';
  }
  return http.get<Array<DictDataOptionTree>>(url, query);
};

/**
 * 查询字典管理详细
 * @param id
 */
export const getDict = (id: string | number) => {
  return http.get<DictVO>('/system/dict/' + id);
};

/**
 * 新增字典管理
 * @param data
 */
export const addDict = (data: DictForm) => {
  return http.post('/system/dict', data);
};

/**
 * 修改字典管理
 * @param data
 */
export const updateDict = (data: DictForm) => {
  return http.put('/system/dict', data);
};

/**
 * 删除字典管理
 * @param id
 */
export const delDict = (id: string | number | Array<string | number>) => {
  return http.delete('/system/dict/' + id);
};
