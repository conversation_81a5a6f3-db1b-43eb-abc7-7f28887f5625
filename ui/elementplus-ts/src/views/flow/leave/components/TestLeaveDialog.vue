<script setup name="LeaveDialog" lang="ts">
import { LeaveForm } from '@/api/flow/leave/types';
import { useLeave } from '@/views/flow/leave/utils';

const { getflowDic,dataOpt} = useLeave();
//定义属性并指定默认值
const contentProps = defineProps<{
  model?: LeaveForm;
  postFlowOptions?:any
}>();
const newFormInline = ref(contentProps.model);
</script>
<template>
  <div>
    <el-row>
      <el-col :span="24">
       <el-form-item label="流程" >
         <SkDict
           v-model="newFormInline.flowCode"
           type="select"
           model="list"
           :data="postFlowOptions?.postOptions"
         >

         </SkDict>
<!--         <SkDict
           v-model="LeaveForm.folwType"
           type="select"
           :List="dataOpt"
         ></SkDict>-->
       </el-form-item>
      </el-col>

      <el-col :span="24">
        <el-form-item label="ywmc" prop="ywmc">
          <el-input v-model="newFormInline.ywmc" clearable placeholder="请输入ywmc" />
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <el-form-item label="start_time" prop="studentBirthday">
          <el-date-picker
            v-model="newFormInline.startTime"
            type="date"
            value-format="YYYY-MM-DD"
            clearable
            placeholder="请选择start_time"
          />
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <el-form-item label="end_time" prop="studentBirthday">
          <el-date-picker
            v-model="newFormInline.endTime"
            type="date"
            value-format="YYYY-MM-DD"
            clearable
            placeholder="请选择end_time"
          />
        </el-form-item>
      </el-col>

    </el-row>
  </div>
</template>
<style lang="scss" scoped>
:deep(.el-form-item__content) {
  margin-left: 0 !important;
}

:deep(.el-table .el-form-item--large) {
  margin-bottom: 0 !important;
}
</style>
