import type {
  <PERSON>lientForm,
  <PERSON>lient<PERSON>uery,
  Client<PERSON>,
} from '#/api/system/client/types';

import { requestClient } from '#/api/request';

/**
 * 查询系统授权列表
 * @param query
 */
export const listClient = (query?: ClientQuery) => {
  return requestClient.get<PageData<ClientVO>>('/system/client/list', query);
};

/**
 * 查询系统授权详细
 * @param id
 */
export const getClient = (id: number | string) => {
  return requestClient.get<ClientVO>(`/system/client/${id}`);
};

/**
 * 新增系统授权
 * @param data
 */
export const addClient = (data: ClientForm) => {
  return requestClient.post('/system/client', data);
};

/**
 * 修改系统授权
 * @param data
 */
export const editClient = (data: ClientForm) => {
  return requestClient.put('/system/client', data);
};

/**
 * 删除系统授权权限
 * @param id
 */
export const removeClient = (id: Array<number | string> | number | string) => {
  return requestClient.delete(`/system/client/${id}`);
};
