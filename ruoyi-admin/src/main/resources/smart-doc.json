{"serverUrl": "http://127.0.0.1", "isStrict": false, "allInOne": true, "outPath": "target/docs", "coverOld": true, "packageFilters": "com.ruoyi.web.controller.*", "style": "xt256", "createDebugPage": true, "md5EncryptedHtmlName": false, "skipTransientField": true, "appToken": "19f1141861cb4f0087a479beb33827df", "openUrl": "http://***********:7700/api", "debugEnvName": "用户登录", "replace": true, "debugEnvUrl": "http://127.0.0.1:8080", "requestHeaders": [{"name": "clientid", "type": "string", "desc": "desc", "required": false, "since": "-"}, {"name": "authorization", "type": "string", "desc": "desc", "required": false, "since": "-"}], "sourceCodePaths": [{"path": "src/main/java", "desc": "测试"}]}