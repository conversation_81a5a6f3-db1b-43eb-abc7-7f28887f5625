import http from '@/utils/http';
import type { SjyVO, SjyForm, SjyQuery } from '@/api/system/sjy/types';
import type { PageData } from '@/utils/http/interface';

/**
 * 查询数据元管理列表
 * @param query
 * @returns {*}
 */
export const listSjy = (query?: SjyQuery) => {
  return http.get<PageData<SjyVO>>('/system/sjy/list', query);
};

/**
 * 查询数据元管理详细
 * @param id
 */
export const getSjy = (id: string | number) => {
  return http.get<SjyVO>('/system/sjy/' + id);
};

/**
 * 新增数据元管理
 * @param data
 */
export const addSjy = (data: SjyForm) => {
  return http.post('/system/sjy', data);
};

/**
 * 修改数据元管理
 * @param data
 */
export const editSjy = (data: SjyForm) => {
  return http.put('/system/sjy', data);
};

/**
 * 删除数据元管理
 * @param id
 */
export const removeSjy = (id: string | number | Array<string | number>) => {
  return http.delete('/system/sjy/' + id);
};
