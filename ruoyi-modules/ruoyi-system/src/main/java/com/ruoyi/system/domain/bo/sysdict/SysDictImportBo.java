package com.ruoyi.system.domain.bo.sysdict;

import cn.idev.excel.annotation.ExcelProperty;
import com.ruoyi.common.excel.annotation.ExcelDictFormat;
import com.ruoyi.common.excel.convert.ExcelDictConvert;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * 字典管理导入视图对象 sys_dict
 *
 * <AUTHOR>
 * @date 2024-06-06
 */

@Data
@NoArgsConstructor
public class SysDictImportBo implements Serializable
{

    @Serial
    private static final long serialVersionUID = 1L;

     /** 主键 */
     @ExcelProperty(value = "主键")
     private String id;

     /** 逻辑删除标识 */
    @ExcelProperty(value = "逻辑删除标识")
    private Integer jlzt;

     /** 代码 */
    @ExcelProperty(value = "代码")
    private String dm;

     /** 名称 */
    @ExcelProperty(value = "名称")
    private String mc;

     /** 简称 */
    @ExcelProperty(value = "简称")
    private String jc;

     /** 类别简称代码 */
    @ExcelProperty(value = "类别简称代码")
    private String lx;

     /** 父结点ID */
    @ExcelProperty(value = "父结点ID")
    private String parentId;

     /** 使用状态;1. 启用 2.停用 */
    @ExcelProperty(value = "使用状态;1. 启用 2.停用", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_job_status")
    private String syztdm;

     /** 标准类型;1.中国国家标准 2.中国行业标准 3.云南省标准 4.云南地方标准 5.系统标准 */
    @ExcelProperty(value = "标准类型;1.中国国家标准 2.中国行业标准 3.云南省标准 4.云南地方标准 5.系统标准")
    private String bzlxdm;

     /** 显示顺序 */
    @ExcelProperty(value = "显示顺序")
    private Integer orderNum;

     /** 数据元内部标识符 */
    @ExcelProperty(value = "数据元内部标识符")
    private String nbbsf;

     /** 字典标准名称 */
    @ExcelProperty(value = "字典标准名称")
    private String cybzzy;

     /** 其他配置代码 */
    @ExcelProperty(value = "其他配置代码")
    private String qtdm;

     /** $column.columnComment */
    @ExcelProperty(value = "${comment}", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "$column.readConverterExp()")
    private String sjlx;


}
