package com.ruoyi.system.domain.bo.syslogininfor;

import com.ruoyi.system.domain.po.SysLogininfor;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import jakarta.validation.constraints.*;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serial;
import java.io.Serializable;

/**
 * 系统访问记录业务对象 sys_logininfor
 *
 * <AUTHOR>
 * @since 2025-05-08 12:49:33
 */
@Data
@AutoMapper(target = SysLogininfor.class, reverseConvertGenerate = false)
public class SysLogininforAddBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    private String tenantId;
    /**
     * 用户账号
     */
    @NotBlank(message = "用户账号不能为空")
    private String userName;

    /**
     * 客户端
     */
    @NotBlank(message = "客户端不能为空")
    private String clientKey;

    /**
     * 设备类型
     */
    @NotBlank(message = "设备类型不能为空")
    private String deviceType;

    /**
     * IP地址
     */
    @NotBlank(message = "IP地址不能为空")
    private String ipaddr;

    /**
     * 登录地点
     */
    @NotBlank(message = "登录地点不能为空")
    private String loginLocation;

    /**
     * 浏览器类型
     */
    @NotBlank(message = "浏览器类型不能为空")
    private String browser;

    /**
     * 操作系统
     */
    @NotBlank(message = "操作系统不能为空")
    private String os;

    /**
     * 登录状态（0成功 1失败）
     */
    @NotBlank(message = "登录状态（0成功 1失败）不能为空")
    private String status;

    /**
     * 提示消息
     */
    @NotBlank(message = "提示消息不能为空")
    private String msg;

    /**
     * 访问时间
     */
    @NotNull(message = "访问时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" )
    private LocalDateTime loginTime;


}
