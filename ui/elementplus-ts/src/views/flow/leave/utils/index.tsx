import type { ColumnProps, SkTableInstance } from '@/components/SkUi/SkTable/interface';
import type { LeaveForm, LeaveVO ,PageSet } from '@/api/flow/leave/types';
import testLeaveDialog from '@/views/flow/leave/components/TestLeaveDialog.vue';
import {addLeave, editLeave, flowchart, getFlowDic, getLeave, removeLeave, submit} from '@/api/flow/leave';
import { addDialog } from '@/components/SkUi/SkReDialog';
import { h, reactive, ref } from 'vue';
import { useValidator } from '@/hooks/skUi/web/useValidator';
import {postOptions} from "@/views/system/post/utils";


export function useLeave() {
  const skTable = ref<SkTableInstance>();
  const { required } = useValidator();

  const pageSet = ref({
    flowChart:false,
    imgUrl:''
  })

  const dataOpt = ref([])
  /**
   * 表格配置项
   */
  const columns = reactive<ColumnProps<LeaveVO>[]>([
    { type: 'index', label: '#', width: 80 },
    {
      prop: 'id',
      align: 'center',
      label: 'id'
    },
    {
      prop: 'folwType',
      search: {
        el: 'input'
      },
      align: 'center',
      label: 'folw_type'
    },
    {
      prop: 'ywmc',
      search: {
        el: 'input'
      },
      align: 'center',
      label: 'ywmc'
    },
    {
      prop: 'startTime',
      search: {
        el: 'date-picker',
        span: 1,
        props: {
          type: 'date',
          valueFormat: 'YYYY-MM-DD',
          placeholder: '请选择start_time'
        }
      },
      align: 'center',
      label: 'start_time'
    },
    {
      prop: 'endTime',
      search: {
        el: 'date-picker',
        span: 1,
        props: {
          type: 'date',
          valueFormat: 'YYYY-MM-DD',
          placeholder: '请选择end_time'
        }
      },
      align: 'center',
      label: 'end_time'
    },
    {
      prop: 'instanceId',
      search: {
        el: 'input'
      },
      align: 'center',
      label: 'instance_id'
    },
    {
      prop: 'nodeCode',
      search: {
        el: 'input'
      },
      align: 'center',
      label: 'node_code'
    },
    {
      prop: 'nodeName',
      search: {
        el: 'input'
      },
      align: 'center',
      label: 'node_name'
    },
    {
      prop: 'nodeType',
      search: {
        el: 'input'
      },
      align: 'center',
      label: 'node_type'
    },
    {
      prop: 'flowStatus',
      search: {
        el: 'input'
      },
      align: 'center',
      label: 'flow_status'
    },
    { prop: 'operation', slot: 'operation', label: '操作', width: 300, fixed: 'right' }
  ]);

  const rules = reactive({
    folwType: [required('folw_type')],
    ywmc: [required('ywmc')],
    startTime: [required('start_time')],
    endTime: [required('end_time')],
    instanceId: [required('instance_id')],
    nodeCode: [required('node_code')],
    nodeName: [required('node_name')],
    nodeType: [required('node_type')],
    flowStatus: [required('flow_status')],
  });
  /**
   * 初始化编辑信息
   * @param leaveVo
   */
  const initFormProps = async (leaveVo: Partial<LeaveVO>) => {
    //初始化默认值
    const formInline = ref<LeaveForm>({
      id: undefined,
      folwType: undefined,
      ywmc: '',
      startTime: undefined,
      endTime: undefined,
      instanceId: undefined,
      nodeCode: '',
      nodeName: '',
      nodeType: undefined,
      flowStatus: '',
    });
    if (leaveVo.id) {
      const res = await getLeave(leaveVo.id);
      Object.assign(formInline.value, res.data);
    }
    return formInline;
  };

  /***
   * 编辑，新增,数据交换
   */
  const openDialog = async (title: string, row: Partial<LeaveVO> = {}) => {
    addDialog({
      title: title + '流程测试',
      width: '1024px',
      props: {
        model: await initFormProps(row),
        labelWidth: '140px',
        postFlowOptions:await getflowDic(),
        rules: rules
      },
      contentRenderer: () => h(testLeaveDialog),
      beforeSure: async (done, { options }, formRef) => {
        const curData = options.props.model as LeaveForm;
        formRef.validate(async (valid: boolean) => {
          if (valid) {
            const api = title === '新增' ? addLeave : submit;
            await api(curData).then((res: any) => {
              if (res.code === 200) {
                window.skynet.alert(`${title}成功！`);
                done();
                skTable.value?.getTableList();
              }
            });
          }
        });
      }
    });
  };
  /** 导出按钮操作 */
  const handleExport = () => {
    window.skynet.download(
      'flow/leave/export',
      {
        ...skTable.value.searchParam
      },
      `leave_${new Date().getTime()}.xlsx`
    );
  };

  const showflowChart = async (row?:LeaveVO) =>{

    pageSet.value.flowChart =true;
    const id = row.instanceId;
    const reslt = await flowchart(id);
    pageSet.value.imgUrl='data:image/gif;base64,'+reslt.msg;
   // console.log(pageSet);
  }

  const getflowDic = async ()=>{
    const rest = (await getFlowDic(null)).data;
   const posts = rest.map((p)=>{
      console.log(p.id)
      console.log(p.flowCode)
      console.log(p)
      //{label:'',value:''}
      return{label:p.flowCode,value:p.flowCode}
    })


    return {postOptions:posts};
  }

  /** 删除按钮操作 */
  const handleDelete = async (row?: LeaveVO) => {
    const _ids = row?.id;
    await window.skynet
      .confirm('是否确认删除流程测试编号为"' + _ids + '"的数据项？')
      .then(async () => {
        await removeLeave(_ids);
        window.skynet.alert('删除成功');
        await skTable.value?.getTableList();
      })
      .catch(() => {});
  };
  return {
    skTable,
    columns,
    rules,
    pageSet,
    dataOpt,
    getflowDic,
    openDialog,
    handleExport,
    showflowChart,
    handleDelete
  };
}
