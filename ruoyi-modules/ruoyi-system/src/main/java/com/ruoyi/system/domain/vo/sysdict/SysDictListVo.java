package com.ruoyi.system.domain.vo.sysdict;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 字典管理ListVo
 *
 * <AUTHOR>
 * 2024-09-09
 */
@Data
public class SysDictListVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private String id;

    /**
     * 父菜单ID
     */
    private String parentId;

    /**
     * 显示顺序
     */
    private Integer orderNum;

    /**
     * 代码
     */
    private String dm;

    /**
     * 名称
     */
    private String mc;

    /**
     * 简称
     */
    private String jc;

    /**
     * 使用状态代码
     */
    private String syztdm;

    /**
     * 使用状态名称
     */
    private String syztmc;

    /**
     * 标准类型代码
     */
    private String bzlxdm;

    /**
     * 标准类型名称
     */
    private String bzlxmc;

    /**
     * 是否有子节点
     */
    private boolean hasChildren;
}
