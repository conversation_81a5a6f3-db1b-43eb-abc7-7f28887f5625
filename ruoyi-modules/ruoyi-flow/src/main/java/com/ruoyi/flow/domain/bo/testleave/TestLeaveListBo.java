package com.ruoyi.flow.domain.bo.testleave;

import com.ruoyi.flow.domain.po.TestLeave;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 流程测试业务对象 test_leave
 *
 * <AUTHOR>
 * @date 2025-01-14 15:32:53
 */
@Data
@AutoMapper(target = TestLeave.class, reverseConvertGenerate = false)
public class TestLeaveListBo {

    /**
     * folw_type
     */
   // @NotNull(message = "folw_type不能为空")
    private String flowCode;

    /**
     * ywmc
     */
   // @NotBlank(message = "ywmc不能为空")
    private String ywmc;

    /**
     * start_time
     */
   // @NotNull(message = "start_time不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startTime;

    /**
     * end_time
     */
   // @NotNull(message = "end_time不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endTime;

    /**
     * instance_id
     */
   // @NotNull(message = "instance_id不能为空")
    private Long instanceId;

    /**
     * node_code
     */
   // @NotBlank(message = "node_code不能为空")
    private String nodeCode;

    /**
     * node_name
     */
   // @NotBlank(message = "node_name不能为空")
    private String nodeName;

    /**
     * node_type
     */
   // @NotNull(message = "node_type不能为空")
    private Integer nodeType;

    /**
     * flow_status
     */
   // @NotBlank(message = "flow_status不能为空")
    private String flowStatus;


}
