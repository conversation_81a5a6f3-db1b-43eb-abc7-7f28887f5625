<script setup lang="ts">
import { h, ref } from 'vue';
import { addDrawer, closeAllDrawer, closeDrawer, updateDrawer } from '@/components/SkUi/SkReDrawer';

// 使用全局变量
declare const ElMessage: {
  success: (message: string) => void;
  warning: (message: string) => void;
  info: (message: string) => void;
};

const formData = ref({
  name: '',
  age: 18,
  address: '',
  email: ''
});

// 基本使用
function openBasicDrawer(): void {
  addDrawer({
    title: '基本抽屉',
    width: '30%',
    contentRenderer: () => {
      return h('div', { class: 'p-4' }, [h('p', '这是一个基本的抽屉示例'), h('p', '可以在这里展示内容')]);
    }
  });
}

// 带表单的抽屉
function openFormDrawer(): void {
  addDrawer({
    title: '表单抽屉',
    width: '40%',
    contentRenderer: () => {
      return h('div', { class: 'p-4' }, [
        h('el-form-item', { label: '姓名' }, [
          h('el-input', {
            modelValue: formData.value.name,
            'onUpdate:modelValue': (val: string) => (formData.value.name = val),
            placeholder: '请输入姓名'
          })
        ]),
        h('el-form-item', { label: '年龄' }, [
          h('el-input-number', {
            modelValue: formData.value.age,
            'onUpdate:modelValue': (val: number) => (formData.value.age = val),
            min: 0,
            max: 120
          })
        ]),
        h('el-form-item', { label: '地址' }, [
          h('el-input', {
            modelValue: formData.value.address,
            'onUpdate:modelValue': (val: string) => (formData.value.address = val),
            type: 'textarea',
            rows: 3,
            placeholder: '请输入地址'
          })
        ])
      ]);
    },
    beforeSure: (done, { options, index }) => {
      if (!formData.value.name) {
        ElMessage.warning('请输入姓名');
        return;
      }

      // 模拟请求
      ElMessage.success('提交成功：' + JSON.stringify(formData.value));
      done();
    }
  });
}

// 自定义底部按钮的抽屉
function openCustomButtonDrawer(): void {
  const options = {
    title: '自定义按钮抽屉',
    width: '35%',
    contentRenderer: ({ options, index }) => {
      return h('div', { class: 'p-4' }, [h('p', '抽屉ID: ' + index), h('p', '这个抽屉有自定义按钮')]);
    },
    footerButtons: [
      {
        label: '保存',
        type: 'primary',
        btnClick: ({ drawer: { options, index } }) => {
          ElMessage.success('点击了保存');
          closeDrawer(options, index);
        }
      },
      {
        label: '保存并新建',
        type: 'success',
        btnClick: ({ drawer: { options, index } }) => {
          ElMessage.success('点击了保存并新建');
          updateDrawer(options, index, { title: '新建记录' });
        }
      },
      {
        label: '重置',
        btnClick: () => {
          ElMessage.info('点击了重置');
        }
      },
      {
        label: '取消',
        btnClick: ({ drawer: { options, index } }) => {
          closeDrawer(options, index);
        }
      }
    ]
  };

  addDrawer(options);
}

// 不同方向的抽屉
function openDirectionDrawer(direction: 'rtl' | 'ltr' | 'ttb' | 'btt'): void {
  const directionMap = {
    rtl: '右侧',
    ltr: '左侧',
    ttb: '顶部',
    btt: '底部'
  };

  addDrawer({
    title: `${directionMap[direction]}抽屉`,
    direction,
    width: direction === 'ttb' || direction === 'btt' ? '30%' : '30%',
    contentRenderer: () => {
      return h('div', { class: 'p-4' }, [
        h('p', `这是一个从${directionMap[direction]}打开的抽屉`),
        h('p', '可以调整direction属性来改变抽屉的打开方向')
      ]);
    }
  });
}

// 嵌套抽屉
function openNestedDrawer(): void {
  addDrawer({
    title: '第一层抽屉',
    width: '40%',
    contentRenderer: () => {
      return h('div', { class: 'p-4' }, [
        h('p', '这是第一层抽屉'),
        h(
          'el-button',
          {
            type: 'primary',
            onClick: () => {
              addDrawer({
                title: '第二层抽屉',
                width: '30%',
                contentRenderer: () => {
                  return h('div', { class: 'p-4' }, [h('p', '这是第二层抽屉'), h('p', '抽屉可以嵌套使用')]);
                }
              });
            }
          },
          '打开第二层抽屉'
        )
      ]);
    }
  });
}

// 全屏抽屉
function openFullscreenDrawer(): void {
  addDrawer({
    title: '全屏抽屉',
    fullscreen: true,
    contentRenderer: () => {
      return h('div', { class: 'p-4' }, [
        h('p', '这是一个全屏抽屉示例'),
        h('p', '使用fullscreen属性可以设置抽屉全屏显示，与SkReDialog保持一致'),
        h('div', { class: 'mt-4' }, [
          h('el-alert', {
            title: '提示',
            type: 'info',
            description: '全屏模式下，抽屉会占据整个屏幕',
            showIcon: true,
            closable: false
          })
        ])
      ]);
    }
  });
}

// 自定义关闭图标的抽屉
function openCustomIconDrawer(): void {
  // 使用HTML内联SVG作为关闭图标
  const CloseIcon = () =>
    h(
      'svg',
      {
        viewBox: '0 0 1024 1024',
        width: '1em',
        height: '1em',
        fill: 'currentColor'
      },
      [
        h('path', {
          d: 'M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm165.4 618.2l-66-0.3L512 563.4l-99.3 118.4-66.1 0.3c-4.4 0-8-3.5-8-8 0-1.9 0.7-3.7 1.9-5.2l130.1-155L340.5 359c-1.2-1.5-1.9-3.3-1.9-5.2 0-4.4 3.6-8 8-8l66.1 0.3L512 464.6l99.3-118.4 66-0.3c4.4 0 8 3.5 8 8 0 1.9-0.7 3.7-1.9 5.2L553.5 514l130 155c1.2 1.5 1.9 3.3 1.9 5.2 0 4.4-3.6 8-8 8z'
        })
      ]
    );

  addDrawer({
    title: '自定义关闭图标',
    width: '30%',
    closeIcon: CloseIcon,
    contentRenderer: () => {
      return h('div', { class: 'p-4' }, [
        h('p', '这个抽屉使用了自定义关闭图标'),
        h('p', '你可以看到右上角的关闭按钮已经变成了自定义图标'),
        h('div', { class: 'mt-4' }, [
          h('el-alert', {
            title: '提示',
            type: 'info',
            description: '可以传入任何组件作为关闭图标，包括Element Plus图标、自定义SVG等',
            showIcon: true,
            closable: false
          })
        ])
      ]);
    }
  });
}
</script>

<template>
  <div class="drawer-example-container p-6">
    <h2 class="text-xl font-bold mb-4">SkReDrawer 抽屉组件示例</h2>

    <el-divider content-position="left">基础用法</el-divider>
    <div class="mb-4">
      <el-button type="primary" @click="openBasicDrawer">打开基础抽屉</el-button>
    </div>

    <el-divider content-position="left">表单抽屉</el-divider>
    <div class="mb-4">
      <el-button type="primary" @click="openFormDrawer">打开表单抽屉</el-button>
    </div>

    <el-divider content-position="left">自定义按钮</el-divider>
    <div class="mb-4">
      <el-button type="primary" @click="openCustomButtonDrawer">自定义按钮抽屉</el-button>
    </div>

    <el-divider content-position="left">不同方向</el-divider>
    <div class="mb-4 flex space-x-4">
      <el-button type="primary" @click="openDirectionDrawer('rtl')">右侧抽屉</el-button>
      <el-button type="primary" @click="openDirectionDrawer('ltr')">左侧抽屉</el-button>
      <el-button type="primary" @click="openDirectionDrawer('ttb')">顶部抽屉</el-button>
      <el-button type="primary" @click="openDirectionDrawer('btt')">底部抽屉</el-button>
    </div>

    <el-divider content-position="left">嵌套抽屉</el-divider>
    <div class="mb-4">
      <el-button type="primary" @click="openNestedDrawer">嵌套抽屉</el-button>
    </div>

    <el-divider content-position="left">新增特性</el-divider>
    <div class="mb-4 flex space-x-4">
      <el-button type="success" @click="openFullscreenDrawer">全屏抽屉</el-button>
      <el-button type="success" @click="openCustomIconDrawer">自定义关闭图标</el-button>
    </div>

    <el-divider content-position="left">全屏按钮</el-divider>
    <div class="mb-4">
      <el-alert
        title="新增全屏按钮功能"
        type="success"
        description="抽屉标题栏右侧现在有一个全屏按钮，点击可切换抽屉全屏状态，类似于SkReDialog的实现"
        show-icon
        :closable="false"
        class="mb-3"
      />
      <el-button type="primary" @click="openBasicDrawer">打开任意抽屉尝试全屏按钮</el-button>
    </div>

    <el-divider content-position="left">操作</el-divider>
    <div class="mb-4">
      <el-button type="danger" @click="closeAllDrawer">关闭所有抽屉</el-button>
    </div>
  </div>
</template>

<style scoped>
.drawer-example-container {
  max-width: 1000px;
  margin: 0 auto;
}
</style>
