package com.ruoyi.flow.controller;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.ruoyi.common.excel.core.ExcelResult;
import com.ruoyi.common.core.core.domain.Result;
import com.ruoyi.common.core.core.domain.PageData;
import com.ruoyi.common.excel.utils.ExcelUtil;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.web.annotation.RepeatSubmit;
import com.ruoyi.common.web.core.BaseController;
import jakarta.annotation.Resource;
import com.ruoyi.flow.domain.vo.testleave.TestLeaveDetailVo;
import com.ruoyi.flow.domain.vo.testleave.TestLeaveListVo;
import com.ruoyi.flow.domain.vo.testleave.TestLeaveExcelVo;
import com.ruoyi.flow.domain.vo.testleave.TestLeaveOptionVo;
import com.ruoyi.flow.domain.bo.testleave.TestLeaveImportBo;
import com.ruoyi.flow.domain.bo.testleave.TestLeaveAddBo;
import com.ruoyi.flow.domain.bo.testleave.TestLeaveEditBo;
import com.ruoyi.flow.domain.bo.testleave.TestLeaveListBo;
import com.ruoyi.flow.domain.bo.testleave.TestLeaveOptionBo;
import com.ruoyi.flow.listener.TestLeaveImportListener;
import com.ruoyi.flow.service.TestLeaveService;
import org.springframework.web.multipart.MultipartFile;


/**
 * 流程测试Controller
 *
 * <AUTHOR>
 * @date 2025-01-14 15:32:53
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/flow/leave")
public class TestLeaveController extends BaseController {
    @Resource
    private TestLeaveService testLeaveService;

    /**
     * 查询流程测试List列表
     */
    @SaCheckPermission("flow:leave:list")
    @GetMapping("/list")
    public Result<PageData<TestLeaveListVo>> page(TestLeaveListBo testLeaveListBo) {
        return testLeaveService.page(testLeaveListBo);
    }

    /**
    * 查询流程测试选择列表
    *
    * @param optionBo 流程测试选择查询条件
    * @return 返回流程测试信息列表
    */
    @SaCheckLogin
    @GetMapping("/listOption")
    public Result<List<TestLeaveOptionVo>> listOption(TestLeaveOptionBo optionBo) {
        return testLeaveService.optionSelect(optionBo);
    }


    /**
     * 新增流程测试
     */
    @SaCheckPermission("flow:leave:add")
    @Log(title = "流程测试", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping
    public Result<Void> add(@Validated @RequestBody TestLeaveAddBo testLeaveAddBo) {
        return testLeaveService.InsertFsd(testLeaveAddBo,"");
    }

    @GetMapping(value = "/submit/{id}")
    public Result<Void> submit(@PathVariable Long id) {
        return testLeaveService.submit(id);
    }

    @GetMapping(value = "/flowChart/{id}")
    public Result<String> flowChart(@PathVariable Long id) throws IOException {
        return testLeaveService.flowChart(id);


    }
    /**
     * 修改流程测试
     */
    @SaCheckPermission("flow:leave:edit")
    @Log(title = "流程测试", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping
    public Result<Void> edit(@Validated @RequestBody TestLeaveEditBo testLeaveEditBo) {
        return  testLeaveService.edit(testLeaveEditBo);
    }

    /**
     * 删除流程测试
     */
    @SaCheckPermission("flow:leave:remove")
    @Log(title = "流程测试", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result<Void> remove(@PathVariable Long[] ids) {
        return  testLeaveService.removeByIds(ids);
    }

    /**
     * 获取流程测试详细信息
     */
    @SaCheckPermission("flow:leave:query")
    @GetMapping(value = "/{id}")
    public Result<TestLeaveDetailVo> detail(@PathVariable Long id) {
        return Result.success(testLeaveService.detail(id));
    }

    /**
     * 导出流程测试列表
     */
    @SaCheckPermission("flow:leave:export")
    @Log(title = "流程测试", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TestLeaveListBo testLeaveListBo) {
        List<TestLeaveExcelVo> list = testLeaveService.export(testLeaveListBo);
        ExcelUtil.exportExcel(list, "流程测试", TestLeaveExcelVo.class, response);
    }

    /**
     * 导入数据
     *
     * @param file          导入文件
     * @param updateSupport 是否更新已存在数据
     */
    @Log(title = "流程测试", businessType = BusinessType.IMPORT)
    @SaCheckPermission("flow:leave:import")
    @PostMapping("/importData")
    public Result<Void> importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelResult<TestLeaveImportBo> result = ExcelUtil.importExcel(file.getInputStream(), TestLeaveImportBo.class, new TestLeaveImportListener(updateSupport));
        return Result.success(result.getAnalysis());
    }

    /**
     * 按模板导入数据
     * @param response 返回response
     */
    @SaCheckPermission("flow:leave:import")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil.exportExcel(new ArrayList<>(), "流程测试", TestLeaveImportBo.class, response);
    }
}
