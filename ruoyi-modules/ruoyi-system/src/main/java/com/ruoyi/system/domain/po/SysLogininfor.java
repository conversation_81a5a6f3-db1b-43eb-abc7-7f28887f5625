package com.ruoyi.system.domain.po;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Table;
import lombok.Data;

/**
 * 系统访问记录对象 sys_logininfor
 *
 * <AUTHOR>
 * @since 2025-05-08 10:26:02
 */
@Data
@Table(value = "sys_logininfor")
public class SysLogininfor implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 访问ID */
    @Id
    private String id;

    /**
     * 租户编号
     */
    @Column(tenantId = true)
    private String tenantId;

    /** 用户账号 */
    private String userName;

    /** 客户端 */
    private String clientKey;

    /** 设备类型 */
    private String deviceType;

    /** IP地址 */
    private String ipaddr;

    /** 登录地点 */
    private String loginLocation;

    /** 浏览器类型 */
    private String browser;

    /** 操作系统 */
    private String os;

    /** 登录状态（0成功 1失败） */
    private String status;

    /** 提示消息 */
    private String msg;

    /** 访问时间 */
    private LocalDateTime loginTime;

}
