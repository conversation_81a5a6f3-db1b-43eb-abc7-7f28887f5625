<script setup lang="ts">
import { listDict } from '@/api/system/dict';
import { useDict } from '@/views/system/dict/utils';
// skTable 实例
const { skTable, columns, load, openDialog, handleExport, handleDelete } = useDict();
</script>

<template>
  <div class="table-box">
    <SkTable
      ref="skTable"
      row-key="id"
      :columns="columns"
      :request-api="listDict"
      :search-col="{ xs: 1, sm: 1, md: 2, lg: 3, xl: 3 }"
      lazy
      :load="load"
    >
      <!-- 表格 header 按钮 -->
      <template #tableHeader>
        <el-button v-hasPermi="['system:dict:add']" type="primary" icon="CirclePlus" @click="openDialog('新增')">
          新增
        </el-button>
        <el-button v-hasPermi="['system:dict:export']" type="primary" plain icon="Download" @click="handleExport">
          导出
        </el-button>
      </template>
      <!-- 表格操作 -->
      <template #operation="scope">
        <div>
          <el-button
            v-hasPermi="['system:dict:add']"
            type="primary"
            link
            icon="CirclePlus"
            @click="
              openDialog('新增', {
                parentId: scope.row.id,
                bzlxdm: scope.row.bzlxdm,
                sjlx: '3'
              })
            "
          >
            新增子级
          </el-button>
          <el-button v-hasPermi="['system:dict:edit']" type="primary" link icon="EditPen" @click="openDialog('编辑', scope.row)">
            编辑
          </el-button>
          <el-button v-hasPermi="['system:dict']" type="danger" link icon="Delete" @click="handleDelete(scope.row)">
            删除
          </el-button>
        </div>
      </template>
    </SkTable>
  </div>
</template>
