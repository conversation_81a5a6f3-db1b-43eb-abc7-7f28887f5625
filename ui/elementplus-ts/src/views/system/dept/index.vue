<script setup lang="ts">
import { useDept } from '@/views/system/dept/utils';
import { listDept } from '@/api/system/dept';

defineOptions({
  name: 'Dept'
});
const { skTable, columns, load, isExpandAll, openDialog, handleExport, handleDelete } = useDept();
</script>
<template>
  <div class="table-box">
    <SkTable
      ref="skTable"
      row-key="id"
      :columns="columns"
      :pagination="false"
      :request-api="listDept"
      :search-col="{ xs: 1, sm: 1, md: 2, lg: 3, xl: 3 }"
      :is-expand-all="isExpandAll"
      tree-id="id"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      lazy
      :load="load"
    >
      <!-- 表格 header 按钮 -->
      <template #tableHeader>
        <el-button v-hasPermi="['system:dept:add']" type="primary" icon="CirclePlus" @click="openDialog('新增')">
          新增
        </el-button>
        <el-button v-hasPermi="['system:dept:export']" type="primary" plain icon="Download" @click="handleExport">
          导出
        </el-button>
        <el-button type="primary" plain icon="Sort" @click="isExpandAll = !isExpandAll"> 折叠/展开 </el-button>
      </template>
      <!-- 表格操作 -->
      <template #operation="scope">
        <div>
          <el-button
            v-hasPermi="['system:dept:add']"
            type="primary"
            link
            icon="CirclePlus"
            @click="
              openDialog('新增', {
                parentId: scope.row.id
              })
            "
          >
            新增子级
          </el-button>
          <el-button v-hasPermi="['system:dept:edit']" type="primary" link icon="EditPen" @click="openDialog('编辑', scope.row)">
            编辑
          </el-button>
          <el-button v-hasPermi="['system:dept']" type="danger" link icon="Delete" @click="handleDelete(scope.row)">
            删除
          </el-button>
        </div>
      </template>
    </SkTable>
  </div>
</template>
