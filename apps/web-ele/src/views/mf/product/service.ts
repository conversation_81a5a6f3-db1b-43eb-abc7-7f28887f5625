import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { ProductForm, ProductVO } from '#/api/mf/product/types';
import type { ButtonConfig } from '#/components/action-button-group/types';

import { useVbenDrawer, useVbenForm } from '@vben/common-ui';
import { Delete, Edit, Fold, Plus } from '@vben/icons';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  addProduct,
  editProduct,
  getProduct,
  listProduct,
  removeProduct,
} from '#/api/mf/product';

import { useFormSchema, useGridColumns, useGridFormSchema } from './data';

export const useProductService = () => {
  // 查询表单和表格配置
  const [BasicTable, gridApi] = useVbenVxeGrid({
    formOptions: {
      schema: useGridFormSchema(),
      commonConfig: {
        labelWidth: 120,
        componentProps: {
          allowClear: true,
        },
      },
    },
    gridOptions: {
      columns: useGridColumns(),
      pagerConfig: { enabled: false },
      proxyConfig: {
        ajax: {
          query: async (_, formValues = {}) => {
            return await listProduct({
              ...formValues,
            });
          },
        },
      },
      treeConfig: {
        transform: true,
        rowField: 'id',
        parentField: 'parentId',
        lazy: true,
        hasChildField: 'hasChildren',
        loadMethod({ row }) {
          return load(row);
        },
      },
    } as VxeTableGridOptions,
  });

  // 编辑表单配置
  const [Form, formApi] = useVbenForm({
    schema: useFormSchema(),
    commonConfig: {
      labelWidth: 140,
    },
    showDefaultActions: false,
    wrapperClass: 'md:grid-cols-2',
  });

  // 编辑抽屉配置
  const [FormDialog, formDialogApi] = useVbenDrawer({
    async onConfirm() {
      const { valid } = await formApi.validate();
      if (!valid) return;
      const data = await formApi.getValues<ProductForm>();
      formDialogApi.lock();
      (data?.id ? editProduct(data) : addProduct(data))
        .then(() => {
          onRefresh();
          formDialogApi.close();
        })
        .catch(() => {
          formDialogApi.unlock();
        });
    },
    onOpenChange(isOpen) {
      if (isOpen) {
        const data = formDialogApi.getData<ProductForm>();
        if (data) {
          formApi.setValues(data).then(() => {});
        } else {
          formApi.resetForm().then(() => {});
        }
      }
    },
    destroyOnClose: true,
  });

  // 异步加载方法
  const load = async (row: ProductVO) => {
    return await listProduct({ parentId: row.id });
  };

  /**
   * 刷新表格
   */
  const onRefresh = async () => {
    await gridApi.query();
  };

  /**
   * 创建产品树
   */
  const onCreate = () => {
    formDialogApi.setData(null).open();
  };

  /**
   * 折叠全部
   */
  const collapseAll = () => {
    gridApi.grid?.setAllTreeExpand(false).then(() => {});
  };

  /**
   * 新增子级
   */
  const onAppend = (row: ProductVO) => {
    formDialogApi.setData({ parentId: row.id }).open();
  };

  /**
   * 编辑产品树
   */
  const handleEdit = async (row: ProductVO) => {
    const data = await getProduct(row.id);
    formDialogApi.setData(data).open();
  };

  /**
   * 删除产品树
   */
  const handleDelete = async (row: ProductVO) => {
    await ElMessageBox.confirm('确认删除该产品树吗？', '提示', {
      type: 'warning',
    })
      .then(async () => {
        await removeProduct(row.id);
      })
      .catch(() => {});
  };

  /**
   * 工具栏按钮配置
   */
  const useToolbarButtons = (): ButtonConfig[] => {
    return [
      {
        label: '新增产品树',
        type: 'primary',
        icon: Plus,
        onClick: onCreate,
        auth: ['mf:product:add'],
      },
      {
        label: '折叠全部',
        icon: Fold,
        onClick: collapseAll,
      },
    ];
  };

  /**
   * 行操作按钮配置
   */
  const useRowButtons = (): ButtonConfig[] => {
    return [
      {
        label: '新增子级',
        icon: Plus,
        link: true,
        onClick: onAppend,
        auth: ['mf:product:add'],
      },
      {
        label: '编辑',
        icon: Edit,
        link: true,
        onClick: handleEdit,
        auth: ['mf:product:edit'],
      },
      {
        label: '删除',
        icon: Delete,
        link: true,
        onClick: handleDelete,
        auth: ['mf:product:remove'],
      },
    ];
  };

  return {
    BasicTable,
    Form,
    FormDialog,
    collapseAll,
    handleDelete,
    handleEdit,
    onAppend,
    onCreate,
    onRefresh,
    useToolbarButtons,
    useRowButtons,
  };
};
