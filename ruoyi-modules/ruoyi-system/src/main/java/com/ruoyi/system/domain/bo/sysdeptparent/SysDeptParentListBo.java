package com.ruoyi.system.domain.bo.sysdeptparent;

import com.ruoyi.common.core.core.domain.bo.BaseListBo;
import com.ruoyi.system.domain.po.SysDeptParent;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 上级业务部门业务对象 sys_dept_parent
 *
 * <AUTHOR>
 * @since 2025-05-29 16:59:45
 */
@Data
@AutoMapper(target = SysDeptParent.class, reverseConvertGenerate = false)
public class SysDeptParentListBo extends BaseListBo {

    /**
     * 子部门ID
     */
    @NotBlank(message = "子部门ID不能为空")
    private String deptId;
}
