import type { DeptDataOption } from '#/api/system/dept/types';

export interface RoleVO extends BaseEntity {
  /**
   * 角色ID
   */
  id: number | string;
  /**
   * 角色名称
   */
  roleName: string;
  /**
   * 角色权限字符串
   */
  roleKey: string;
  /**
   * 显示顺序
   */
  roleSort: number;
  /**
   * 角色状态（0正常 1停用）
   */
  status: string;
  /**
   * 创建时间
   */
  createTime: string;
}

export interface RoleForm {
  /**
   * 角色ID
   */
  id?: number | string;
  /**
   * 角色名称
   */
  roleName?: string;
  /**
   * 角色权限字符串
   */
  roleKey?: string;
  /**
   * 显示顺序
   */
  roleSort?: number;
  /**
   * 数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限）
   */
  dataScope?: string;
  /**
   * 菜单树选择项是否关联显示
   */
  menuCheckStrictly?: boolean;
  /**
   * 部门树选择项是否关联显示
   */
  deptCheckStrictly?: boolean;
  /**
   * 角色状态（0正常 1停用）
   */
  status?: string;
  /**
   * 乐观锁
   */
  version?: number;
  /**
   * 备注
   */
  remark?: string;

  menuIds?: Array<string>;
  deptIds?: Array<string>;
}

export interface RoleQuery extends PageQuery {
  /**
   * 角色名称
   */
  roleName?: string;
  /**
   * 角色权限字符串
   */
  roleKey?: string;
  /**
   * 角色状态（0正常 1停用）
   */
  status?: string;
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 日期范围参数
   */
  params?: any;
}

export interface RoleDeptTree {
  checkedKeys: string[];
  depts: DeptDataOption[];
}
