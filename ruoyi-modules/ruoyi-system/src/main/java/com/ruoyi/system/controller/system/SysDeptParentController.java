package com.ruoyi.system.controller.system;

import lombok.RequiredArgsConstructor;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.ruoyi.common.core.core.domain.Result;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.web.annotation.RepeatSubmit;
import com.ruoyi.common.web.core.BaseController;
import jakarta.annotation.Resource;
import com.ruoyi.system.domain.vo.sysdeptparent.SysDeptParentDetailVo;
import com.ruoyi.system.domain.vo.sysdeptparent.SysDeptParentListVo;
import com.ruoyi.system.domain.bo.sysdeptparent.SysDeptParentAddBo;
import com.ruoyi.system.domain.bo.sysdeptparent.SysDeptParentEditBo;
import com.ruoyi.system.domain.bo.sysdeptparent.SysDeptParentListBo;
import com.ruoyi.system.service.SysDeptParentService;

import java.util.List;

/**
 * 上级业务部门控制器
 * <p>
 * 提供上级业务部门地增删改查、导入导出等功能，主要包含：
 * <ul>
 *   <li>上级业务部门地增删改查</li>
 *   <li>上级业务部门的分页查询</li>
 *   <li>上级业务部门的下拉选择</li>
 * </ul>
 *
 * <AUTHOR>
 * @since 2025-05-29 16:59:45
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/deptParent")
public class SysDeptParentController extends BaseController {
    @Resource
    private SysDeptParentService sysDeptParentService;

    /**
     * 分页查询上级业务部门列表
     * <p>
     * 支持多条件组合查询，返回分页数据
     *
     * @param sysDeptParentListBo 查询条件对象，包含分页参数和查询条件
     * @return 包含分页信息和上级业务部门列表的结果对象
     */
    @SaCheckPermission("system:deptParent:list")
    @GetMapping("/list")
    public Result<List<SysDeptParentListVo>> list(SysDeptParentListBo sysDeptParentListBo) {
        return Result.success(sysDeptParentService.list(sysDeptParentListBo));
    }



    /**
     * 新增上级业务部门
     * <p>
     * 包含数据校验和重复提交控制，确保数据的完整性和一致性
     *
     * @param sysDeptParentAddBo 上级业务部门对象，包含必填字段验证
     * @return 操作结果，成功或失败信息
     */
    @SaCheckPermission("system:deptParent:add")
    @Log(title = "上级业务部门", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping
    public Result<Void> add(@Validated @RequestBody SysDeptParentAddBo sysDeptParentAddBo) {
        return sysDeptParentService.add(sysDeptParentAddBo);
    }

    /**
     * 修改上级业务部门
     * <p>
     * 包含数据校验和重复提交控制，确保数据的完整性和一致性
     *
     * @param sysDeptParentEditBo 上级业务部门对象，包含必填字段验证
     * @return 操作结果，成功或失败信息
     */
    @SaCheckPermission("system:deptParent:edit")
    @Log(title = "上级业务部门", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping
    public Result<Void> edit(@Validated @RequestBody SysDeptParentEditBo sysDeptParentEditBo) {
        return sysDeptParentService.edit(sysDeptParentEditBo);
    }

    /**
     * 删除上级业务部门
     * <p>
     * 支持批量删除操作，可同时删除多个上级业务部门
     *
     * @param ids 上级业务部门ID数组，包含要删除的ID列表
     * @return 操作结果，成功或失败信息
     */
    @SaCheckPermission("system:deptParent:remove")
    @Log(title = "上级业务部门", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result<Void> remove(@PathVariable String[] ids) {
        return sysDeptParentService.removeByIds(ids);
    }

    /**
     * 获取上级业务部门详细信息
     * <p>
     * 根据ID查询单个上级业务部门的完整信息，包含所有字段
     *
     * @param id 上级业务部门ID
     * @return 包含上级业务部门所有字段的详细信息对象
     */
    @SaCheckPermission("system:deptParent:query")
    @GetMapping(value = "/{id}")
    public Result<SysDeptParentDetailVo> detail(@PathVariable String id) {
        return Result.success(sysDeptParentService.detail(id));
    }


}
