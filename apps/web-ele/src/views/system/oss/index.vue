<script lang="ts" setup>
import { Page } from '@vben/common-ui';

import { useOssService } from './service';

defineOptions({
  name: 'OssIndex',
});

const {
  BasicTable,
  useToolbarButtons,
  useRowButtons,
  handleQuery,
  ImageUploadModal,
  FileUploadModal,
} = useOssService();
</script>

<template>
  <Page auto-content-height>
    <BasicTable>
      <template #toolbar-actions>
        <ActionButtonGroup :buttons="useToolbarButtons()" />
      </template>

      <template #action="{ row }">
        <ActionButtonGroup :buttons="useRowButtons()" :row="row" />
      </template>
    </BasicTable>
    <ImageUploadModal @reload="handleQuery()" />
    <FileUploadModal @reload="handleQuery()" />
  </Page>
</template>
