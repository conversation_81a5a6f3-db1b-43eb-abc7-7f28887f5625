// 测试 STRING 字段的全角字符错误信息
import { BaseSchema, ZodEnum } from './zod-enum';

console.log('=== 测试 STRING 字段的全角字符错误信息 ===');

// 1. 测试 BaseSchema.STRING
console.log('\n1. BaseSchema.STRING 测试:');

// 1.1 全角数字
const stringFullWidthNumbers = BaseSchema.STRING.safeParse('１２３４５');
console.log('全角数字结果:', stringFullWidthNumbers);
if (!stringFullWidthNumbers.success) {
  console.log('全角数字错误信息:', stringFullWidthNumbers.error.issues[0].message);
  console.log('期望: "不能包含全角字符，请使用半角字符"');
}

// 1.2 中文字符
const stringChinese = BaseSchema.STRING.safeParse('你好世界');
console.log('中文字符结果:', stringChinese);
if (!stringChinese.success) {
  console.log('中文字符错误信息:', stringChinese.error.issues[0].message);
  console.log('期望: "不能包含全角字符，请使用半角字符"');
}

// 1.3 全角字母
const stringFullWidthLetters = BaseSchema.STRING.safeParse('ａｂｃｄｅ');
console.log('全角字母结果:', stringFullWidthLetters);
if (!stringFullWidthLetters.success) {
  console.log('全角字母错误信息:', stringFullWidthLetters.error.issues[0].message);
  console.log('期望: "不能包含全角字符，请使用半角字符"');
}

// 1.4 全角符号
const stringFullWidthSymbols = BaseSchema.STRING.safeParse('！＠＃＄％');
console.log('全角符号结果:', stringFullWidthSymbols);
if (!stringFullWidthSymbols.success) {
  console.log('全角符号错误信息:', stringFullWidthSymbols.error.issues[0].message);
  console.log('期望: "不能包含全角字符，请使用半角字符"');
}

// 1.5 全角空格
const stringFullWidthSpace = BaseSchema.STRING.safeParse('hello　world');
console.log('全角空格结果:', stringFullWidthSpace);
if (!stringFullWidthSpace.success) {
  console.log('全角空格错误信息:', stringFullWidthSpace.error.issues[0].message);
  console.log('期望: "不能包含全角字符，请使用半角字符"');
}

// 1.6 半角字符（应该通过）
const stringHalfWidth = BaseSchema.STRING.safeParse('hello world 123');
console.log('半角字符结果:', stringHalfWidth);
console.log('期望: success: true');

// 1.7 空字符串（应该通过）
const stringEmpty = BaseSchema.STRING.safeParse('');
console.log('空字符串结果:', stringEmpty);
console.log('期望: success: true');

// 2. 测试 ZodEnum.STRING
console.log('\n2. ZodEnum.STRING 测试:');

const zodStringFullWidth = ZodEnum.STRING.safeParse('１２３４５');
console.log('ZodEnum.STRING 全角数字结果:', zodStringFullWidth);
if (!zodStringFullWidth.success) {
  console.log('ZodEnum.STRING 全角数字错误信息:', zodStringFullWidth.error.issues[0].message);
  console.log('期望: "不能包含全角字符，请使用半角字符"');
}

// 3. 测试混合字符
console.log('\n3. 混合字符测试:');

const mixedChars = BaseSchema.STRING.safeParse('hello你好world');
console.log('混合字符结果:', mixedChars);
if (!mixedChars.success) {
  console.log('混合字符错误信息:', mixedChars.error.issues[0].message);
  console.log('期望: "不能包含全角字符，请使用半角字符"');
}

// 4. 测试特殊的全角字符
console.log('\n4. 特殊全角字符测试:');

const specialChars = [
  '　', // 全角空格
  '，', // 全角逗号
  '。', // 全角句号
  '？', // 全角问号
  '！', // 全角感叹号
  '（）', // 全角括号
  '【】', // 全角方括号
];

specialChars.forEach((char, index) => {
  const result = BaseSchema.STRING.safeParse(char);
  console.log(`特殊字符 ${index + 1} "${char}":`, result.success ? '通过' : result.error.issues[0].message);
});

// 5. 对比测试：noFullWidthString vs withNoFullWidth
console.log('\n5. 对比测试:');

// 导入 noFullWidthString 进行对比
import { noFullWidthString } from './zod-enum';

const noFullWidthResult = noFullWidthString().safeParse('１２３４５');
console.log('noFullWidthString 结果:', noFullWidthResult);
if (!noFullWidthResult.success) {
  console.log('noFullWidthString 错误信息:', noFullWidthResult.error.issues[0].message);
}

const withNoFullWidthResult = BaseSchema.STRING.safeParse('１２３４５');
console.log('withNoFullWidth 结果:', withNoFullWidthResult);
if (!withNoFullWidthResult.success) {
  console.log('withNoFullWidth 错误信息:', withNoFullWidthResult.error.issues[0].message);
}

export {
  stringFullWidthNumbers,
  stringChinese,
  stringFullWidthLetters,
  stringHalfWidth,
  mixedChars
};
