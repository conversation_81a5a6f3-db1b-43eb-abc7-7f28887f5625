package com.ruoyi.mf.domain.vo.mfcustomer;

import java.util.Date;
import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.ruoyi.common.excel.annotation.ExcelDictFormat;
import com.ruoyi.common.excel.convert.ExcelDictConvert;
import lombok.Data;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;

import com.mybatisflex.annotation.RelationOneToMany;
import com.ruoyi.mf.domain.po.MfGoods;
import com.ruoyi.common.orm.core.domain.BaseEntity;

/**
 * 客户主表视图对象 mf_customer
 *
 * <AUTHOR>
 * @since 2025-03-17 17:46:47
 */
@Data
@ExcelIgnoreUnannotated
public class MfCustomerExcelVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

     /** 客户id */
    @ExcelProperty(value = "客户id")
    private String id;

     /** 租户编码 */
    @ExcelProperty(value = "租户编码")
    private String tenantId;

     /** 客户姓名 */
    @ExcelProperty(value = "客户姓名")
    private String customerName;

     /** 手机号码 */
    @ExcelProperty(value = "手机号码")
    private String phonenumber;

     /** 客户性别 */
    @ExcelProperty(value = "客户性别", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "xb")
    private String gender;

     /** 客户生日 */
    @ExcelProperty(value = "客户生日")
    private Date birthday;

     /** 客户描述 */
    @ExcelProperty(value = "客户描述")
    private String remark;

     /** 乐观锁 */
    @ExcelProperty(value = "乐观锁")
    private Integer version;

     /** 删除标志（1代表存在 0代表删除） */
    @ExcelProperty(value = "删除标志（1代表存在 0代表删除）")
    private Integer jlzt;

     /** 创建者 */
    @ExcelProperty(value = "创建者")
    private String createBy;

     /** 创建时间 */
    @ExcelProperty(value = "创建时间")
    private Date createTime;

     /** 更新者 */
    @ExcelProperty(value = "更新者")
    private String updateBy;

     /** 更新时间 */
    @ExcelProperty(value = "更新时间")
    private Date updateTime;


    /** 商品子信息 */
    @RelationOneToMany(selfField = "id", targetField = "id")
    private List<MfGoods> mfGoodsList;

}
