// 使用示例：如何用 withNoFullWidth 包装器来实现全角字符验证

import { z } from '#/adapter/form';
import { withNoFullWidth, optional } from './zod-enum';

// 原来的写法
const originalSchema = {
  STRING: z.string(),
  EMAIL: z.string().email('请输入正确的邮箱'),
  PHONE: z.string().regex(/^1[3-9]\d{9}$/, '请输入正确的手机号'),
};

// 使用 withNoFullWidth 包装器的写法
const newSchema = {
  STRING: withNoFullWidth(z.string()),
  EMAIL: withNoFullWidth(z.string().email('请输入正确的邮箱')),
  PHONE: withNoFullWidth(z.string().regex(/^1[3-9]\d{9}$/, '请输入正确的手机号')),
};

// 自定义错误消息的写法
const customMessageSchema = {
  USERNAME: withNoFullWidth(z.string(), '用户名不能包含中文字符'),
  PASSWORD: withNoFullWidth(z.string(), '密码不能包含全角字符'),
};

// 与 optional 包装器组合使用
const optionalSchema = {
  DESCRIPTION: optional(withNoFullWidth(z.string(), '描述不能包含全角字符')),
  REMARK: optional(withNoFullWidth(z.string())),
};

// 使用示例
export const ExampleUsage = {
  // 基础用法
  basic: newSchema,

  // 自定义消息
  custom: customMessageSchema,

  // 可选字段
  optional: optionalSchema,

  // 完整的表单 schema 示例
  userForm: z.object({
    username: withNoFullWidth(z.string(), '用户名不能包含中文字符'),
    email: withNoFullWidth(z.string().email('请输入正确的邮箱')),
    phone: optional(withNoFullWidth(z.string().regex(/^1[3-9]\d{9}$/, '请输入正确的手机号'))),
    description: optional(withNoFullWidth(z.string())),
  }),
};

// 验证示例
const testValidation = () => {
  const schema = withNoFullWidth(z.string());

  // 这些会通过验证
  console.log('✅ 通过:', schema.safeParse('hello')); // 半角字符
  console.log('✅ 通过:', schema.safeParse('Hello123')); // 半角字符和数字
  console.log('✅ 通过:', schema.safeParse('')); // 空字符串
  console.log('✅ 通过:', schema.safeParse('<EMAIL>')); // 半角邮箱

  // 这些会失败
  console.log('❌ 失败:', schema.safeParse('你好')); // 中文字符
  console.log('❌ 失败:', schema.safeParse('hello世界')); // 混合字符
  console.log('❌ 失败:', schema.safeParse('　')); // 全角空格
  console.log('❌ 失败:', schema.safeParse('！')); // 全角感叹号
};

export { testValidation };
