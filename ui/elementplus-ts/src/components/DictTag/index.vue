<script setup lang="ts">
import { getDictDataOption } from '@/utils/dict';

interface Props {
  data: string;
  value?: number | string | Array<number | string>;
  showValue?: boolean;
  separator?: string;
}

const props = withDefaults(defineProps<Props>(), {
  showValue: true,
  separator: ','
});
const options = ref([]);
const values = computed(() => {
  if (props.value === '' || props.value === null || typeof props.value === 'undefined') return [];
  return Array.isArray(props.value) ? props.value.map((item) => '' + item) : String(props.value).split(props.separator);
});
const getDict = async () => {
  options.value = await getDictDataOption({ lx: props.data });
};
onMounted(() => {
  getDict();
});
</script>
<template>
  <div>
    <template v-for="(item, index) in options">
      <template v-if="values.includes(item.value)">
        <span
          v-if="(item.elTagType === 'default' || item.elTagType === '') && (item.elTagClass === '' || item.elTagClass == null)"
          :key="item.value"
          :index="index"
          :class="item.elTagClass"
        >
          {{ item.label + ' ' }}
        </span>
        <el-tag
          v-else
          :key="item.value + ''"
          :disable-transitions="true"
          :index="index"
          :type="item.elTagType === 'primary' || item.elTagType === 'default' ? 'primary' : item.elTagType"
          :class="item.elTagClass"
        >
          {{ item.label + ' ' }}
        </el-tag>
      </template>
    </template>
  </div>
</template>

<style scoped>
.el-tag + .el-tag {
  margin-left: 10px;
}
</style>
