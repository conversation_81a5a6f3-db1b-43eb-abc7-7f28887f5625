import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { VbenFormSchema } from '#/adapter/form';

import { renderDictTag } from '#/utils/dict';

/**
 * 获取编辑表单的字段配置
 * @returns VbenFormSchema[] 表单配置
 */
export function useFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      dependencies: {
        show: () => false,
        triggerFields: [''],
      },
      fieldName: 'id',
      label: '日志主键',
    },
    {
      component: 'Input',
      fieldName: 'title',
      label: '系统模块',
      rules: 'required',
      componentProps: {
        placeholder: '请输入系统模块',
        clearable: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'method',
      label: '方法名称',
      rules: 'required',
      componentProps: {
        placeholder: '请输入方法名称',
        clearable: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'requestMethod',
      label: '请求方式',
      rules: 'required',
      componentProps: {
        placeholder: '请输入请求方式',
        clearable: true,
      },
    },
    {
      component: 'SkDict',
      fieldName: 'operatorType',
      label: '操作类别',
      rules: 'required',
      componentProps: {
        data: 'czlx',
        placeholder: '请选择操作类别',
        clearable: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'operName',
      label: '操作人员',
      rules: 'required',
      componentProps: {
        placeholder: '请输入操作人员',
        clearable: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'deptName',
      label: '部门名称',
      rules: 'required',
      componentProps: {
        placeholder: '请输入部门名称',
        clearable: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'operUrl',
      label: '请求URL',
      rules: 'required',
      componentProps: {
        placeholder: '请输入请求URL',
        clearable: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'operIp',
      label: '主机地址',
      rules: 'required',
      componentProps: {
        placeholder: '请输入主机地址',
        clearable: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'operLocation',
      label: '操作地点',
      rules: 'required',
      componentProps: {
        placeholder: '请输入操作地点',
        clearable: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'operParam',
      label: '请求参数',
      rules: 'required',
      componentProps: {
        placeholder: '请输入请求参数',
        clearable: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'jsonResult',
      label: '返回参数',
      rules: 'required',
      componentProps: {
        placeholder: '请输入返回参数',
        clearable: true,
      },
    },
    {
      component: 'SkDict',
      fieldName: 'status',
      label: '操作状态',
      rules: 'required',
      componentProps: {
        data: 'xtzt',
        placeholder: '请选择操作状态',
        clearable: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'errorMsg',
      label: '错误消息',
      rules: 'required',
      componentProps: {
        placeholder: '请输入错误消息',
        clearable: true,
      },
    },
    {
      component: 'DatePicker',
      fieldName: 'operTime',
      label: '操作时间',
      rules: 'required',
      componentProps: {
        type: 'date',
        valueFormat: 'YYYY-MM-DD',
        placeholder: '请选择操作时间',
        clearable: true,
      },
    },
  ];
}

/**
 * 获取查询表单的字段配置operation-preview-drawer.vue
 * @returns VbenFormSchema[] 查询表单配置
 */
export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'title',
      label: '系统模块',
      componentProps: {
        placeholder: '请输入系统模块',
        clearable: true,
      },
    },
    {
      component: 'SkDict',
      fieldName: 'operatorType',
      label: '操作类别',
      componentProps: {
        data: 'czlx',
        placeholder: '请选择操作类别',
        clearable: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'operName',
      label: '操作人员',
      componentProps: {
        placeholder: '请输入操作人员',
        clearable: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'operIp',
      label: '主机地址',
      componentProps: {
        placeholder: '请输入主机地址',
        clearable: true,
      },
    },
    {
      component: 'SkDict',
      fieldName: 'status',
      label: '操作状态',
      componentProps: {
        data: 'xtzt',
        placeholder: '请选择操作状态',
        clearable: true,
      },
    },
    {
      component: 'DatePicker',
      fieldName: 'operTime',
      label: '操作时间',
      componentProps: {
        type: 'daterange',
        valueFormat: 'YYYY-MM-DD HH:mm:ss',
        rangeSeparator: '-',
        startPlaceholder: '开始日期',
        endPlaceholder: '结束日期',
        clearable: true,
      },
    },
  ];
}

/**
 * 获取表格列配置
 * @returns VxeTableGridOptions['columns'] 表格列配置
 * @description 使用函数的形式返回列数据而不是直接export一个Array常量，是为了响应语言切换时重新翻译表头
 */
export function useGridColumns(): VxeTableGridOptions['columns'] {
  return [
    {
      type: 'checkbox',
      width: 60,
    },
    {
      title: '#',
      type: 'seq',
      width: 60,
      fixed: 'left',
    },
    {
      field: 'title',
      title: '系统模块',
      align: 'center',
    },
    {
      field: 'operatorType',
      title: '操作类别',
      align: 'center',
      slots: {
        default: ({ row }) => {
          return renderDictTag(row.operatorType, 'czlx');
        },
      },
    },
    {
      field: 'operName',
      title: '操作人员',
      align: 'center',
    },
    {
      field: 'deptName',
      title: '部门名称',
      align: 'center',
    },
    {
      field: 'operIp',
      title: '主机地址',
      align: 'center',
    },
    {
      field: 'status',
      title: '操作状态',
      align: 'center',
      slots: {
        default: ({ row }) => {
          return renderDictTag(row.status, 'xtzt');
        },
      },
    },
    {
      field: 'operTime',
      title: '操作时间',
      align: 'center',
    },
    {
      field: 'costTime',
      title: '消耗时间',
      align: 'center',
    },
    {
      title: '操作',
      field: 'action',
      fixed: 'right',
      slots: { default: 'action' },
      width: 240,
    },
  ];
}
