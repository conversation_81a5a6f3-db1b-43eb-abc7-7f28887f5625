import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { UserVO } from '#/api/system/user/types';
import type { ButtonConfig } from '#/components/action-button-group/types';

import { useTabs } from '@vben/hooks';
import { Delete, Plus } from '@vben/icons';

import { useVbenVxeGrid, vxeCheckboxChecked } from '#/adapter/vxe-table';
import {
  allocatedUserList,
  authUserCancel,
  authUserCancelAll,
} from '#/api/system/role';

import { useGridColumns, useGridFormSchema } from './data';

export const useUserService = () => {
  const route = useRoute();
  const roleId = route.params.roleId as string;
  const addUserRef = ref();
  const { closeCurrentTab } = useTabs();
  // 查询表单和表格配置
  const [BasicTable, gridApi] = useVbenVxeGrid({
    formOptions: {
      schema: useGridFormSchema(),
      commonConfig: {
        labelWidth: 120,
        componentProps: {
          allowClear: true,
        },
      },
    },
    gridOptions: {
      columns: useGridColumns(),
      pagerConfig: { enabled: true },
      proxyConfig: {
        ajax: {
          query: async ({ page }, formValues = {}) => {
            return await allocatedUserList({
              pageNum: page.currentPage,
              pageSize: page.pageSize,
              roleId,
              ...formValues,
            });
          },
        },
      },
      rowConfig: {
        keyField: 'id',
      },
      id: 'system-role-assign-index',
    } as VxeTableGridOptions,
  });

  /**
   * 刷新表格
   */
  const onRefresh = async () => {
    await gridApi.query();
  };

  // 添加成员
  const handleAdd = () => {
    addUserRef.value.open();
  };

  /**
   * 取消授权 一条记录
   */
  async function handleAuthCancel(row: UserVO) {
    await window.skynet
      .confirm(`确认要取消该用户"${row.userName}"角色吗？`)
      .then(async () => {
        await authUserCancel({ userId: row.id, roleId });
        await onRefresh();
      })
      .catch(() => {});
  }

  /**
   * 批量取消授权
   */
  async function handleMultipleAuthCancel() {
    const rows = gridApi.grid.getCheckboxRecords();
    const ids = rows.map((row: UserVO) => row.id);

    await window.skynet
      .confirm('是否取消选中用户授权数据项?')
      .then(async () => {
        await authUserCancelAll({ roleId, userIds: ids });
        await onRefresh();
        await gridApi.grid.clearCheckboxRow();
      })
      .catch(() => {});
  }

  /**
   * 工具栏按钮配置
   */
  const useToolbarButtons = (): ButtonConfig[] => {
    return [
      {
        label: '添加用户',
        type: 'primary',
        icon: Plus,
        onClick: handleAdd,
        auth: ['system:user:add'],
      },
      {
        label: '批量取消授权',
        type: 'primary',
        disabled: !vxeCheckboxChecked(gridApi),
        icon: Plus,
        onClick: handleMultipleAuthCancel,
        auth: ['system:user:add'],
      },
      {
        label: '关闭',
        type: 'primary',
        icon: Plus,
        onClick: closeCurrentTab,
        auth: ['system:user:add'],
      },
    ];
  };

  /**
   * 行操作按钮配置
   */
  const useRowButtons = (): ButtonConfig[] => {
    return [
      {
        label: '取消授权',
        icon: Delete,
        link: true,
        onClick: handleAuthCancel,
        auth: ['system:user:remove'],
      },
    ];
  };

  return {
    BasicTable,
    onRefresh,
    useToolbarButtons,
    useRowButtons,
    addUserRef,
  };
};
