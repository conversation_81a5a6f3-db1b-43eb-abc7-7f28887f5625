// 测试 superRefine 实现的效果
import { BaseSchema } from './zod-enum';

console.log('=== 测试 superRefine 实现的效果 ===');

// 1. 测试 PHONE 字段
console.log('\n1. PHONE 字段测试:');

// 1.1 全角数字 - 应该显示全角字符错误
const phoneFullWidth = BaseSchema.PHONE.safeParse('１３８１２３４５６７８');
console.log('PHONE 全角数字结果:', phoneFullWidth);
if (!phoneFullWidth.success) {
  console.log('PHONE 全角数字错误信息:', phoneFullWidth.error.issues[0].message);
  console.log('期望: "不能包含全角字符，请使用半角字符"');
  console.log('✅ 正确:', phoneFullWidth.error.issues[0].message === '不能包含全角字符，请使用半角字符');
}

// 1.2 半角但格式错误 - 应该显示手机号格式错误
const phoneFormat = BaseSchema.PHONE.safeParse('12345678901');
console.log('\nPHONE 格式错误结果:', phoneFormat);
if (!phoneFormat.success) {
  console.log('PHONE 格式错误信息:', phoneFormat.error.issues[0].message);
  console.log('期望: "请输入正确的手机号"');
  console.log('✅ 正确:', phoneFormat.error.issues[0].message === '请输入正确的手机号');
}

// 1.3 正确的手机号
const phoneCorrect = BaseSchema.PHONE.safeParse('13812345678');
console.log('\nPHONE 正确结果:', phoneCorrect.success ? '✅ 通过' : '❌ 失败');

// 2. 测试 GAJGJGDM 字段
console.log('\n2. GAJGJGDM 字段测试:');

// 2.1 全角数字 - 应该显示全角字符错误
const gajgjgdmFullWidth = BaseSchema.GAJGJGDM.safeParse('１１１１１１１１１１１１');
console.log('GAJGJGDM 全角数字结果:', gajgjgdmFullWidth);
if (!gajgjgdmFullWidth.success) {
  console.log('GAJGJGDM 全角数字错误信息:', gajgjgdmFullWidth.error.issues[0].message);
  console.log('期望: "不能包含全角字符，请使用半角字符"');
  console.log('✅ 正确:', gajgjgdmFullWidth.error.issues[0].message === '不能包含全角字符，请使用半角字符');
}

// 2.2 半角但长度错误 - 应该显示长度错误
const gajgjgdmLength = BaseSchema.GAJGJGDM.safeParse('12345');
console.log('\nGAJGJGDM 长度错误结果:', gajgjgdmLength);
if (!gajgjgdmLength.success) {
  console.log('GAJGJGDM 长度错误信息:', gajgjgdmLength.error.issues[0].message);
  console.log('期望: "请输入12位数字"');
  console.log('✅ 正确:', gajgjgdmLength.error.issues[0].message === '请输入12位数字');
}

// 2.3 半角但格式错误 - 应该显示格式错误
const gajgjgdmFormat = BaseSchema.GAJGJGDM.safeParse('abcdefghijkl');
console.log('\nGAJGJGDM 格式错误结果:', gajgjgdmFormat);
if (!gajgjgdmFormat.success) {
  console.log('GAJGJGDM 格式错误信息:', gajgjgdmFormat.error.issues[0].message);
  console.log('期望: "请输入12位数字"');
  console.log('✅ 正确:', gajgjgdmFormat.error.issues[0].message === '请输入12位数字');
}

// 2.4 正确的GAJGJGDM
const gajgjgdmCorrect = BaseSchema.GAJGJGDM.safeParse('123456789012');
console.log('\nGAJGJGDM 正确结果:', gajgjgdmCorrect.success ? '✅ 通过' : '❌ 失败');

// 3. 测试 EMAIL 字段
console.log('\n3. EMAIL 字段测试:');

// 3.1 全角字符 - 应该显示全角字符错误
const emailFullWidth = BaseSchema.EMAIL.safeParse('测试@example.com');
console.log('EMAIL 全角字符结果:', emailFullWidth);
if (!emailFullWidth.success) {
  console.log('EMAIL 全角字符错误信息:', emailFullWidth.error.issues[0].message);
  console.log('期望: "不能包含全角字符，请使用半角字符"');
  console.log('✅ 正确:', emailFullWidth.error.issues[0].message === '不能包含全角字符，请使用半角字符');
}

// 3.2 半角但格式错误 - 应该显示邮箱格式错误
const emailFormat = BaseSchema.EMAIL.safeParse('invalid-email');
console.log('\nEMAIL 格式错误结果:', emailFormat);
if (!emailFormat.success) {
  console.log('EMAIL 格式错误信息:', emailFormat.error.issues[0].message);
  console.log('期望: "请输入正确的邮箱"');
  console.log('✅ 正确:', emailFormat.error.issues[0].message === '请输入正确的邮箱');
}

// 3.3 正确的邮箱
const emailCorrect = BaseSchema.EMAIL.safeParse('<EMAIL>');
console.log('\nEMAIL 正确结果:', emailCorrect.success ? '✅ 通过' : '❌ 失败');

// 4. 测试 STRING 字段
console.log('\n4. STRING 字段测试:');

// 4.1 全角字符 - 应该显示全角字符错误
const stringFullWidth = BaseSchema.STRING.safeParse('你好世界');
console.log('STRING 全角字符结果:', stringFullWidth);
if (!stringFullWidth.success) {
  console.log('STRING 全角字符错误信息:', stringFullWidth.error.issues[0].message);
  console.log('期望: "不能包含全角字符，请使用半角字符"');
  console.log('✅ 正确:', stringFullWidth.error.issues[0].message === '不能包含全角字符，请使用半角字符');
}

// 4.2 半角字符 - 应该通过
const stringHalfWidth = BaseSchema.STRING.safeParse('hello world');
console.log('\nSTRING 半角字符结果:', stringHalfWidth.success ? '✅ 通过' : '❌ 失败');

// 5. 特殊测试案例
console.log('\n5. 特殊测试案例:');

// 5.1 混合字符
const mixedChars = BaseSchema.STRING.safeParse('hello你好world');
console.log('混合字符结果:', mixedChars.success ? '通过' : mixedChars.error.issues[0].message);

// 5.2 全角空格
const fullWidthSpace = BaseSchema.STRING.safeParse('hello　world');
console.log('全角空格结果:', fullWidthSpace.success ? '通过' : fullWidthSpace.error.issues[0].message);

// 5.3 全角符号
const fullWidthSymbols = BaseSchema.STRING.safeParse('！＠＃＄％');
console.log('全角符号结果:', fullWidthSymbols.success ? '通过' : fullWidthSymbols.error.issues[0].message);

// 5.4 空字符串
const emptyString = BaseSchema.STRING.safeParse('');
console.log('空字符串结果:', emptyString.success ? '✅ 通过' : '❌ 失败');

// 6. 验证优先级
console.log('\n6. 验证优先级测试:');

// 6.1 全角数字但长度正确 - 应该显示全角字符错误而不是格式错误
const fullWidthCorrectLength = BaseSchema.GAJGJGDM.safeParse('１２３４５６７８９０１２');
console.log('全角数字正确长度结果:', fullWidthCorrectLength);
if (!fullWidthCorrectLength.success) {
  console.log('错误信息:', fullWidthCorrectLength.error.issues[0].message);
  console.log('应该是全角字符错误，不是格式错误');
}

// 6.2 全角邮箱格式正确 - 应该显示全角字符错误而不是邮箱格式错误
const fullWidthEmail = BaseSchema.EMAIL.safeParse('测试＠ｅｘａｍｐｌｅ．ｃｏｍ');
console.log('全角邮箱结果:', fullWidthEmail);
if (!fullWidthEmail.success) {
  console.log('错误信息:', fullWidthEmail.error.issues[0].message);
  console.log('应该是全角字符错误，不是邮箱格式错误');
}

console.log('\n=== 测试完成 ===');
console.log('如果所有测试都显示正确的错误信息，说明 superRefine 实现成功！');

export {
  phoneFullWidth,
  phoneFormat,
  gajgjgdmFullWidth,
  gajgjgdmLength,
  emailFullWidth,
  emailFormat,
  stringFullWidth
};
