import type { OperLogQuery, OperLogVO } from '#/api/monitor/operlog/types';

import { requestClient } from '#/api/request';

/**
 * 查询操作日志记录列表
 * @param query
 */
export const listOperLog = (query?: OperLogQuery) => {
  return requestClient.get<PageData<OperLogVO>>('/monitor/operlog/list', query);
};

/**
 * 查询操作日志记录详细
 * @param id
 */
export const getOperLog = (id: number | string) => {
  return requestClient.get<OperLogVO>(`/monitor/operlog/${id}`);
};

/**
 * 删除操作日志记录权限
 * @param id
 */
export const removeOperLog = (id: Array<number | string> | number | string) => {
  return requestClient.delete(`/monitor/operlog/${id}`);
};
