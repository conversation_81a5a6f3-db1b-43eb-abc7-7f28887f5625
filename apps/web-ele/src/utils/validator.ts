import type { FormItemRule } from 'element-plus';

export const validator = () => {
  const required = (message?: string): FormItemRule => {
    return {
      required: true,
      message: `${message}为必填项！`,
    };
  };

  const lengthRange = (
    min: number,
    max: number,
    message?: string,
  ): FormItemRule => {
    return {
      min,
      max,
      message,
    };
  };

  const notSpace = (message?: string): FormItemRule => {
    return {
      validator: (_, val, callback) => {
        if (val?.indexOf(' ') === -1) {
          callback();
        } else {
          callback(new Error(message));
        }
      },
    };
  };

  const notSpecialCharacters = (message?: string): FormItemRule => {
    return {
      validator: (_, val, callback) => {
        if (/[`~!@#$%^&*()_+<>?:"{},./;'[\]]/.test(val)) {
          callback(new Error(message));
        } else {
          callback();
        }
      },
    };
  };

  const phone = (message?: string): FormItemRule => {
    return {
      validator: (_, val, callback) => {
        if (!val) return callback();
        if (/^1[3-9]\d{9}$/.test(val)) {
          callback();
        } else {
          callback(new Error(message || '请输入正确的手机号码'));
        }
      },
    };
  };

  const email = (message?: string): FormItemRule => {
    return {
      validator: (_, val, callback) => {
        if (!val) return callback();
        if (/^(?:\w-*\.*)+@(?:\w-?)+(?:\.\w{2,})+$/.test(val)) {
          callback();
        } else {
          callback(new Error(message || '请输入正确的邮箱'));
        }
      },
    };
  };

  const maxlength = (max: number): FormItemRule => {
    return {
      max,
      message: `长度不能超过${max}个字符`,
    };
  };

  const check = (message?: string): FormItemRule => {
    return {
      validator: (_, val, callback) => {
        if (val) {
          callback();
        } else {
          callback(new Error(message));
        }
      },
    };
  };

  return {
    required,
    lengthRange,
    notSpace,
    notSpecialCharacters,
    phone,
    email,
    maxlength,
    check,
  };
};
