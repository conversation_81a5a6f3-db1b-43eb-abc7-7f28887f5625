export interface XzqhVO extends BaseEntity {
  /**
   * 主键
   */
  id: string;

  /**
   * 父结点ID
   */
  parentId?: string;
  /**
   * 名称
   */
  mc: string;

  /**
   * 简称
   */
  jc: string;

  /**
   * 省份简称
   */
  sfjc: string;

  /**
   * 地址元素类型代码
   */
  dzyslxdm: string;

  /**
   * 使用状态代码
   */
  syztdm: string;

  /**
   * 显示顺序
   */
  orderNum: number;

  /**
   * 子树对象
   */
  children: XzqhVO[];
}

export interface XzqhForm {
  /**
   * 主键
   */
  id?: string;

  /**
   * 名称
   */
  mc?: string;

  /**
   * 简称
   */
  jc?: string;

  /**
   * 省份简称
   */
  sfjc?: string;

  /**
   * 父结点ID
   */
  parentId?: string;

  /**
   * 地址元素类型代码
   */
  dzyslxdm?: string;

  /**
   * 使用状态代码
   */
  syztdm?: string;

  /**
   * 乐观锁
   */
  version?: number;

  /**
   * 显示顺序
   */
  orderNum?: number;

  /**
   * 祖级列表
   */
  ancestors?: string;
}

/**
 * 行政区划管理选项数据
 */
export interface XzqhDataOption extends BaseDataOption {
  children?: XzqhDataOption[];
}

export interface XzqhQuery {
  /**
   * 名称
   */
  mc?: string;

  /**
   * 简称
   */
  jc?: string;

  /**
   * 省份简称
   */
  sfjc?: string;

  /**
   * 父结点ID
   */
  parentId?: string;

  /**
   * 地址元素类型代码
   */
  dzyslxdm?: string;

  /**
   * 使用状态代码
   */
  syztdm?: string;

  /**
   * 是否树形结构
   */
  isTree?: boolean;
  /**
   * 日期范围参数
   */
  params?: any;
}
