import type { ColumnProps, SkTableInstance } from '@/components/SkUi/SkTable/interface';
import { addTenant, changeTenantStatus, delTenant, getTenant, syncTenantPackage, updateTenant } from '@/api/system/tenant';
import type { TenantForm, TenantVO } from '@/api/system/tenant/types';
import TenantDialog from '@/views/system/tenant/components/TenantDialog.vue';
import { addDialog } from '@/components/SkUi/SkReDialog';
import { h, reactive, ref } from 'vue';
import { useValidator } from '@/hooks/skUi/web/useValidator';
import { selectTenantPackage } from '@/api/system/tenantPackage';

export function useTenant() {
  const skTable = ref<SkTableInstance>();
  const { required, lengthRange } = useValidator();

  /**
   * 查询列配置
   */
  const columns = reactive<ColumnProps<TenantVO>[]>([
    { type: 'selection', label: '#', width: 55 },
    { type: 'index', label: '#', width: 80 },
    { prop: 'id', label: '租户编号', align: 'center' },
    { prop: 'contactUserName', label: '联系人', align: 'center', search: { el: 'input' } },
    { prop: 'contactPhone', label: '联系电话', align: 'center', search: { el: 'input' } },
    { prop: 'companyName', label: '企业名称', align: 'center', search: { el: 'input' } },
    { prop: 'licenseNumber', label: '社会信用代码', align: 'center' },
    { prop: 'expireTime', label: '过期时间', align: 'center' },
    { prop: 'status', label: '租户状态', tag: true, align: 'center', search: { el: 'sk-dict', props: { data: 'xtkg' } } },
    { prop: 'operation', label: '操作', width: 260, fixed: 'right' }
  ]);

  const rules = reactive({
    id: [required('租户编号不能为空')],
    contactUserName: [required('联系人不能为空')],
    contactPhone: [required('联系电话不能为空')],
    companyName: [required('企业名称不能为空')],
    username: [required('用户名不能为空'), lengthRange(2, 20, '用户名称长度必须介于')],
    password: [required('密码不能为空'), lengthRange(5, 20, '用户密码长度必须介于 5 和 20 之间')]
  });
  /** 查询字典管理下拉树结构 */
  const getTreeSelect = async () => {
    const res = await selectTenantPackage();
    return res.data;
  };

  /**
   * 初始化项目
   * @param tentVO
   */
  const initFormProps = async (tentVO: Partial<TenantVO>) => {
    //初始化默认值
    const formInline = ref<TenantForm>({});
    if (tentVO.id) {
      const res = await getTenant(tentVO.id);
      formInline.value = structuredClone(res.data);
      return formInline;
    }
    return formInline;
  };

  /***
   * 编辑，新增,数据交换
   */
  const openDialog = async (title: string, row: Partial<TenantVO> = {}) => {
    addDialog({
      title: title + '租户',
      width: '1024px',
      props: {
        model: await initFormProps(row),
        labelWidth: '140px',
        rules: rules,
        packageList: await getTreeSelect()
      },
      contentRenderer: () => h(TenantDialog),
      beforeSure: async (done, { options }, formRef) => {
        const curData = options.props.model as TenantForm;
        formRef.validate(async (valid: boolean) => {
          if (valid) {
            const api = title === '新增' ? addTenant : updateTenant;
            await api(curData).then((res: any) => {
              if (res.code === 200) {
                window.skynet.alert(`${title}成功！`);
                done();
                skTable.value?.getTableList();
              }
            });
          }
        });
      }
    });
  };

  /** 同步租户套餐按钮操作 */
  const handleSyncTenantPackage = async (row: TenantVO) => {
    try {
      await window.skynet.confirm('是否确认同步租户套餐租户编号为"' + row.id + '"的数据项？');
      await syncTenantPackage(row.id, row.packageId);
      await skTable.value?.getTableList();
      window.skynet.alert('同步成功');
    } catch {
      return;
    } finally {
    }
  };

  // 租户套餐状态修改
  const handleStatusChange = async (row: TenantVO) => {
    let text = row.status === '0' ? '启用' : '停用';
    try {
      await window.skynet.confirm('确认要"' + text + '""' + row.companyName + '"租户吗？');
      const res = await changeTenantStatus(row.id, row.version, row.status);
      if (res.code == 200) {
        window.skynet.alert(text + '成功');
        await skTable.value?.getTableList();
      } else {
        window.skynet.alert(text + '失败');
      }
    } catch {
      row.status = row.status === '0' ? '1' : '0';
    }
  };

  /** 导出按钮操作 */
  const handleExport = () => {
    window.skynet.download(
      'system/tenant/export',
      {
        ...skTable.value.searchParam
      },
      `tent_${new Date().getTime()}.xlsx`
    );
  };

  /** 删除按钮操作 */
  const handleDelete = async (id: string | number | Array<string | number>) => {
    await window.skynet
      .confirm('是否确认删除租户管理编号为"' + id + '"的数据项？')
      .then(async () => {
        await delTenant(id);
        window.skynet.alert('删除成功');
        await skTable.value?.getTableList();
      })
      .catch(() => {});
  };
  return {
    skTable,
    columns,
    rules,
    openDialog,
    handleExport,
    handleDelete,
    handleSyncTenantPackage,
    handleStatusChange
  };
}
