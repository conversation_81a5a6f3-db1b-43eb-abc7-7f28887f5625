.dataVisualize-box {
  .top-box {
    box-sizing: border-box;
    padding: 25px 40px 0;
    margin-bottom: 10px;
    .top-title {
      margin-bottom: 10px;
      font-family: DIN;
      font-size: 18px;
      font-weight: bold;
    }
    .top-content {
      margin-top: 10px;
      .item-left {
        box-sizing: border-box;
        height: 100%;
        padding: 40px 0 30px 30px;
        overflow: hidden;
        color: #ffffff;
        background: url("./images/book-bg.png");
        background-position: 50%;
        background-size: cover;
        border-radius: 20px;
        .left-title {
          font-family: DIN;
          font-size: 20px;
        }
        .img-box {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 90px;
          height: 90px;
          margin: 40px 0 20px;
          background-color: #ffffff;
          border-radius: 20px;
          box-shadow: 0 10px 20px rgb(0 0 0 / 14%);
          img {
            width: 60px;
            height: 65px;
          }
        }
        .left-number {
          overflow: hidden;
          font-family: DIN;
          font-size: 62px;
        }
      }
      .item-center {
        display: flex;
        flex-wrap: wrap;
        place-content: space-between space-between;
        height: 100%;
        .traffic-box {
          box-sizing: border-box;
          display: flex;
          flex-direction: column;
          width: 47%;
          height: 48%;
          padding: 25px;
          border-radius: 30px;
          .traffic-img {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 70px;
            height: 70px;
            margin-bottom: 10px;
            background-color: #ffffff;
            border-radius: 19px;
          }
        }
        img {
          width: 33px;
          height: 33px;
        }
        .item-value {
          margin-bottom: 4px;
          font-family: DIN;
          font-size: 28px;
          font-weight: bold;
          color: #1a1a37;
        }
        .traffic-name {
          overflow: hidden;
          font-family: DIN;
          font-size: 15px;
          color: #1a1a37;
          white-space: nowrap;
        }
        .gitee-traffic {
          background: url("./images/1-bg.png");
          background-color: #e8faea;
          background-size: 100% 100%;
        }
        .gitHub-traffic {
          background: url("./images/2-bg.png");
          background-color: #e7e1fb;
          background-size: 100% 100%;
        }
        .today-traffic {
          background: url("./images/3-bg.png");
          background-color: #fdf3e9;
          background-size: 100% 100%;
        }
        .yesterday-traffic {
          background: url("./images/4-bg.png");
          background-color: #f0f5fb;
          background-size: 100% 100%;
        }
      }
      .item-right {
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        width: 100%;
        height: 430px;
        border: 1px solid var(--el-border-color);
        border-radius: 25px;
        .echarts-title {
          padding: 15px 20px;
          font-family: DIN;
          font-size: 18px;
          font-weight: bold;
          border-bottom: 1px solid var(--el-border-color);
        }
        .book-echarts {
          flex: 1;
          width: 100%;
        }
      }
    }
  }
  .bottom-box {
    position: relative;
    padding: 20px 0 0;
    .bottom-title {
      position: absolute;
      top: 75px;
      left: 50px;
      font-family: DIN;
      font-size: 18px;
      font-weight: bold;
    }
    .bottom-tabs {
      padding: 0 50px;
    }
    .curve-echarts {
      box-sizing: border-box;
      height: 400px;
      padding: 0 50px;
    }
  }
}
