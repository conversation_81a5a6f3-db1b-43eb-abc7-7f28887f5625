// 简单测试错误信息
import { withNoFullWidth, BaseSchema } from './zod-enum';
import { z } from '#/adapter/form';

// 测试基础功能
const basicTest = withNoFullWidth(z.string());
console.log('基础测试 - 全角字符:', basicTest.safeParse('你好'));

// 测试自定义消息
const customTest = withNoFullWidth(z.string(), '用户名不能包含中文字符');
console.log('自定义消息测试:', customTest.safeParse('用户名'));

// 测试 BaseSchema
console.log('BaseSchema.STRING 测试:', BaseSchema.STRING.safeParse('你好'));
console.log('BaseSchema.EMAIL 测试:', BaseSchema.EMAIL.safeParse('测试@example.com'));

// 测试正常情况
console.log('正常情况测试:', BaseSchema.STRING.safeParse('hello'));

export { basicTest, customTest };
