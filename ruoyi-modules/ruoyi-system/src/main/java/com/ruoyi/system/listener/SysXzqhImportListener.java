package com.ruoyi.system.listener;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.idev.excel.context.AnalysisContext;
import cn.idev.excel.event.AnalysisEventListener;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.SpringUtils;
import com.ruoyi.common.core.utils.ValidatorUtils;
import com.ruoyi.common.excel.core.ExcelListener;
import com.ruoyi.common.excel.core.ExcelResult;
import com.ruoyi.common.core.core.domain.Result;
import com.ruoyi.system.domain.vo.sysxzqh.*;
import com.ruoyi.system.domain.bo.sysxzqh.*;
import com.ruoyi.system.domain.po.SysXzqh;
import com.ruoyi.system.service.*;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 行政区划管理自定义导入
 *
 * <AUTHOR>
 * @date 2024-11-15 17:37:59
 */
@Slf4j
public class SysXzqhImportListener extends AnalysisEventListener<SysXzqhImportBo> implements ExcelListener<SysXzqhImportBo> {
    private final SysXzqhService sysXzqhService;

    private final Boolean isUpdateSupport;
    private int successNum = 0;
    private int failureNum = 0;
    private final StringBuilder successMsg = new StringBuilder();
    private final StringBuilder failureMsg = new StringBuilder();

    public SysXzqhImportListener(Boolean isUpdateSupport) {
        this.sysXzqhService = SpringUtils.getBean(SysXzqhService.class);
        this.isUpdateSupport = isUpdateSupport;
    }

    @Override
    public void invoke(SysXzqhImportBo sysXzqhImportBo, AnalysisContext context) {
        try {
            SysXzqh sysXzqh= sysXzqhService.getById(sysXzqhImportBo);
            if (ObjectUtil.isNull(sysXzqh)) {
                //不存在就新增
                SysXzqhAddBo sysXzqhAddBo = BeanUtil.toBean(sysXzqhImportBo, SysXzqhAddBo.class);
                ValidatorUtils.validate(sysXzqhAddBo);
                  Result<Void> result= sysXzqhService.insertWithPk(sysXzqhAddBo);//树表需要前台传来主键值
                if (result.isSuccess()) {
                    successNum++;
                    successMsg.append("<br/>").append(successNum).append("、行政区划管理 记录导入成功");
                    return;
                } else {
                    failureNum++;
                    failureMsg.append("<br/>").append(failureNum).append("、行政区划管理 记录导入失败");
                    return;
                }
            } else if (isUpdateSupport) {
                //存在就更新
                SysXzqhEditBo sysXzqhEditBo = BeanUtil.toBean(sysXzqhImportBo, SysXzqhEditBo.class);
                sysXzqhEditBo.setId(sysXzqhImportBo.getId());
                Result<Void> result= sysXzqhService.edit(sysXzqhEditBo);
                if (result.isSuccess()) {
                    successNum++;
                    successMsg.append("<br/>").append(successNum).append("、行政区划管理 记录更新成功");
                    return;
                } else {
                    failureNum++;
                    failureMsg.append("<br/>").append(failureNum).append("、行政区划管理 记录更新失败");
                    return;
                }
            }
        } catch (Exception e) {
            failureNum++;
            String msg = "<br/>" + failureNum + "、行政区划管理 记录导入失败：";
            failureMsg.append(msg).append(e.getMessage());
            log.error(msg, e);
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {

    }

    @Override
    public ExcelResult<SysXzqhImportBo> getExcelResult() {
        return new ExcelResult<>() {

            @Override
            public String getAnalysis() {
                if (failureNum > 0) {
                    failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据没有成功导入，错误如下：");
                    throw new ServiceException(failureMsg.toString());
                } else {
                    successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
                }
                return successMsg.toString();
            }

            @Override
            public List<SysXzqhImportBo> getList() {
                return null;
            }

            @Override
            public List<String> getErrorList() {
                return null;
            }
        };
    }
}
