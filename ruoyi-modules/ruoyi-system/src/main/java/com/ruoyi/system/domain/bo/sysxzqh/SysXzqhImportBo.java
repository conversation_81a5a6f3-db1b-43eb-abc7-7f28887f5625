package com.ruoyi.system.domain.bo.sysxzqh;

import cn.idev.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * 行政区划管理导入视图对象 sys_xzqh
 *
 * <AUTHOR>
 * @date 2024-11-15 17:37:59
 */

@Data
@NoArgsConstructor
public class SysXzqhImportBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private String id;

    /**
     * 父结点ID
     */
    @ExcelProperty(value = "父结点ID")
    private String parentId;

    /**
     * 显示顺序
     */
    @ExcelProperty(value = "显示顺序")
    private Integer orderNum;


}
