/**
 * 公民身份号码（身份证号码）验证工具
 * 实现完整的身份证号码验证，包括格式、地区代码、出生日期和校验码验证
 */

/**
 * 验证身份证号码的校验码
 * 使用 ISO 7064:1983.MOD 11-2 算法（国家标准 GB 11643-1999）
 * @param idCard 18位身份证号码
 * @returns 是否有效
 */
export const validateIdCardChecksum = (idCard: string): boolean => {
  if (idCard.length !== 18) return false;
  
  // 加权因子
  const weights = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
  // 校验码对应表
  const checkCodes = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'];
  
  // 计算前17位的加权和
  let sum = 0;
  for (let i = 0; i < 17; i++) {
    const digit = parseInt(idCard[i]);
    if (isNaN(digit)) return false;
    sum += digit * weights[i];
  }
  
  // 计算校验码
  const checkCodeIndex = sum % 11;
  const expectedCheckCode = checkCodes[checkCodeIndex];
  
  // 比较校验码（大小写不敏感）
  return idCard[17].toUpperCase() === expectedCheckCode;
};

/**
 * 验证身份证号码的出生日期是否有效
 * @param idCard 18位身份证号码
 * @returns 是否有效
 */
export const validateIdCardBirthDate = (idCard: string): boolean => {
  if (idCard.length !== 18) return false;
  
  const year = parseInt(idCard.substring(6, 10));
  const month = parseInt(idCard.substring(10, 12));
  const day = parseInt(idCard.substring(12, 14));
  
  // 检查年份范围（1900-当前年份）
  const currentYear = new Date().getFullYear();
  if (year < 1900 || year > currentYear) return false;
  
  // 检查月份
  if (month < 1 || month > 12) return false;
  
  // 检查日期有效性
  const date = new Date(year, month - 1, day);
  return date.getFullYear() === year && 
         date.getMonth() === month - 1 && 
         date.getDate() === day;
};

/**
 * 验证身份证号码的地区代码是否有效
 * 基于现行行政区划代码
 * @param idCard 18位身份证号码
 * @returns 是否有效
 */
export const validateIdCardAreaCode = (idCard: string): boolean => {
  if (idCard.length !== 18) return false;
  
  const areaCode = idCard.substring(0, 6);
  const firstTwo = parseInt(areaCode.substring(0, 2));
  
  // 地区代码前两位的有效范围
  const validAreaCodes = [
    // 华北地区
    11, // 北京市
    12, // 天津市
    13, // 河北省
    14, // 山西省
    15, // 内蒙古自治区
    
    // 东北地区
    21, // 辽宁省
    22, // 吉林省
    23, // 黑龙江省
    
    // 华东地区
    31, // 上海市
    32, // 江苏省
    33, // 浙江省
    34, // 安徽省
    35, // 福建省
    36, // 江西省
    37, // 山东省
    
    // 华中地区
    41, // 河南省
    42, // 湖北省
    43, // 湖南省
    
    // 华南地区
    44, // 广东省
    45, // 广西壮族自治区
    46, // 海南省
    
    // 西南地区
    50, // 重庆市
    51, // 四川省
    52, // 贵州省
    53, // 云南省
    54, // 西藏自治区
    
    // 西北地区
    61, // 陕西省
    62, // 甘肃省
    63, // 青海省
    64, // 宁夏回族自治区
    65, // 新疆维吾尔自治区
    
    // 特别行政区和其他
    71, // 台湾省
    81, // 香港特别行政区
    82, // 澳门特别行政区
    91  // 国外
  ];
  
  return validAreaCodes.includes(firstTwo);
};

/**
 * 验证身份证号码的基础格式
 * @param idCard 身份证号码
 * @returns 是否符合基础格式
 */
export const validateIdCardBasicFormat = (idCard: string): boolean => {
  if (idCard.length !== 18) return false;
  
  // 前17位必须是数字
  for (let i = 0; i < 17; i++) {
    if (!/\d/.test(idCard[i])) return false;
  }
  
  // 第18位必须是数字或X
  const lastChar = idCard[17].toUpperCase();
  return /[\dX]/.test(lastChar);
};

/**
 * 完整验证身份证号码
 * 按照验证优先级依次检查：格式 -> 地区代码 -> 出生日期 -> 校验码
 * @param idCard 18位身份证号码
 * @returns 验证结果对象
 */
export const validateIdCard = (idCard: string): {
  isValid: boolean;
  errorType?: 'length' | 'format' | 'areaCode' | 'birthDate' | 'checksum';
  errorMessage?: string;
} => {
  // 长度检查
  if (idCard.length !== 18) {
    return {
      isValid: false,
      errorType: 'length',
      errorMessage: '身份证号码必须为18位'
    };
  }
  
  // 基础格式检查
  if (!validateIdCardBasicFormat(idCard)) {
    return {
      isValid: false,
      errorType: 'format',
      errorMessage: '身份证号码格式不正确'
    };
  }
  
  // 地区代码检查
  if (!validateIdCardAreaCode(idCard)) {
    return {
      isValid: false,
      errorType: 'areaCode',
      errorMessage: '身份证号码地区代码无效'
    };
  }
  
  // 出生日期检查
  if (!validateIdCardBirthDate(idCard)) {
    return {
      isValid: false,
      errorType: 'birthDate',
      errorMessage: '身份证号码出生日期无效'
    };
  }
  
  // 校验码检查
  if (!validateIdCardChecksum(idCard)) {
    return {
      isValid: false,
      errorType: 'checksum',
      errorMessage: '身份证号码校验码错误'
    };
  }
  
  return { isValid: true };
};

/**
 * 获取身份证号码中的信息
 * @param idCard 18位身份证号码
 * @returns 身份证信息对象
 */
export const parseIdCardInfo = (idCard: string): {
  areaCode: string;
  birthYear: number;
  birthMonth: number;
  birthDay: number;
  sequenceCode: string;
  checkCode: string;
  gender: 'male' | 'female';
} | null => {
  if (!validateIdCard(idCard).isValid) return null;
  
  const areaCode = idCard.substring(0, 6);
  const birthYear = parseInt(idCard.substring(6, 10));
  const birthMonth = parseInt(idCard.substring(10, 12));
  const birthDay = parseInt(idCard.substring(12, 14));
  const sequenceCode = idCard.substring(14, 17);
  const checkCode = idCard[17];
  
  // 根据顺序码的奇偶性判断性别
  const sequenceNumber = parseInt(sequenceCode);
  const gender = sequenceNumber % 2 === 1 ? 'male' : 'female';
  
  return {
    areaCode,
    birthYear,
    birthMonth,
    birthDay,
    sequenceCode,
    checkCode,
    gender
  };
};

/**
 * 生成有效的身份证号码校验码
 * @param idCardWithoutChecksum 前17位身份证号码
 * @returns 校验码
 */
export const generateIdCardChecksum = (idCardWithoutChecksum: string): string => {
  if (idCardWithoutChecksum.length !== 17) {
    throw new Error('输入必须为17位数字');
  }
  
  const weights = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
  const checkCodes = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'];
  
  let sum = 0;
  for (let i = 0; i < 17; i++) {
    const digit = parseInt(idCardWithoutChecksum[i]);
    if (isNaN(digit)) {
      throw new Error('前17位必须为数字');
    }
    sum += digit * weights[i];
  }
  
  const checkCodeIndex = sum % 11;
  return checkCodes[checkCodeIndex];
};
