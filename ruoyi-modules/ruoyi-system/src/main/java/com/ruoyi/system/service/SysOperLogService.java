package com.ruoyi.system.service;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import com.ruoyi.common.core.core.domain.Result;
import com.ruoyi.common.core.core.domain.PageData;
import com.ruoyi.common.core.utils.ip.AddressUtils;
import com.ruoyi.common.log.event.OperLogEvent;
import com.ruoyi.common.orm.core.page.PageQuery;
import com.ruoyi.common.orm.core.page.PageResult;
import com.mybatisflex.core.query.QueryWrapper;
import com.ruoyi.common.core.utils.MapstructUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.orm.core.service.impl.BaseServiceImpl;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.system.mapper.SysOperLogMapper;
import com.ruoyi.system.domain.po.SysOperLog;
import com.ruoyi.system.domain.vo.sysoperlog.SysOperLogDetailVo;
import com.ruoyi.system.domain.vo.sysoperlog.SysOperLogListVo;
import com.ruoyi.system.domain.bo.sysoperlog.SysOperLogAddBo;
import com.ruoyi.system.domain.bo.sysoperlog.SysOperLogListBo;
import static com.ruoyi.system.domain.po.table.SysOperLogTableDef.SYS_OPER_LOG;

/**
 * 操作日志记录Service业务层处理
 * <p>
 * 本服务类提供操作日志记录地增删改查等基础操作，包括：
 * <ul>
 *     <li>分页查询操作日志记录列表</li>
 *     <li>查询操作日志记录详情</li>
 *     <li>新增操作日志记录</li>
 *     <li>修改操作日志记录</li>
 *     <li>删除操作日志记录</li>
 * </ul>
 *
 * <AUTHOR>
 * @since 2025-05-08 16:31:34
 * @version 1.0
 */
@Service
public class SysOperLogService extends BaseServiceImpl<SysOperLogMapper, SysOperLog> {

    /**
     * 获取基础查询对象
     *
     * @return 预设了表信息的查询包装器
     */
    public QueryWrapper query() {
        return super.query().from(SYS_OPER_LOG);
    }

    /**
     * 构建查询条件
     * <p>
     * 根据传入的查询参数构建查询条件，MyBatis Flex会自动处理空值
     *
     * @param bo 查询参数对象
     * @return 构建好地查询条件包装器
     */
    private QueryWrapper buildQueryWrapper(SysOperLogListBo bo) {
        return super.buildBaseQueryWrapper()
                // 系统模块
                .and(SYS_OPER_LOG.TITLE.eq(bo.getTitle()))
                // 操作类别（0其它 1后台用户 2手机端用户）
                .and(SYS_OPER_LOG.OPERATOR_TYPE.eq(bo.getOperatorType()))
                // 操作人员
                .and(SYS_OPER_LOG.OPER_NAME.like(bo.getOperName()))
                // 主机地址
                .and(SYS_OPER_LOG.OPER_IP.eq(bo.getOperIp()))
                // 操作状态（0正常 1异常）
                .and(SYS_OPER_LOG.STATUS.eq(bo.getStatus()))
                // 操作时间
                .and(SYS_OPER_LOG.OPER_TIME.eq(bo.getOperTime()))
                .orderBy(SYS_OPER_LOG.OPER_TIME.desc())
                ;
    }

    /**
     * 分页查询操作日志记录列表
     * <p>
     * 根据查询条件进行分页查询，并将结果转换为前端所需的VO对象
     *
     * @param bo 操作日志记录查询参数
     * @return 包含分页操作日志记录集合的结果对象
     */
    public Result<PageData<SysOperLogListVo>> page(SysOperLogListBo bo) {
        return PageResult.success(this.pageAs(
            PageQuery.build(),
            buildQueryWrapper(bo),
            SysOperLogListVo.class
        ));
    }

    /**
     * 查询操作日志记录列表（不分页）
     * <p>
     * 根据查询条件获取所有符合条件的操作日志记录
     *
     * @param bo 操作日志记录查询参数
     * @return 操作日志记录集合
     */
    public List<SysOperLogListVo> list(SysOperLogListBo bo) {
        QueryWrapper queryWrapper = buildQueryWrapper(bo);
        return this.listAs(queryWrapper, SysOperLogListVo.class);
    }

    /**
     * 新增操作日志记录
     * <p>
     * 将前端传入的数据转换为实体并保存到数据库
     *
     * @param bo 操作日志记录新增参数
     * @return 操作结果，成功返回{@code Result.success()}，失败返回包含错误信息的{@code Result.fail()}
     */
    public Result<Void> add(SysOperLogAddBo bo) {
        SysOperLog entity = MapstructUtils.convert(bo, SysOperLog.class);
        boolean inserted = this.save(entity);
        return inserted
            ? Result.success()
            : Result.fail("新增操作日志记录记录失败！");
    }


    /**
     * 批量删除操作日志记录
     * <p>
     * 根据ID数组删除多条操作日志记录记录
     *
     * @param ids 需要删除的操作日志记录主键数组
     * @return 操作结果，成功返回{@code Result.success()}，失败返回包含错误信息的{@code Result.fail()}
     * @throws IllegalArgumentException 当ids为null或空数组时抛出
     */
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> removeByIds(String[] ids) {
        // 参数校验
        if (ids == null || ids.length == 0) {
            return Result.fail("删除ID不能为空");
        }

        // 执行批量删除
        return this.removeByIds(Arrays.asList(ids))
            ? Result.success()
            : Result.fail("删除操作日志记录记录失败!");
    }

    /**
     * 查询操作日志记录详情
     * <p>
     * 根据ID获取单个操作日志记录的详细信息
     *
     * @param id 操作日志记录主键
     * @return 操作日志记录详细信息，如果id为空则返回null
     */
    public SysOperLogDetailVo detail(String id) {
        if (StringUtils.isEmpty(id)) {
            return null;
        }

        return this.getOneAs(
            query().where(SYS_OPER_LOG.ID.eq(id)),
            SysOperLogDetailVo.class
        );
    }

    /**
     * 操作日志记录
     *
     * @param operLogEvent 操作日志事件
     */
    @Async
    @EventListener
    public void recordLog(OperLogEvent operLogEvent) {
        SysOperLogAddBo operLog = MapstructUtils.convert(operLogEvent, SysOperLogAddBo.class);
        // 远程查询操作地点
        if (operLog != null) {
            operLog.setOperLocation(AddressUtils.getRealAddressByIP(operLog.getOperIp()));
            operLog.setOperTime(LocalDateTime.now());
        }
        add(operLog);
    }

}
