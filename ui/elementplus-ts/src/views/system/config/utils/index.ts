import type { ColumnProps, SkTableInstance } from '@/components/SkUi/SkTable/interface';
import { h, reactive, ref } from 'vue';
import type { ConfigForm, ConfigVO } from '@/api/system/config/types';
import { addConfig, delConfig, getConfig, refreshCache, updateConfig } from '@/api/system/config';
import { addDialog } from '@/components/SkUi/SkReDialog';
import ConfigDialog from '@/views/system/config/components/ConfigDialog.vue';
import { useValidator } from '@/hooks/skUi/web/useValidator';

export function configOptions() {
  const skTable = ref<SkTableInstance>();
  const { required } = useValidator();
  const rules = reactive({
    name: [required('参数名称不能为空')],
    key: [required('参数键名不能为空')],
    value: [required('参数键值不能为空')],
    type: [required('参数内置不能为空')]
  });
  // 表格配置项
  const columns = reactive<ColumnProps[]>([
    { type: 'index', label: '#', width: 80 },
    { prop: 'name', label: '参数名称', align: 'center', search: { el: 'input' } },
    { prop: 'key', label: '参数键名', align: 'center', search: { el: 'input' } },
    { prop: 'value', label: '参数键值', align: 'center' },
    {
      prop: 'type',
      label: '系统内置',
      width: 100,
      align: 'center',
      search: { el: 'sk-dict', props: { data: 'xtsf' } }
    },
    { prop: 'remark', label: '备注', align: 'center' },
    {
      prop: 'createTime',
      label: '创建时间',
      width: 180,
      search: {
        el: 'date-picker',
        span: 2,
        props: {
          type: 'daterange',
          valueFormat: 'YYYY-MM-DD',
          rangeSeparator: '-',
          startPlaceholder: '开始日期',
          endPlaceholder: '结束日期'
        }
      }
    },
    { prop: 'operation', label: '操作', width: 160, fixed: 'right' }
  ]);
  /**
   * @Description: 参数数据初始化
   * <AUTHOR>
   * @date 2024/09/27 09:15
   * @param row
   */
  const initFormProps = async (row: ConfigVO) => {
    if (row?.id) {
      const postId = row?.id;
      const { data } = await getConfig(postId);
      Object.assign(row, data);
    }
    return row;
  };
  /**
   * 编辑，新增,数据交换
   */
  const openDialog = async (title: string, row: Partial<ConfigVO> = {}) => {
    addDialog({
      title: title + '参数设置',
      width: '70%',
      props: {
        model: await initFormProps(<ConfigVO>row),
        labelWidth: '100px',
        rules: rules
      },
      contentRenderer: () => h(ConfigDialog),
      beforeSure: async (done, { options }, formRef) => {
        const curData = options.props.model as ConfigForm;
        formRef.validate(async (valid: boolean) => {
          if (valid) {
            const api = title === '新增' ? addConfig : updateConfig;
            await api(curData).then((res: any) => {
              if (res.code === 200) {
                window.skynet.alert(`${title}成功！`);
                skTable.value?.getTableList();
                done();
              }
            });
          }
        });
      }
    });
  };
  /** 导出按钮操作 */
  const handleExport = () => {
    window.skynet?.download(
      'system/config/export',
      {
        ...skTable.value.searchParam
      },
      `dict_${new Date().getTime()}.xlsx`
    );
  };
  /** 删除按钮操作 */
  const handleDelete = async (row?: ConfigVO) => {
    const ids = row?.id;
    await window.skynet.confirm('是否确认删除参数编号为"' + ids + '"的数据项？');
    await delConfig(ids);
    await skTable.value?.getTableList();
    window.skynet.alert('删除成功');
  };
  /** 刷新缓存按钮操作 */
  const handleRefreshCache = async () => {
    await refreshCache();
    window.skynet.alert('刷新缓存成功');
  };
  return {
    skTable,
    columns,
    openDialog,
    handleDelete,
    handleRefreshCache,
    handleExport
  };
}
