export const RegexEnum = {
  /**
   * 手机号码
   */
  MOBILE: /^1[3-9]\d{9}$/,
  /**
   * 全角字符检测
   * 包括：全角空格(\u3000)、全角标点符号(\uFF00-\uFFEF)、中日韩字符(\u4E00-\u9FFF)、
   * 平假名(\u3040-\u309F)、片假名(\u30A0-\u30FF)等
   */
  FULL_WIDTH: /[\u3000\uFF00-\uFFEF\u4E00-\u9FFF\u3040-\u309F\u30A0-\u30FF]/,
  /**
   * 半角字符检测（只包含ASCII字符）
   */
  HALF_WIDTH_ONLY: /^[\x00-\x7F]*$/,
} as const;

export type RegexEnumKey = keyof typeof RegexEnum;
