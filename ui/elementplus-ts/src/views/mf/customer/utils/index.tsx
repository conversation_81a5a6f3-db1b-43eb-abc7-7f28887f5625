import type { ColumnProps, SkTableInstance } from '@/components/SkUi/SkTable/interface';
import type { CustomerForm, CustomerVO } from '@/api/mf/customer/types';
import mfCustomerDialog from '@/views/mf/customer/components/MfCustomerDialog.vue';
import { addCustomer, editCustomer, getCustomer, removeCustomer } from '@/api/mf/customer';
import { addDialog } from '@/components/SkUi/SkReDialog';
import { h, reactive, ref, type Ref } from 'vue';
import { useValidator } from '@/hooks/skUi/web/useValidator';

/**
 * 客户主表管理Hook
 * @returns 客户主表管理相关的状态和方法
 */
export function useCustomer() {
  const skTable = ref<SkTableInstance>();
  const { required } = useValidator();

  const columns = reactive<ColumnProps<CustomerVO>[]>([
    { type: 'index', label: '#', width: 80 },
    {
      prop: 'id',
      align: 'center',
      label: '客户id'
    },
    {
      prop: 'customerName',
      search: {
        el: 'input'
      },
      align: 'center',
      label: '客户姓名'
    },
    {
      prop: 'phonenumber',
      search: {
        el: 'input'
      },
      align: 'center',
      label: '手机号码'
    },
    {
      prop: 'gender',
      search: {
        el: 'sk-dict',
        props: {
          data: 'xb'
        }
      },
      align: 'center',
      label: '客户性别'
    },
    {
      prop: 'birthday',
      search: {
        el: 'date-picker',
        span: 1,
        props: {
          type: 'date',
          valueFormat: 'YYYY-MM-DD',
          placeholder: '请选择客户生日'
        }
      },
      align: 'center',
      label: '客户生日'
    },
    {
      prop: 'remark',
      align: 'center',
      label: '客户描述'
    },
    { prop: 'operation', slot: 'operation', label: '操作', width: 300, fixed: 'right' }
  ]);

  const rules = reactive<Record<string, any>>({
    mfGoodsList: {
      customerId: [required('客户id')],
      name: [required('商品名称')],
      weight: [required('商品重量')],
      price: [required('商品价格')],
      date: [required('商品时间')],
      type: [required('商品种类')],
    },
    customerName: [required('客户姓名')],
    phonenumber: [required('手机号码')],
    gender: [required('客户性别')],
    birthday: [required('客户生日')],
    remark: [required('客户描述')],
  });

  /**
   * 初始化表单数据
   * @param {Partial<CustomerVO>} customerVo - 客户主表数据
   * @returns {Promise<Ref<CustomerForm>>} 表单响应式对象
   */
  const initFormProps = async (customerVo: Partial<CustomerVO>): Promise<Ref<CustomerForm>> => {
    const formInline = ref<CustomerForm>({
      mfGoodsList: [],
      id: '',
      customerName: '',
      phonenumber: '',
      gender: '',
      birthday: undefined,
      remark: '',
    });

    if (customerVo.id) {
      const res = await getCustomer(customerVo.id);
      formInline.value = structuredClone(res.data);
    }

    return formInline;
  };

  /**
   * 打开编辑/新增对话框
   * @param {string} title - 对话框标题
   * @param {Partial<CustomerVO>} [row={}] - 行数据
   * @returns {Promise<void>}
   */
  const openDialog = async (title: string, row: Partial<CustomerVO> = {}): Promise<void> => {
    addDialog({
      title: `${title}客户主表`,
      width: '1024px',
      props: {
        model: await initFormProps(row),
        labelWidth: '140px',
        rules
      },
      contentRenderer: () => h(mfCustomerDialog),
      beforeSure: async (done, { options }, formRef) => {
        const formData = options.props.model as CustomerForm;

        formRef.validate(async (valid: boolean) => {
          if (!valid) return;

          const api = title === '新增' ? addCustomer : editCustomer;
          const res = await api(formData);

          if (res.code === 200) {
            window.skynet.alert(`${title}成功！`);
            done();
            await skTable.value?.getTableList();
          }
        });
      }
    });
  };

  /**
   * 导出数据
   * @returns {void}
   */
  const handleExport = (): void => {
    window.skynet
      .download(
        'mf/customer/export',
        {
          ...skTable.value.searchParam
        },
        `customer_${new Date().getTime()}.xlsx`
      )
      .then(() => {});
  };

  /**
   * 删除客户主表
   * @param {CustomerVO} [row] - 要删除的数据
   * @returns {Promise<void>}
   * @throws {Error} 删除失败时抛出错误
   */
  const handleDelete = async (row?: CustomerVO): Promise<void> => {
    if (!row?.id) return;

    const _ids = row?.id;
    await window.skynet.confirm('是否确认删除客户主表编号为"' + _ids + '"的数据项？');

    try {
      await removeCustomer(row.id);
      window.skynet.alert('删除成功');
      await skTable.value?.getTableList();
    } catch (error) {
      console.error('删除失败:', error);
    }
  };

  return {
    skTable,
    columns,
    rules,
    openDialog,
    handleExport,
    handleDelete
  };
}
