import type { FormSchemaGetter, VbenFormSchema } from '#/adapter/form';

import { getPopupContainer } from '@vben/utils';

import { z } from '#/adapter/form';

const baseFormSchema: FormSchemaGetter = () => [
  {
    component: 'SkDivider',
    componentProps: {
      content: '基本信息',
    },
    fieldName: 'divider1',
    formItemClass: 'col-span-2',
    hideLabel: true,
  },
  {
    component: 'Input',
    fieldName: 'tableName',
    label: '表名称',
    rules: 'required',
  },
  {
    component: 'Input',
    fieldName: 'tableComment',
    label: '表描述',
    rules: 'required',
  },
  {
    component: 'Input',
    fieldName: 'className',
    label: '实体类名称',
    rules: 'required',
  },
  {
    component: 'Input',
    fieldName: 'functionAuthor',
    label: '作者',
    rules: 'required',
  },
  {
    component: 'Input',
    fieldName: 'remark',
    formItemClass: 'col-span-2 items-baseline',
    label: '备注',
  },
  {
    component: 'SkDivider',
    componentProps: {
      content: '生成信息',
    },
    fieldName: 'divider2',
    formItemClass: 'col-span-2',
    hideLabel: true,
  },
  {
    component: 'Select',
    componentProps: {
      allowClear: true,
      getPopupContainer,
      options: [
        { label: '单表(增删改查)', value: 'crud' },
        { label: '主子表（增删改查）', value: 'sub' },
        { label: '树表(增删改查)', value: 'tree' },
      ],
    },
    defaultValue: 'crud',
    fieldName: 'tplCategory',
    label: '生成模板',
    rules: 'selectRequired',
  },
  {
    component: 'Input',
    fieldName: 'packageName',
    help: '生成在哪个java包下, 例如 com.ruoyi.system',
    label: '生成包路径',
    rules: 'required',
  },
  {
    component: 'Input',
    fieldName: 'moduleName',
    help: '可理解为子系统名，例如 system',
    label: '生成模块名',
    rules: 'required',
  },
  {
    component: 'Input',
    fieldName: 'businessName',
    help: '可理解为功能英文名，例如 user',
    label: '生成业务名',
    rules: 'required',
  },
  {
    component: 'Input',
    fieldName: 'functionName',
    help: '用作类描述，例如 用户',
    label: '生成功能名',
    rules: 'required',
  },
  {
    component: 'TreeSelect',
    componentProps: {
      allowClear: true,
      getPopupContainer,
    },
    defaultValue: 0,
    fieldName: 'parentMenuId',
    label: '上级菜单',
  },
  {
    component: 'InputNumber',
    fieldName: 'editColumns',
    label: '编辑页列数',
    rules: 'required',
  },
  {
    component: 'Switch',
    fieldName: 'isImport',
    help: '开启后将生成Excel导入功能，包括导入控制器、服务方法和导入模板',
    label: ' Excel导入',
    rules: 'required',
    componentProps: {
      activeValue: '1',
      inactiveValue: '0',
    },
  },
  {
    component: 'Switch',
    fieldName: 'isExport',
    help: '开启后将生成Excel导出功能，包括导出控制器和服务方法',
    label: ' Excel导出',
    rules: 'required',
    componentProps: {
      activeValue: '1',
      inactiveValue: '0',
    },
  },
  {
    component: 'Switch',
    fieldName: 'isOption',
    help: '开启后将生成下拉选择框的后端接口，包括控制器listOption方法和服务层optionSelect方法，树表类型将强制生成',
    label: ' 下拉选项',
    rules: 'required',
    componentProps: {
      activeValue: '1',
      inactiveValue: '0',
    },
  },
  {
    component: 'RadioGroup',
    componentProps: {
      buttonStyle: 'solid',
      options: [
        { label: 'modal弹窗', value: '0' },
        { label: 'drawer抽屉', value: '1' },
      ],
      optionType: 'button',
    },
    help: '自定义功能, 需要后端支持',
    defaultValue: '0',
    fieldName: 'dialogType',
    label: '弹窗组件类型',
  },
  {
    component: 'RadioGroup',
    componentProps: {
      buttonStyle: 'solid',
      options: [
        { label: 'Element Plus', value: '0' },
        { label: 'Vben Admin', value: '1' },
      ],
      optionType: 'button',
    },
    help: '选择使用的组件库类型，Element Plus 或 Vben Admin',
    defaultValue: '0',
    fieldName: 'componentType',
    label: '组件类型',
  },
  {
    component: 'RadioGroup',
    componentProps: {
      buttonStyle: 'solid',
      options: [
        { label: 'zip压缩包', value: '0' },
        { label: '自定义路径', value: '1' },
      ],
      optionType: 'button',
    },
    defaultValue: '0',
    fieldName: 'genType',
    help: '默认为zip压缩包下载, 也可以自定义生成路径',
    label: '生成代码方式',
  },
  {
    component: 'Input',
    defaultValue: '/',
    dependencies: {
      if: (model) => model.genType === '1',
      triggerFields: ['genType'],
    },
    fieldName: 'genPath',
    help: '输入绝对路径, 不支持"./"相对路径',
    label: '代码生成路径',
    rules: z
      .string()
      .regex(/^(?:[a-z]:)?(?:\/|(?:\\|\/)[^\\/:*?"<>|\r\n]+)*(?:\\|\/)?$/i, {
        message: '请输入合法的路径',
      }),
  },
];

const treeFormSchema: FormSchemaGetter = () => [
  {
    component: 'SkDivider',
    componentProps: {
      content: '树结点配置信息',
    },
    dependencies: {
      if: (values) => values.tplCategory === 'tree',
      triggerFields: ['tplCategory'],
    },
    fieldName: 'divider3',
    formItemClass: 'col-span-2',
    hideLabel: true,
  },
  {
    component: 'Select',
    componentProps: {
      getPopupContainer,
    },
    dependencies: {
      if: (values) => values.tplCategory === 'tree',
      triggerFields: ['tplCategory'],
    },
    fieldName: 'treeCode',
    helpMessage: '树节点显示的编码字段名， 如: dept_id (相当于id)',
    label: '树编码字段',
    rules: 'selectRequired',
  },
  {
    component: 'Select',
    componentProps: {
      allowClear: true,
    },
    dependencies: {
      if: (values) => values.tplCategory === 'tree',
      triggerFields: ['tplCategory'],
    },
    fieldName: 'treeParentCode',
    help: '树节点显示的父编码字段名， 如: parent_Id (相当于parentId)',
    label: '树父编码字段',
    rules: 'selectRequired',
  },
  {
    component: 'Select',
    componentProps: {
      allowClear: true,
    },
    dependencies: {
      if: (values) => values.tplCategory === 'tree',
      triggerFields: ['tplCategory'],
    },
    fieldName: 'treeName',
    help: '树节点的显示名称字段名， 如: dept_name (相当于label)',
    label: '树名称字段',
    rules: 'selectRequired',
  },
];

export const subFormSchema: () => VbenFormSchema[] = () => [
  {
    component: 'SkDivider',
    componentProps: {
      content: '子表信息',
    },
    dependencies: {
      if: (values) => values.tplCategory === 'sub',
      triggerFields: ['tplCategory'],
    },
    fieldName: 'divider4',
    formItemClass: 'col-span-2',
    hideLabel: true,
  },
  {
    component: 'Select',
    componentProps: {
      allowClear: true,
      options: [],
    },
    dependencies: {
      if: (values) => values.tplCategory === 'sub',
      triggerFields: ['tplCategory'],
    },
    fieldName: 'subTableName',
    help: '关联子表的表名， 如：sys_user',
    label: '关联子表的表名',
    rules: 'selectRequired',
  },
  {
    component: 'Select',
    componentProps: {
      allowClear: true,
      options: [],
    },
    dependencies: {
      if: (values) => values.tplCategory === 'sub',
      triggerFields: ['tplCategory'],
    },
    fieldName: 'subTableFkName',
    help: '子表关联的外键名， 如：user_id',
    label: '子表关联的外键名',
    rules: 'selectRequired',
  },
];

export const formSchema: () => VbenFormSchema[] = () => [
  ...baseFormSchema(),
  ...treeFormSchema(),
  ...subFormSchema(),
];
