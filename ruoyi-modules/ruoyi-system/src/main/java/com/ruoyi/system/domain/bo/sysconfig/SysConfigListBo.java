package com.ruoyi.system.domain.bo.sysconfig;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.core.domain.bo.BaseListBo;
import com.ruoyi.system.domain.po.SysConfig;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 参数配置业务对象 sys_config
 *
 * <AUTHOR>
 * @since 2025-05-09 21:27:42
 */
@Data
@AutoMapper(target = SysConfig.class, reverseConvertGenerate = false)
public class SysConfigListBo extends BaseListBo {

    /**
     * 参数名称
     */
    @NotBlank(message = "参数名称不能为空")
    private String name;

    /**
     * 参数键名
     */
    @NotBlank(message = "参数键名不能为空")
    private String key;

    /**
     * 系统内置（Y是 N否）
     */
    @NotBlank(message = "系统内置（Y是 N否）不能为空")
    private String type;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" )
    private LocalDateTime createTime;


}
