<script lang="ts" setup>
import { Page } from '@vben/common-ui';

import { useLogininforService } from './service';

defineOptions({
  name: 'LogininforIndex',
});

const { BasicTable, FormDialog, useToolbarButtons, useRowButtons } =
  useLogininforService();
</script>

<template>
  <Page auto-content-height>
    <FormDialog
      :footer="false"
      :fullscreen-button="false"
      class="w-[550px]"
      title="登录日志"
    />

    <BasicTable>
      <template #toolbar-actions>
        <ActionButtonGroup :buttons="useToolbarButtons()" />
      </template>

      <template #action="{ row }">
        <ActionButtonGroup :buttons="useRowButtons()" :row="row" />
      </template>
    </BasicTable>
  </Page>
</template>
