<!--
不再支持url 统一使用ossId
去除使用`file-type`库进行文件类型检测 在Safari无法使用
-->
<script setup lang="ts">
import type { UploadFile, UploadProps } from 'element-plus';

import type { BaseUploadProps, UploadEmits } from './props';

import { Plus, Upload } from '@vben/icons';

import { isFunction } from 'lodash-es';

import { uploadApi } from '#/api';

import { defaultImageAcceptExts } from './helper';
import { useImagePreview, useUpload } from './hook';

interface ImageUploadProps extends BaseUploadProps {
  /**
   * 同element-plus的listType
   * @default picture-card
   */
  listType?: UploadProps['listType'];
  /**
   * 使用list-type: picture-card时 是否显示动画
   * 会有一个`弹跳`的效果 默认关闭
   * @default false
   */
  withAnimation?: boolean;
}

const props = withDefaults(defineProps<ImageUploadProps>(), {
  api: () => uploadApi,
  removeOnError: true,
  showSuccessMsg: true,
  removeConfirm: false,
  accept: defaultImageAcceptExts.join(','),
  data: () => undefined,
  limit: 1,
  maxSize: 5,
  disabled: false,
  listType: 'picture-card',
  helpMessage: true,
  drag: false,
  abortOnUnmounted: true,
  withAnimation: false,
});

const emit = defineEmits<UploadEmits>();

// 双向绑定 ossId
const ossIdList = defineModel<string | string[]>('value', {
  default: () => [],
});

const {
  acceptStr,
  handleChange,
  handleRemove,
  beforeUpload,
  innerFileList,
  customRequest,
} = useUpload(props, emit, ossIdList, 'image');

const { previewVisible, previewImage, handleCancel, handlePreview } =
  useImagePreview();

function currentPreview(file: UploadFile) {
  // 有自定义预览逻辑走自定义
  if (props.preview && isFunction(props.preview)) {
    return props.preview(file);
  }
  // 否则走默认预览
  return handlePreview(file);
}
</script>

<template>
  <div>
    <ElUpload
      v-model:file-list="innerFileList"
      :class="{ 'upload-animation__disabled': !withAnimation }"
      :list-type="listType"
      :accept="accept"
      :disabled="disabled"
      :limit="limit"
      :multiple="multiple"
      :before-upload="beforeUpload"
      :http-request="customRequest"
      @preview="currentPreview"
      @change="handleChange"
      @remove="handleRemove"
    >
      <div v-if="innerFileList?.length < limit && listType === 'picture-card'">
        <Plus />
        <div class="mt-[8px]">上传</div>
      </div>
      <ElButton
        v-if="innerFileList?.length < limit && listType !== 'picture-card'"
        :disabled="disabled"
      >
        <ElIcon><Upload /></ElIcon>
        上传
      </ElButton>
    </ElUpload>
    <slot name="helpMessage" v-bind="{ limit, disabled, maxSize, accept }">
      <div
        v-if="helpMessage"
        class="mt-2"
        :class="{ 'upload-text__disabled': disabled }"
      >
        请上传不超过
        <span
          class="text-primary mx-1 font-medium"
          :class="{ 'upload-text__disabled': disabled }"
        >
          {{ maxSize }}MB 的
        </span>
        <span
          class="text-primary mx-1 font-medium"
          :class="{ 'upload-text__disabled': disabled }"
        >
          {{ acceptStr }}
        </span>
        格式文件
      </div>
    </slot>

    <ElImageViewer
      v-if="previewVisible"
      :url-list="[previewImage]"
      @close="handleCancel"
    />
  </div>
</template>

<style lang="scss">
.el-upload-list--picture-card {
  .el-upload-list__item {
    border-radius: 4px;
  }
}

// 禁用的样式和element-plus保持一致
.upload-text__disabled {
  color: rgb(50 54 57 / 25%);
  cursor: not-allowed;

  &:where(.dark, .dark *) {
    color: rgb(242 242 242 / 25%);
  }
}

// list-type: picture-card动画效果关闭样式
.upload-animation__disabled {
  .el-upload-list__item {
    animation-duration: 0s !important;
  }
}
</style>
