package com.ruoyi.system.controller.monitor;

import lombok.RequiredArgsConstructor;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.ruoyi.common.core.core.domain.Result;
import com.ruoyi.common.core.core.domain.PageData;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.web.core.BaseController;
import jakarta.annotation.Resource;
import com.ruoyi.system.domain.vo.syslogininfor.SysLogininforDetailVo;
import com.ruoyi.system.domain.vo.syslogininfor.SysLogininforListVo;
import com.ruoyi.system.domain.bo.syslogininfor.SysLogininforListBo;
import com.ruoyi.system.service.SysLogininforService;

/**
 * 系统访问记录控制器
 * <p>
 * 提供系统访问记录地增删改查、导入导出等功能，主要包含：
 * <ul>
 *   <li>系统访问记录地增删改查</li>
 *   <li>系统访问记录的分页查询</li>
 *   <li>系统访问记录的下拉选择</li>
 * </ul>
 *
 * <AUTHOR>
 * @since 2025-05-08 10:26:02
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/monitor/logininfor")
public class SysLogininforController extends BaseController {
    @Resource
    private SysLogininforService sysLogininforService;

    /**
     * 分页查询系统访问记录列表
     * <p>
     * 支持多条件组合查询，返回分页数据
     *
     * @param sysLogininforListBo 查询条件对象，包含分页参数和查询条件
     * @return 包含分页信息和系统访问记录列表的结果对象
     */
    @SaCheckPermission("system:logininfor:list")
    @GetMapping("/list")
    public Result<PageData<SysLogininforListVo>> page(SysLogininforListBo sysLogininforListBo) {
        return sysLogininforService.page(sysLogininforListBo);
    }

    /**
     * 删除系统访问记录
     * <p>
     * 支持批量删除操作，可同时删除多个系统访问记录
     *
     * @param ids 系统访问记录ID数组，包含要删除的ID列表
     * @return 操作结果，成功或失败信息
     */
    @SaCheckPermission("system:logininfor:remove")
    @Log(title = "系统访问记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result<Void> remove(@PathVariable String[] ids) {
        return sysLogininforService.removeByIds(ids);
    }

    /**
     * 获取系统访问记录详细信息
     * <p>
     * 根据ID查询单个系统访问记录的完整信息，包含所有字段
     *
     * @param id 系统访问记录ID
     * @return 包含系统访问记录所有字段的详细信息对象
     */
    @SaCheckPermission("system:logininfor:query")
    @GetMapping(value = "/{id}")
    public Result<SysLogininforDetailVo> detail(@PathVariable String id) {
        return Result.success(sysLogininforService.detail(id));
    }

    /**
     * 账户解锁
     *
     * @param userName 用户名
     */
    @SaCheckPermission("monitor:logininfor:unlock")
    @Log(title = "账户解锁", businessType = BusinessType.OTHER)
    @GetMapping("/unlock/{userName}")
    public Result<Void> unlock(@PathVariable("userName") String userName) {
        return sysLogininforService.unlock(userName);
    }
}
