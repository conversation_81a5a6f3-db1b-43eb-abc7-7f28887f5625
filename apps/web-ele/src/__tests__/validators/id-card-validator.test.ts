// 身份证验证器测试
import {
  generateIdCardChecksum,
  parseIdCardInfo,
  validateIdCard,
} from '#/utils/validators';

describe('身份证验证器测试', () => {
  describe('validateIdCard', () => {
    it('有效身份证号码', () => {
      const validIdCards = [
        '11010519491231002X',
        '110105194912310029',
        '320311198001010011',
        '******************',
        '510104200001010012',
      ];

      validIdCards.forEach((idCard) => {
        const result = validateIdCard(idCard);
        expect(result.isValid).toBe(true);
      });
    });

    it('无效身份证号码 - 长度错误', () => {
      const invalidIdCards = [
        '1101051949123100', // 16位
        '110105194912310', // 15位
        '11010519491231002XX', // 19位
        '', // 空字符串
      ];

      invalidIdCards.forEach((idCard) => {
        const result = validateIdCard(idCard);
        expect(result.isValid).toBe(false);
        expect(result.errorType).toBe('length');
      });
    });

    it('无效身份证号码 - 格式错误', () => {
      const invalidIdCards = [
        '11010519491231002A', // 最后一位不是数字或X
        '1101051949123100ZX', // 倒数第二位不是数字
        'ABCDEFGHIJKLMNOPQR', // 全字母
      ];

      invalidIdCards.forEach((idCard) => {
        const result = validateIdCard(idCard);
        expect(result.isValid).toBe(false);
        expect(result.errorType).toBe('format');
      });
    });

    it('无效身份证号码 - 地区代码错误', () => {
      const invalidIdCards = [
        '00010519491231002X', // 地区代码00无效
        '99010519491231002X', // 地区代码99无效
      ];

      invalidIdCards.forEach((idCard) => {
        const result = validateIdCard(idCard);
        expect(result.isValid).toBe(false);
        expect(result.errorType).toBe('areaCode');
      });
    });

    it('无效身份证号码 - 出生日期错误', () => {
      const invalidIdCards = [
        '11010518991231002X', // 年份1899无效
        '11010520251231002X', // 年份2025无效
        '11010519491301002X', // 月份13无效
        '11010519491232002X', // 日期32无效
      ];

      invalidIdCards.forEach((idCard) => {
        const result = validateIdCard(idCard);
        expect(result.isValid).toBe(false);
        expect(result.errorType).toBe('birthDate');
      });
    });

    it('无效身份证号码 - 校验码错误', () => {
      const invalidIdCards = [
        '11010519491231002Y', // 正确应该是X
        '110105194912310020', // 正确应该是9
      ];

      invalidIdCards.forEach((idCard) => {
        const result = validateIdCard(idCard);
        expect(result.isValid).toBe(false);
        expect(result.errorType).toBe('checksum');
      });
    });
  });

  describe('parseIdCardInfo', () => {
    it('解析有效身份证信息', () => {
      const idCard = '11010519491231002X';
      const info = parseIdCardInfo(idCard);

      expect(info).not.toBeNull();
      expect(info?.areaCode).toBe('110105');
      expect(info?.birthYear).toBe(1949);
      expect(info?.birthMonth).toBe(12);
      expect(info?.birthDay).toBe(31);
      expect(info?.sequenceCode).toBe('002');
      expect(info?.checkCode).toBe('X');
      expect(info?.gender).toBe('female'); // 002是偶数，女性
    });

    it('解析无效身份证返回null', () => {
      const invalidIdCard = '11010519491231002Y';
      const info = parseIdCardInfo(invalidIdCard);

      expect(info).toBeNull();
    });
  });

  describe('generateIdCardChecksum', () => {
    it('生成正确的校验码', () => {
      const testCases = [
        { prefix: '11010519491231002', expected: 'X' },
        { prefix: '11010519491231002', expected: 'X' },
      ];

      testCases.forEach(({ prefix, expected }) => {
        const checksum = generateIdCardChecksum(prefix);
        expect(checksum).toBe(expected);
      });
    });

    it('无效输入抛出错误', () => {
      expect(() => generateIdCardChecksum('')).toThrow();
      expect(() => generateIdCardChecksum('1234567890123456')).toThrow();
      expect(() => generateIdCardChecksum('1234567890123456A')).toThrow();
    });
  });

  describe('边界情况测试', () => {
    it('闰年2月29日', () => {
      const leapYearIdCard = '11010520000229001X';
      const result = validateIdCard(leapYearIdCard);
      expect(result.isValid).toBe(true);
    });

    it('小写x校验码', () => {
      const lowerCaseX = '11010519491231002x';
      const result = validateIdCard(lowerCaseX);
      expect(result.isValid).toBe(true);
    });
  });
});

export {};
