package com.ruoyi.system.domain.vo.sysrole;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 角色信息视图对象 sys_role
 *
 * <AUTHOR>
 * @since 2025-04-28 12:56:34
 */
@Data
public class SysRoleDataScopeVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 角色ID */
    private String id;

    /**
     * 数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限）
     */
    private String dataScope;
}
