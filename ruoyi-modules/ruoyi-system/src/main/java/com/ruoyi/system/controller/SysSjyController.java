package com.ruoyi.system.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.annotation.SaCheckPermission;
import com.ruoyi.common.core.core.domain.PageData;
import com.ruoyi.common.core.core.domain.Result;
import com.ruoyi.common.excel.core.ExcelResult;
import com.ruoyi.common.excel.utils.ExcelUtil;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.web.annotation.RepeatSubmit;
import com.ruoyi.common.web.core.BaseController;
import com.ruoyi.system.domain.bo.syssjy.*;
import com.ruoyi.system.domain.vo.syssjy.SysSjyDetailVo;
import com.ruoyi.system.domain.vo.syssjy.SysSjyExcelVo;
import com.ruoyi.system.domain.vo.syssjy.SysSjyListVo;
import com.ruoyi.system.domain.vo.syssjy.SysSjyOptionVo;
import com.ruoyi.system.listener.SysSjyImportListener;
import com.ruoyi.system.service.SysSjyService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;


/**
 * 数据元管理Controller
 *
 * <AUTHOR>
 * @date 2025-01-03 17:31:57
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/sjy")
public class SysSjyController extends BaseController {
    @Resource
    private SysSjyService sysSjyService;

    /**
     * 查询数据元管理List列表
     */
    @SaCheckPermission("system:sjy:list")
    @GetMapping("/list")
    public Result<PageData<SysSjyListVo>> page(SysSjyListBo sysSjyListBo) {
        return sysSjyService.page(sysSjyListBo);
    }

    /**
     * 查询数据元管理选择列表
     *
     * @param optionBo 数据元管理选择查询条件
     * @return 返回数据元管理信息列表
     */
    @SaCheckLogin
    @GetMapping("/listOption")
    public Result<List<SysSjyOptionVo>> listOption(SysSjyOptionBo optionBo) {
        return sysSjyService.optionSelect(optionBo);
    }


    /**
     * 新增数据元管理
     */
    @SaCheckPermission("system:sjy:add")
    @Log(title = "数据元管理", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping
    public Result<Void> add(@Validated @RequestBody SysSjyAddBo sysSjyAddBo) {
        return sysSjyService.add(sysSjyAddBo);
    }

    /**
     * 修改数据元管理
     */
    @SaCheckPermission("system:sjy:edit")
    @Log(title = "数据元管理", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping
    public Result<Void> edit(@Validated @RequestBody SysSjyEditBo sysSjyEditBo) {
        return sysSjyService.edit(sysSjyEditBo);
    }

    /**
     * 删除数据元管理
     */
    @SaCheckPermission("system:sjy:remove")
    @Log(title = "数据元管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result<Void> remove(@PathVariable String[] ids) {
        return sysSjyService.removeByIds(ids);
    }

    /**
     * 获取数据元管理详细信息
     */
    @SaCheckPermission("system:sjy:query")
    @GetMapping(value = "/{id}")
    public Result<SysSjyDetailVo> detail(@PathVariable String id) {
        return Result.success(sysSjyService.detail(id));
    }

    /**
     * 导出数据元管理列表
     */
    @SaCheckPermission("system:sjy:export")
    @Log(title = "数据元管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysSjyListBo sysSjyListBo) {
        List<SysSjyExcelVo> list = sysSjyService.export(sysSjyListBo);
        ExcelUtil.exportExcel(list, "数据元管理", SysSjyExcelVo.class, response);
    }

    /**
     * 导入数据
     *
     * @param file          导入文件
     * @param updateSupport 是否更新已存在数据
     */
    @Log(title = "数据元管理", businessType = BusinessType.IMPORT)
    @SaCheckPermission("system:sjy:import")
    @PostMapping("/importData")
    public Result<Void> importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelResult<SysSjyImportBo> result = ExcelUtil.importExcel(file.getInputStream(), SysSjyImportBo.class, new SysSjyImportListener(updateSupport));
        return Result.success(result.getAnalysis());
    }

    /**
     * 按模板导入数据
     *
     * @param response 返回response
     */
    @SaCheckPermission("system:sjy:import")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil.exportExcel(new ArrayList<>(), "数据元管理", SysSjyImportBo.class, response);
    }
}
