import type { OssQuery, OssVO } from './types';
import http from '@/utils/http';
import type { PageData } from '@/utils/http/interface';

// 查询OSS对象存储列表
export const listOss = (query: OssQuery) => {
  return http.get<PageData<OssVO>>('/resource/oss/list', query);
};

// 查询OSS对象基于id串
export const listByIds = (id: string | number | Array<string | number>) => {
  return http.get<OssVO[]>('/resource/oss/listByIds/' + id);
};

// 删除OSS对象存储
export const delOss = (id: string | number | Array<string | number>) => {
  return http.delete('/resource/oss/' + id);
};
