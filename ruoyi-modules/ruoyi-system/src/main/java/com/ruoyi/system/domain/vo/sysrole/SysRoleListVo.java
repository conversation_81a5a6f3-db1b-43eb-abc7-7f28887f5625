package com.ruoyi.system.domain.vo.sysrole;

import lombok.Data;
import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 角色信息视图对象 sys_role
 *
 * <AUTHOR>
 * @since 2025-04-28 12:56:34
 */
@Data
public class SysRoleListVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 角色ID */
    private String id;
    /** 角色名称 */
    private String roleName;
    /** 角色权限字符串 */
    private String roleKey;
    /** 显示顺序 */
    private Integer roleSort;
    /** 角色状态（0正常 1停用） */
    private String status;
    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" )
    private LocalDateTime createTime;
    /**
     * 用户是否存在此角色标识 默认不存在
     */
    private boolean flag = false;

    /**
     * 菜单树选择项是否关联显示
     */
    private Boolean menuCheckStrictly;
}
