// @see: https://www.prettier.cn

module.exports = {
  /**
   * 换行宽度，当代码宽度达到多少时换行
   * @default 130
   * @type {number}
   */
  printWidth: 130,
  /**
   * 缩进的空格数量
   * @default 2
   * @type {number}
   */
  tabWidth: 2,
  /**
   * 使用制表符而不是空格缩进行 (true：制表符，false：空格)
   * @default false
   * @type {boolean}
   */
  useTabs: false,
  /**
   * 是否在代码块结尾加上分号
   * @default true
   * @type {boolean}
   */
  semi: true,
  /**
   * 使用单引号 (true：单引号，false：双引号)
   * @default false
   * @type {boolean}
   */
  singleQuote: true,
  /**
   * 在对象字面量中决定是否将属性名用引号括起来 可选值 "<as-needed|consistent|preserve>"
   * @default "as-needed"
   * @type {"as-needed"|"consistent"|"preserve"}
   */
  quoteProps: 'as-needed',
  /**
   * 在JSX中使用单引号而不是双引号 (true：单引号，false：双引号)
   * @default false
   * @type {boolean}
   */
  jsxSingleQuote: false,
  /**
   * 末尾是否加上逗号,多行时尽可能打印尾随逗号 可选值"<none|es5|all>"
   * @default "es5"
   * @type {"es5"|"none"|"all"}
   */
  trailingComma: 'none',
  /**
   * 在对象，数组括号与文字之间加空格 "{ foo: bar }"
   * @default true
   * @type {boolean}
   */
  bracketSpacing: true,
  /**
   * 把多行HTML (HTML, JSX, Vue, Angular)元素的>放在最后一行的末尾，而不是单独放在下一行(不适用于自关闭元素,true：放末尾，false：单独一行)。
   * @default false
   * @type {boolean}
   */
  bracketSameLine: false,
  /**
   * (x) => {} 箭头函数参数只有一个时是否要有小括号 (avoid：省略括号，always：不省略括号)
   * @default "always"
   * @type {"always"|"avoid"}
   */
  arrowParens: 'always',
  // 指定要使用的解析器，不需要写文件开头的 @prettier
  requirePragma: false,
  // 可以在文件顶部插入一个特殊标记，指定该文件已使用 Prettier 格式化
  insertPragma: false,
  // 用于控制文本是否应该被换行以及如何进行换行
  proseWrap: 'preserve',
  /**
   * 在html中空格是否是敏感的 "css" - 遵守 CSS 显示属性的默认值， "strict" - 空格被认为是敏感的 ，"ignore" - 空格被认为是不敏感的
   * @default "css"
   * @type {"css"|"strict"|"ignore"}
   */
  htmlWhitespaceSensitivity: 'css',
  /**
   * 是否缩进Vue文件中的<script>和<style>标记内的代码。有些人(比如Vue的创建者)不使用缩进来保存缩进级别，但这可能会破坏编辑器中的代码折叠。
   * @default "always"
   * @type {"always"|"avoid"}
   */
  vueIndentScriptAndStyle: false,
  /**
   * 文件结束符
   * @default "lf"
   * @type {"lf"|"crlf"|"cr"|"auto"}
   */
  endOfLine: 'auto',
  // 这两个选项可用于格式化以给定字符偏移量（分别包括和不包括）开始和结束的代码 (rangeStart：开始，rangeEnd：结束)
  rangeStart: 0,
  rangeEnd: Infinity,
  /**
   * HTML\VUE\JSX每行只有单个属性
   * @default true
   * @type {boolean}
   */
  singleAttributePerLine: false,
  disableLanguages: ['html']
};
