package com.ruoyi.safetyoutline.domain.bo.syslevel3protectconfig;

import cn.idev.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * 三级等保配置信息导入视图对象 sys_level3protect_config
 *
 * <AUTHOR>
 * @date 2025-01-13 15:56:35
 */
@Data
@NoArgsConstructor
public class SysLevel3protectConfigImportBo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private String id;
    /**
     * 配置双因子登录模式(0关闭1开启)
     */
    @ExcelProperty(value = "配置双因子登录模式(0关闭1开启)")
    private String pzsyzdlmsbz;
    /**
     * 最大连续登录失败次数
     */
    @ExcelProperty(value = "最大连续登录失败次数")
    private Integer zdlxdlsbcs;
    /**
     * 连续登录失败锁定分钟
     */
    @ExcelProperty(value = "连续登录失败锁定分钟")
    private Integer lxdlsbsdsj;
    /**
     * 登录后无操作自动退出的分钟
     */
    @ExcelProperty(value = "登录后无操作自动退出的分钟")
    private Integer dlwczzdtcsj;
    /**
     * 开启密码复杂度(0关闭1开启)
     */
    @ExcelProperty(value = "开启密码复杂度(0关闭1开启)")
    private String kqmmfzdbz;
    /**
     * 定期修改密码时间间隔
     */
    @ExcelProperty(value = "定期修改密码时间间隔")
    private Integer dqxgmmsjjg;
    /**
     * 定期修改密码不允许重复次数
     */
    @ExcelProperty(value = "定期修改密码不允许重复次数")
    private Integer dqxgmmbxxcfcs;
    /**
     * 文件安全检测(0关闭1开启)
     */
    @ExcelProperty(value = "文件安全检测(0关闭1开启)")
    private String wjaqjcbz;
    /**
     * 上传文件大小限制
     */
    @ExcelProperty(value = "上传文件大小限制")
    private Integer scwjdxxz;
    /**
     * 乐观锁
     */
    @ExcelProperty(value = "乐观锁")
    private Integer version;
}
