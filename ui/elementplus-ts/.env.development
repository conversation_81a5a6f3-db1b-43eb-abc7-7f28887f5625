# 页面标题
VITE_APP_TITLE = RuoYi-Flex多租户管理系统

# 开发环境配置
VITE_APP_ENV = 'development'

# 开发环境
VITE_APP_BASE_API = '/dev-api'

# 应用访问路径 例如使用前缀 /admin/
VITE_APP_CONTEXT_PATH = '/'

# 监控地址
VITE_APP_MONITRO_ADMIN = 'http://localhost:9090/admin/applications'

# powerjob 控制台地址
VITE_APP_POWERJOB_ADMIN = 'http://localhost:7700/'

VITE_APP_PORT = 80

# 接口加密功能开关(如需关闭 后端也必须对应关闭)
VITE_APP_ENCRYPT = true
# 接口加密传输 SM2 公钥与后端解密私钥对应 如更换需前后端一同更换
VITE_APP_RSA_PUBLIC_KEY = '049eee13e2add61597243c14f1ae6411856ef02a4e2c51ee37d0475918398c196c956acede43b682ce0af199c65443e3786f96a1b403842bfaab973e9c77f6546f'
# 接口响应解密 SM2 私钥与后端加密公钥对应 如更换需前后端一同更换
VITE_APP_RSA_PRIVATE_KEY = 'cb8d30fcee43dd35b53fe4b68cb8d19e6e6b2d2ef73eeb2a795b28d1e40dcf10'


# 客户端id
VITE_APP_CLIENT_ID = 'e5cd7e4891bf95d1d19206ce24a7b32e'

# websocket 开关 默认使用sse推送
VITE_APP_WEBSOCKET = false

# sse 开关
VITE_APP_SSE = true
