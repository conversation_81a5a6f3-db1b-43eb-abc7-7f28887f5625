import type { DbTableForm, DbTableQuery, DbTableVO, GenTableVO, PreviewVo, TableQuery, TableVO } from './types';
import http from '@/utils/http';
import type { PageData } from '@/utils/http/interface';

// 查询生成表数据
export const listTable = (query: TableQuery) => {
  return http.get<TableVO[]>('/tool/gen/list', query);
};
// 查询db数据库列表
export const listDbTable = (query: DbTableQuery) => {
  return http.get<PageData<DbTableVO>>('/tool/gen/db/list', query);
};

// 查询表详细信息
export const getGenTable = (id: string | number) => {
  return http.get<GenTableVO>('/tool/gen/' + id);
};

// 修改代码生成信息
export const updateGenTable = (data: DbTableForm) => {
  return http.put<GenTableVO>('/tool/gen', data);
};

// 导入表
export const importTable = (data: string[]) => {
  return http.post<GenTableVO>('/tool/gen/importTable', data);
};

// 预览生成代码
export const previewTable = (id: string | number, frontType: number) => {
  const data = {
    id,
    frontType
  };
  return http.get<Array<PreviewVo>>('/tool/gen/preview', data);
};

// 删除表数据
export const delTable = (id: string | number | Array<string | number>) => {
  return http.delete('/tool/gen/' + id);
};

// 生成代码（自定义路径）
export const genCode = (id: string | number, frontType: number) => {
  const data = {
    id,
    frontType
  };
  return http.get('/tool/gen/genCode', data);
};

// 同步数据库
export const synchDb = (id: string | number) => {
  return http.get('/tool/gen/synchDb/' + id);
};
