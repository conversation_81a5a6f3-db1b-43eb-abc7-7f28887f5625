<script setup lang="ts">
import type { VbenFormProps } from '@vben/common-ui';

import type { VxeGridProps } from '#/adapter/vxe-table';
import type { OnLine } from '#/api/monitor/online/model';
import type { ButtonConfig } from '#/components/action-button-group/types';

import { Page } from '@vben/common-ui';

import dayjs from 'dayjs';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { forceLogout, list } from '#/api/monitor/online';

import { querySchema, tableColumns } from './data';

const formOptions: VbenFormProps = {
  schema: querySchema(),
  wrapperClass: 'grid-cols-4',
};

const gridOptions: VxeGridProps = {
  columns: tableColumns,
  pagerConfig: { enabled: true },
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues = {}) => {
        return await list({
          ...formValues,
          pageNum: page.currentPage,
          pageSize: page.pageSize,
        });
      },
    },
  },
};
const [BasicTable, basicTableApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});
const forceExit = async (onLine: OnLine) => {
  window.skynet
    .confirm(`确认要强制退出账号为${onLine.userName}的用户吗?`)
    .then(async () => {
      await forceLogout(onLine.tokenId);
      await basicTableApi.reload();
    });
};
/**
 * 行操作按钮配置
 */
const useRowButtons = (): ButtonConfig[] => {
  return [
    {
      label: '强退',
      link: true,
      onClick: forceExit,
      auth: ['monitor:online:forceLogout'],
    },
  ];
};
</script>

<template>
  <Page :auto-content-height="true">
    <BasicTable table-title="在线用户">
      <template #loginTime="{ row }">
        {{
          row.loginTime
            ? dayjs(row.loginTime).format('YYYY-MM-DD HH:mm:ss')
            : ''
        }}
      </template>
      <template #action="{ row }">
        <ActionButtonGroup :buttons="useRowButtons()" :row="row" />
      </template>
    </BasicTable>
  </Page>
</template>
