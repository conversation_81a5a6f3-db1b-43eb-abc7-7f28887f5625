// 表单 Schema 测试
import { BaseSchema, ZodEnum } from '#/schemas';

describe('表单 Schema 测试', () => {
  describe('baseSchema', () => {
    describe('sTRING', () => {
      it('有效的半角字符串', () => {
        const result = BaseSchema.STRING.safeParse('hello world');
        expect(result.success).toBe(true);
      });

      it('无效的全角字符串', () => {
        const result = BaseSchema.STRING.safeParse('你好世界');
        expect(result.success).toBe(false);
        if (!result.success) {
          expect(result.error.issues[0].message).toBe(
            '不能包含全角字符，请使用半角字符',
          );
        }
      });
    });

    describe('eMAIL', () => {
      it('有效的邮箱', () => {
        const result = BaseSchema.EMAIL.safeParse('<EMAIL>');
        expect(result.success).toBe(true);
      });

      it('全角字符邮箱', () => {
        const result = BaseSchema.EMAIL.safeParse('测试@example.com');
        expect(result.success).toBe(false);
        if (!result.success) {
          expect(result.error.issues[0].message).toBe(
            '不能包含全角字符，请使用半角字符',
          );
        }
      });

      it('格式错误的邮箱', () => {
        const result = BaseSchema.EMAIL.safeParse('invalid-email');
        expect(result.success).toBe(false);
        if (!result.success) {
          expect(result.error.issues[0].message).toBe('请输入正确的邮箱');
        }
      });
    });

    describe('pHONE', () => {
      it('有效的手机号', () => {
        const result = BaseSchema.PHONE.safeParse('13812345678');
        expect(result.success).toBe(true);
      });

      it('全角字符手机号', () => {
        const result = BaseSchema.PHONE.safeParse('１３８１２３４５６７８');
        expect(result.success).toBe(false);
        if (!result.success) {
          expect(result.error.issues[0].message).toBe(
            '不能包含全角字符，请使用半角字符',
          );
        }
      });

      it('格式错误的手机号', () => {
        const result = BaseSchema.PHONE.safeParse('12345678901');
        expect(result.success).toBe(false);
        if (!result.success) {
          expect(result.error.issues[0].message).toBe('请输入正确的手机号');
        }
      });
    });

    describe('iD_CARD', () => {
      it('有效的身份证号码', () => {
        const result = BaseSchema.ID_CARD.safeParse('11010519491231002X');
        expect(result.success).toBe(true);
      });

      it('全角字符身份证号码', () => {
        const result =
          BaseSchema.ID_CARD.safeParse('１１０１０５１９４９１２３１００２Ｘ');
        expect(result.success).toBe(false);
        if (!result.success) {
          expect(result.error.issues[0].message).toBe(
            '不能包含全角字符，请使用半角字符',
          );
        }
      });

      it('长度错误的身份证号码', () => {
        const result = BaseSchema.ID_CARD.safeParse('1101051949123100');
        expect(result.success).toBe(false);
        if (!result.success) {
          expect(result.error.issues[0].message).toBe('身份证号码必须为18位');
        }
      });

      it('校验码错误的身份证号码', () => {
        const result = BaseSchema.ID_CARD.safeParse('11010519491231002Y');
        expect(result.success).toBe(false);
        if (!result.success) {
          expect(result.error.issues[0].message).toBe('身份证号码校验码错误');
        }
      });
    });

    describe('gAJGJGDM', () => {
      it('有效的机构代码', () => {
        const result = BaseSchema.GAJGJGDM.safeParse('123456789012');
        expect(result.success).toBe(true);
      });

      it('全角字符机构代码', () => {
        const result =
          BaseSchema.GAJGJGDM.safeParse('１２３４５６７８９０１２');
        expect(result.success).toBe(false);
        if (!result.success) {
          expect(result.error.issues[0].message).toBe(
            '不能包含全角字符，请使用半角字符',
          );
        }
      });

      it('长度错误的机构代码', () => {
        const result = BaseSchema.GAJGJGDM.safeParse('12345');
        expect(result.success).toBe(false);
        if (!result.success) {
          expect(result.error.issues[0].message).toBe('请输入12位数字');
        }
      });
    });

    describe('cHINESE_NAME', () => {
      it('有效的中文姓名', () => {
        const validNames = [
          '张三',
          '李四',
          '王小明',
          '欧阳修',
          '爱新觉罗·玄烨',
          '叶赫那拉·慧征',
        ];

        validNames.forEach(name => {
          const result = BaseSchema.CHINESE_NAME.safeParse(name);
          expect(result.success).toBe(true);
        });
      });

      it('包含英文的姓名', () => {
        const result = BaseSchema.CHINESE_NAME.safeParse('张San');
        expect(result.success).toBe(false);
        if (!result.success) {
          expect(result.error.issues[0].message).toBe('只能输入中文字符');
        }
      });

      it('包含禁用字符的姓名', () => {
        const result = BaseSchema.CHINESE_NAME.safeParse('张【三】');
        expect(result.success).toBe(false);
        if (!result.success) {
          expect(result.error.issues[0].message).toBe('姓名包含不允许的字符');
        }
      });

      it('中文点使用错误的姓名', () => {
        const result = BaseSchema.CHINESE_NAME.safeParse('·张三');
        expect(result.success).toBe(false);
        if (!result.success) {
          expect(result.error.issues[0].message).toBe('中文点（·）只能在姓名中间使用，且只能有一个');
        }
      });

      it('空姓名', () => {
        const result = BaseSchema.CHINESE_NAME.safeParse('');
        expect(result.success).toBe(false);
        if (!result.success) {
          expect(result.error.issues[0].message).toBe('姓名不能为空');
        }
      });
    });
  });

  describe('zodEnum', () => {
    describe('可选字段测试', () => {
      it('eMAIL_EMPTY 允许空值', () => {
        const emptyResult = ZodEnum.EMAIL_EMPTY.safeParse('');
        expect(emptyResult.success).toBe(true);

        const validResult = ZodEnum.EMAIL_EMPTY.safeParse('<EMAIL>');
        expect(validResult.success).toBe(true);
      });

      it('pHONE_EMPTY 允许空值', () => {
        const emptyResult = ZodEnum.PHONE_EMPTY.safeParse('');
        expect(emptyResult.success).toBe(true);

        const validResult = ZodEnum.PHONE_EMPTY.safeParse('13812345678');
        expect(validResult.success).toBe(true);
      });

      it('iD_CARD_EMPTY 允许空值', () => {
        const emptyResult = ZodEnum.ID_CARD_EMPTY.safeParse('');
        expect(emptyResult.success).toBe(true);

        const validResult =
          ZodEnum.ID_CARD_EMPTY.safeParse('11010519491231002X');
        expect(validResult.success).toBe(true);
      });

      it('gAJGJGDM_EMPTY 允许空值', () => {
        const emptyResult = ZodEnum.GAJGJGDM_EMPTY.safeParse('');
        expect(emptyResult.success).toBe(true);

        const validResult = ZodEnum.GAJGJGDM_EMPTY.safeParse('123456789012');
        expect(validResult.success).toBe(true);
      });

      it('cHINESE_NAME_EMPTY 允许空值', () => {
        const emptyResult = ZodEnum.CHINESE_NAME_EMPTY.safeParse('');
        expect(emptyResult.success).toBe(true);

        const validResult = ZodEnum.CHINESE_NAME_EMPTY.safeParse('张三');
        expect(validResult.success).toBe(true);
      });
    });
  });

  describe('验证优先级测试', () => {
    it('全角字符错误优先级最高', () => {
      // 全角数字但长度正确 - 应该显示全角字符错误
      const result =
        BaseSchema.ID_CARD.safeParse('１２３４５６７８９０１２３４５６７８');
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].message).toBe(
          '不能包含全角字符，请使用半角字符',
        );
      }
    });
  });
});

export {};
