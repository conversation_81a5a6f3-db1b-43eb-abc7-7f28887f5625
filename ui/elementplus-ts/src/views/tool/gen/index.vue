<script setup lang="ts">
defineOptions({
  name: 'GenList'
});
// skTable 实例
import { useGen } from '@/views/tool/gen/utils';
import { listTable } from '@/api/tool/gen';
import { Delete, Download, EditPen, Refresh, Upload, View } from '@element-plus/icons-vue';

const { skTable, columns, openInputDialog, openPreviewDialog, handleEditTable, handleGenTable, handleSyncDb, handleDelete } =
  useGen();

// 如果你想在请求之前对当前请求参数做一些操作，可以自定义如下函数：params 为当前所有的请求参数（包括分页），最后返回请求列表接口
// 默认不做操作就直接在 ProTable 组件上绑定	:requestApi="getUserList"
const getTableList = (params: any) => {
  let newParams = JSON.parse(JSON.stringify(params));
  newParams.createTime && (newParams.beginTime = newParams.createTime[0]);
  newParams.createTime && (newParams.endTime = newParams.createTime[1]);
  delete newParams.createTime;
  return listTable(newParams);
};
</script>
<template>
  <div class="table-box">
    <SkTable
      ref="skTable"
      row-key="id"
      :columns="columns"
      :request-api="getTableList"
      :search-col="{ xs: 1, sm: 1, md: 2, lg: 3, xl: 3 }"
    >
      <!-- 表格 header 按钮 -->
      <template #tableHeader="scope">
        <el-button
          v-hasPermi="['tool:gen:code']"
          type="primary"
          :icon="Download"
          :disabled="!scope.isSelected"
          @click="handleGenTable(null, scope.selectedListIds)"
        >
          生成
        </el-button>
        <el-button v-hasPermi="['tool:gen:import']" type="primary" plain :icon="Upload" @click="openInputDialog">
          导入
        </el-button>
        <el-button
          v-hasPermi="['mf:customer:remove']"
          type="danger"
          :icon="Delete"
          :disabled="!scope.isSelected"
          @click="handleDelete(scope.selectedListIds)"
        >
          删除
        </el-button>
      </template>
      <!-- 表格操作 -->
      <template #operation="scope">
        <div>
          <el-button v-hasPermi="['tool:gen:preview']" type="primary" link :icon="View" @click="openPreviewDialog(scope.row)">
            预览
          </el-button>
          <el-button v-hasPermi="['tool:gen:edit']" type="primary" link :icon="EditPen" @click="handleEditTable(scope.row)">
            编辑
          </el-button>
          <el-button v-hasPermi="['tool:gen:edit']" type="primary" link :icon="Refresh" @click="handleSyncDb(scope.row)">
            同步
          </el-button>
          <el-button v-hasPermi="['tool:gen:code']" type="primary" link :icon="Download" @click="handleGenTable(scope.row)">
            生成代码
          </el-button>
          <el-button v-hasPermi="['mf:customer:remove']" type="danger" link :icon="Delete" @click="handleDelete(scope.row.id)"
            >删除
          </el-button>
        </div>
      </template>
    </SkTable>
  </div>
</template>
