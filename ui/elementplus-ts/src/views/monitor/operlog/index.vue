<script setup name="Operlog" lang="ts">
import { operlogOptions } from '@/views/monitor/operlog/utils';
import { list } from '@/api/monitor/operlog';
import { Delete } from '@element-plus/icons-vue';

const { skTable, columns, openDialog, handleDelete, handleClean, handleExport } = operlogOptions();
</script>

<template>
  <div class="table-box">
    <SkTable ref="skTable" :columns="columns" :request-api="list" :search-col="{ xs: 1, sm: 1, md: 2, lg: 3, xl: 3 }">
      <!-- 表格 header 按钮 -->
      <template #tableHeader>
        <el-button v-hasPermi="['system:config:add']" type="primary" icon="WarnTriangleFilled" @click="handleClean()">
          清空
        </el-button>
        <el-button v-hasPermi="['system:config:export']" type="primary" plain icon="Download" @click="handleExport()">
          导出
        </el-button>
      </template>
      <!-- 表格操作 -->
      <template #operation="scope">
        <div>
          <el-button v-hasPermi="['system:config:edit']" type="primary" link icon="View" @click="openDialog(scope.row)"
            >详情
          </el-button>
          <el-button v-hasPermi="['mf:customer:remove']" type="danger" link :icon="Delete" @click="handleDelete(scope.row)"
            >删除
          </el-button>
        </div>
      </template>
      <template #businessType="scope">
        <dict-tag data="czlx" :value="scope.row.businessType" />
      </template>
      <template #status="scope">
        <dict-tag data="xtzt" :value="scope.row.status" />
      </template>
      <template #costTime="scope">
        <span>{{ scope.row.costTime }}毫秒</span>
      </template>
    </SkTable>
  </div>
</template>
