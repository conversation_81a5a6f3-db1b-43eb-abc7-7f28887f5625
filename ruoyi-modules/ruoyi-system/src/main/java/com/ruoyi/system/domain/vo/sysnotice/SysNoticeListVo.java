package com.ruoyi.system.domain.vo.sysnotice;

import lombok.Data;
import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 通知公告视图对象 sys_notice
 *
 * <AUTHOR>
 * @since 2025-05-10 18:01:12
 */
@Data
public class SysNoticeListVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 公告ID */
    private String id;
    /** 公告标题 */
    private String title;
    /** 公告类型（1通知 2公告） */
    private String type;
    /** 公告状态（0正常 1关闭） */
    private String status;
    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" )
    private LocalDateTime createTime;
}
