package com.ruoyi.dzgl.domain.po;

import java.time.LocalDateTime;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import com.ruoyi.common.orm.core.domain.TreeEntity;

/**
 * 地址元素管理对象 dz_dzys
 *
 * <AUTHOR>
 * @since 2025-06-04 16:50:12
 */
@Data
@Table(value = "dz_dzys")
public class Dzys extends TreeEntity {

    /** 主键 */
    @Id
    private String id;

    /** 州市区划 */
    private String zsqh;

    /** 删除标志（1代表存在 0代表删除） */
    @Column(isLogicDelete = true)
    private Integer jlzt;

    /** 代码 */
    private String dm;

    /** 名称 */
    private String mc;

    /** 简称 */
    private String jc;

    /** 地址元素类型代码 */
    private String dzyslxdm;

    /** 使用状态代码 */
    private String syztdm;

    /** 数据归属单位ID */
    private String sjgsDeptId;

    /** 流动人口数据归属单位ID */
    private String sjgsLdrkDeptId;

    /** 设立日期 */
    private LocalDateTime slrq;

    /** 启用日期 */
    private LocalDateTime qyrq;

    /** 停用日期 */
    private LocalDateTime tyrq;

    /** 抵边判断标志 */
    private String dbPdbz;

    /** 公安部地址编码 */
    private String gabDzbm;

    /** 公安部上级编号 */
    private String gabSjbh;

    /** 备案状态代码 */
    private String baztdm;


}
