import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { VbenFormSchema } from '#/adapter/form';

import { z } from '#/adapter/form';
import { renderTag } from '#/utils/render';

const accessPolicyOptions: {
  label: string;
  type: 'danger' | 'info' | 'primary' | 'success' | 'warning';
  value: string;
}[] = [
  { type: 'warning', label: '私有', value: '0' },
  { type: 'success', label: '公开', value: '1' },
  { type: 'info', label: '自定义', value: '2' },
];
/**
 * 获取编辑表单的字段配置
 * @returns VbenFormSchema[] 表单配置
 */
export function useFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      dependencies: {
        show: () => false,
        triggerFields: [''],
      },
      fieldName: 'id',
    },
    {
      component: 'Input',
      dependencies: {
        show: () => false,
        triggerFields: [''],
      },
      fieldName: 'version',
      label: '乐观锁',
    },
    {
      component: 'SkDivider',
      componentProps: {
        content: '基本信息',
      },
      fieldName: 'divider1',
      hideLabel: true,
    },
    {
      component: 'Input',
      fieldName: 'key',
      label: '配置名称',
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'endpoint',
      label: '服务地址',
      renderComponentContent: (formModel) => ({
        append: () => (formModel.isHttps === 'Y' ? 'https://' : 'http://'),
      }),
      rules: z
        .string()
        .refine((domain) => domain && !/^https?:\/\/.*/.test(domain), {
          message: '请输入正确的域名, 不需要http(s)',
        }),
    },
    {
      component: 'Input',
      fieldName: 'domain',
      label: '自定义域名',
    },
    {
      component: 'Input',
      fieldName: 'tip',
      label: '占位作为提示使用',
      hideLabel: true,
    },
    {
      component: 'SkDivider',
      componentProps: {
        content: '认证信息',
      },
      fieldName: 'divider2',
      hideLabel: true,
    },
    {
      component: 'Input',
      fieldName: 'accessKey',
      label: 'accessKey',
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'secretKey',
      label: 'secretKey',
      rules: 'required',
    },
    {
      component: 'SkDivider',
      componentProps: {
        content: '其他信息',
      },
      fieldName: 'divider3',
      hideLabel: true,
    },
    {
      component: 'Input',
      fieldName: 'bucketName',
      label: '桶名称',
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'prefix',
      label: '前缀',
    },
    {
      component: 'RadioGroup',
      componentProps: {
        options: accessPolicyOptions,
        isButton: true,
      },
      defaultValue: '0',
      fieldName: 'accessPolicy',
      formItemClass: 'col-span-3 lg:col-span-2',
      label: '权限桶类型',
    },
    {
      component: 'SkDict',
      fieldName: 'status',
      label: '配置状态',
      rules: 'required',
      defaultValue: '0',
      componentProps: {
        data: 'xtkg',
        type: 'radio-group-button',
        placeholder: '请选择配置状态',
        clearable: true,
      },
    },
    {
      component: 'SkDict',
      fieldName: 'isHttps',
      label: '是否https',
      rules: 'required',
      defaultValue: 'N',
      componentProps: {
        data: 'xtsf',
        placeholder: '请选择是否https',
        clearable: true,
        type: 'radio-group-button',
      },
      formItemClass: 'col-span-3 lg:col-span-1',
    },
    {
      component: 'Input',
      fieldName: 'region',
      label: '区域',
    },
    {
      component: 'Textarea',
      fieldName: 'remark',
      formItemClass: 'items-start',
      label: '备注',
    },
  ];
}

/**
 * 获取查询表单的字段配置
 * @returns VbenFormSchema[] 查询表单配置
 */
export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'key',
      label: '配置名称',
      componentProps: {
        placeholder: '请输入配置名称',
        clearable: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'bucketName',
      label: '桶名称',
      componentProps: {
        placeholder: '请输入桶名称',
        clearable: true,
      },
    },
    {
      component: 'SkDict',
      fieldName: 'isHttps',
      label: '是否https',
      componentProps: {
        data: 'xtsf',
        placeholder: '请输入是否https',
        clearable: true,
      },
    },
    {
      component: 'SkDict',
      fieldName: 'status',
      label: '配置状态',
      componentProps: {
        data: 'xtkg',
        placeholder: '请选择配置状态',
        clearable: true,
      },
    },
  ];
}

/**
 * 获取表格列配置
 * @returns VxeTableGridOptions['columns'] 表格列配置
 * @description 使用函数的形式返回列数据而不是直接export一个Array常量，是为了响应语言切换时重新翻译表头
 */
export function useGridColumns(): VxeTableGridOptions['columns'] {
  return [
    {
      type: 'checkbox',
      width: 60,
    },
    {
      title: '#',
      type: 'seq',
      width: 60,
      fixed: 'left',
    },
    {
      field: 'key',
      title: '配置名称',
      align: 'center',
    },
    {
      field: 'bucketName',
      title: '桶名称',
      align: 'center',
    },
    {
      field: 'endpoint',
      title: '访问站点',
      align: 'center',
    },
    {
      field: 'region',
      title: '域',
      align: 'center',
    },
    {
      field: 'accessPolicy',
      title: '桶权限类型',
      align: 'center',
      slots: {
        default: ({ row }) => {
          const current = accessPolicyOptions.find(
            (item) => item.value === row.accessPolicy,
          );
          if (current) {
            return renderTag(current.label, current.type);
          }
          return '未知类型';
        },
      },
    },
    {
      field: 'status',
      title: '配置状态',
      align: 'center',
      slots: { default: 'status' },
      minWidth: 80,
    },
    {
      field: 'remark',
      title: '备注',
      align: 'center',
    },
    {
      title: '操作',
      field: 'action',
      fixed: 'right',
      slots: { default: 'action' },
      width: 240,
    },
  ];
}
