package com.ruoyi.common.core.exception.common;

import com.ruoyi.common.core.exception.ServiceException;

import java.text.MessageFormat;

public interface ServiceExceptionCustomAssert extends IResponseEnum, CustomAssert {

    @Override
    default ServiceException newException(Object... args) {
        String msg = MessageFormat.format(this.getMsg(), args);
        return new ServiceException(msg, this.getCode());
    }

    @Override
    default ServiceException newException(Throwable t, Object... args) {
        String msg = MessageFormat.format(this.getMsg(), args);
        return new ServiceException(msg, this.getCode(), t);
    }

    /**
     * 创建异常，允许传递自定义的message和args
     *
     * @param message 自定义错误消息
     * @param args    message占位符对应的参数列表
     * @return 创建的异常
     */
    default ServiceException newException(String message, Object... args) {
        String formattedMessage = MessageFormat.format(message, args);
        return new ServiceException(formattedMessage, this.getCode());
    }
}
