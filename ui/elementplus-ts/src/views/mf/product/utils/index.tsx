import type { ColumnProps, SkTableInstance } from '@/components/SkUi/SkTable/interface';
import type { ProductDataOption, ProductForm, ProductVO } from '@/api/mf/product/types';
import mfProductDialog from '@/views/mf/product/components/MfProductDialog.vue';
import { addProduct, editProduct, getProduct, listProduct, listProductOption, removeProduct } from '@/api/mf/product';
import { addDialog } from '@/components/SkUi/SkReDialog';
import { h, reactive, ref, type Ref } from 'vue';
import { useValidator } from '@/hooks/skUi/web/useValidator';

/**
 * 产品树管理Hook
 * @returns 产品树管理相关的状态和方法
 */
export function useProduct() {
  const skTable = ref<SkTableInstance>();
  const { required } = useValidator();
  const loadMap = new Map<
    string | number,
    {
      /** 当前行数据 */
      row: ProductVO;
      /** 树节点信息 */
      treeNode: unknown;
      /** 异步加载完成的回调函数 */
      resolve: (data: ProductVO[]) => void;
    }
  >();
  const isExpandAll = ref(false);

  const columns = reactive<ColumnProps<ProductVO>[]>([
    { type: 'index', label: '#', width: 80 },
    {
      prop: 'id',
      align: 'center',
      label: '产品id'
    },
    {
      prop: 'parentId',
      search: {
        el: 'input'
      },
      align: 'center',
      label: '父产品id'
    },
    {
      prop: 'productName',
      search: {
        el: 'input'
      },
      align: 'center',
      label: '产品名称'
    },
    {
      prop: 'orderNum',
      search: {
        el: 'input'
      },
      align: 'center',
      label: '显示顺序'
    },
    {
      prop: 'status',
      search: {
        el: 'input'
      },
      align: 'center',
      label: '产品状态'
    },
    { prop: 'operation', slot: 'operation', label: '操作', width: 300, fixed: 'right' }
  ]);

  const rules = reactive<Record<string, any>>({
    id: [required('产品id')],
    parentId: [required('父产品id')],
    productName: [required('产品名称')],
    orderNum: [required('显示顺序')],
    status: [required('产品状态')],
  });

  /**
   * 异步加载树节点数据
   * @param {ProductVO} row - 当前行数据
   * @param {unknown} treeNode - 树节点信息
   * @param {Function} resolve - 加载完成回调
   * @returns {Promise<void>}
   */
  const load = async (row: ProductVO, treeNode: unknown, resolve: (data: ProductVO[]) => void): Promise<void> => {
    loadMap.set(row.id, { row, treeNode, resolve });
    const res = await listProduct({ parentId: row.id });
    resolve(res.data);
  };

  /**
   * 重新加载指定节点的子节点数据
   * @param {string | number} id - 节点ID
   * @returns {Promise<void>}
   */
  const refreshLoad = async (id: string | number): Promise<void> => {
    const cached = loadMap.get(id);
    if (cached) {
      const { row, resolve } = cached;
      await load(row, null, resolve);
    }
  };

  /**
   * 获取产品树树形选择数据
   * @returns {Promise<ProductDataOption[]>} 树形结构的数据
   */
  const getTreeSelect = async (): Promise<ProductDataOption[]> => {
    const res = await listProductOption({ isTree: true });
    const rootNode: ProductDataOption = {
      id: '0',
      value: '0',
      label: '顶级节点',
      isLeaf: false,
      children: res.data
    };
    return [rootNode];
  };

  /**
   * 初始化表单数据
   * @param {Partial<ProductVO>} productVo - 产品树数据
   * @returns {Promise<Ref<ProductForm>>} 表单响应式对象
   */
  const initFormProps = async (productVo: Partial<ProductVO>): Promise<Ref<ProductForm>> => {
    const formInline = ref<ProductForm>({ parentId: '0' });

    if (productVo.id) {
      const res = await getProduct(productVo.id);
      formInline.value = structuredClone(res.data);
      return formInline;
    }

    if (productVo.parentId) {
      Object.assign(formInline.value, productVo);
    }

    return formInline;
  };

  /**
   * 父节点选择事件处理
   * @param {string} value - 选中的值
   * @param {ProductForm} formInline - 表单数据
   * @returns {Promise<void>}
   */
  const handleChane = async (value: string, formInline: ProductForm): Promise<void> => {
    if (!value) return;

    formInline.parentId = value === '0' ? '0' : value;

    if (value !== '0') {
      const { data } = await getProduct(value);
      formInline.id = data.id;
    }
  };

  /**
   * 打开编辑/新增对话框
   * @param {string} title - 对话框标题
   * @param {Partial<ProductVO>} [row={}] - 行数据
   * @returns {Promise<void>}
   */
  const openDialog = async (title: string, row: Partial<ProductVO> = {}): Promise<void> => {
    addDialog({
      title: `${title}产品树`,
      width: '1024px',
      props: {
        model: await initFormProps(row),
        labelWidth: '140px',
        rules,
        productDataOptions: await getTreeSelect()
      },
      contentRenderer: () => h(mfProductDialog),
      beforeSure: async (done, { options }, formRef) => {
        const formData = options.props.model as ProductForm;

        formRef.validate(async (valid: boolean) => {
          if (!valid) return;

          const api = title === '新增' ? addProduct : editProduct;
          const res = await api(formData);

          if (res.code === 200) {
            window.skynet.alert(`${title}成功！`);
            done();

            if (!row.parentId || row.parentId === '0') {
              await skTable.value?.getTableList();
            } else {
              await refreshLoad(row.parentId);
            }
          }
        });
      }
    });
  };

  /**
   * 导出数据
   * @returns {void}
   */
  const handleExport = (): void => {
    window.skynet
      .download(
        'mf/product/export',
        {
          ...skTable.value.searchParam
        },
        `product_${new Date().getTime()}.xlsx`
      )
      .then(() => {});
  };

  /**
   * 删除产品树
   * @param {ProductVO} [row] - 要删除的数据
   * @returns {Promise<void>}
   * @throws {Error} 删除失败时抛出错误
   */
  const handleDelete = async (row?: ProductVO): Promise<void> => {
    if (!row?.id) return;

    const _ids = row?.id;
    await window.skynet.confirm('是否确认删除产品树编号为"' + _ids + '"的数据项？');

    try {
      await removeProduct(row.id);
      window.skynet.alert('删除成功');

      if (!row.parentId || row.parentId === '0') {
        await skTable.value?.getTableList();
      } else {
        await refreshLoad(row.parentId);
      }
    } catch (error) {
      console.error('删除失败:', error);
    }
  };

  return {
    skTable,
    columns,
    load,
    rules,
    openDialog,
    handleExport,
    handleDelete,
    handleChane,
    isExpandAll
  };
}
