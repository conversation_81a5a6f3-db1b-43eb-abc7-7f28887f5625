package com.ruoyi.generator.domain.vo;

import lombok.Data;

/**
 * 生成代码预览VO
 *
 * <AUTHOR>
 */
@Data
public class PreviewVo {

    public PreviewVo(String name, String language, String alias) {
        this.name = name;
        this.language = language;
        this.alias = alias;
    }

    public PreviewVo(String name, String code, String language, String alias) {
        this.name = name;
        this.code = code;
        this.language = language;
        this.alias = alias;
    }

    /**
     * 文件名称
     */
    private String name;

    /**
     * 代码
     */
    private String code;

    /**
     * 语言
     */
    private String language;

    /**
     * 别名
     */
    private String alias;
}
