<script setup lang="ts">
// 接收父组件参数并设置默认值
import { TenantForm } from '@/api/system/tenant/types';
import { useTenant } from '@/views/system/tenant/utils';
import { TenantPkgVO } from '@/api/system/tenantPackage/types';

const {} = useTenant();
//定义属性并指定默认值
const contentProps = defineProps<{
  model?: TenantForm;
  packageList?: TenantPkgVO[];
}>();
const form = ref(contentProps.model);
</script>

<template>
  <el-row>
    <el-col :span="12">
      <el-form-item label="企业名称" prop="companyName">
        <el-input v-model="form.companyName" placeholder="请输入企业名称" />
      </el-form-item>
    </el-col>
    <el-col :span="12">
      <el-form-item label="联系人" prop="contactUserName">
        <el-input v-model="form.contactUserName" placeholder="请输入联系人" />
      </el-form-item>
    </el-col>
    <el-col :span="12">
      <el-form-item label="联系电话" prop="contactPhone">
        <el-input v-model="form.contactPhone" placeholder="请输入联系电话" />
      </el-form-item>
    </el-col>
    <el-col :span="12">
      <el-form-item v-if="!form.id" label="用户名" prop="username">
        <el-input v-model="form.username" placeholder="请输入系统用户名" maxlength="30" />
      </el-form-item>
    </el-col>
    <el-col :span="12">
      <el-form-item v-if="!form.id" label="用户密码" prop="password">
        <el-input v-model="form.password" type="password" placeholder="请输入系统用户密码" maxlength="20" />
      </el-form-item>
    </el-col>
    <el-col :span="12">
      <el-form-item label="租户套餐" prop="packageId">
        <el-select v-model="form.packageId" :disabled="!!form.id" placeholder="请选择租户套餐" clearable style="width: 100%">
          <el-option v-for="item in contentProps.packageList" :key="item.id" :label="item.packageName" :value="item.id" />
        </el-select>
      </el-form-item>
    </el-col>
    <el-col :span="12">
      <el-form-item label="过期时间" prop="expireTime">
        <el-date-picker
          v-model="form.expireTime"
          clearable
          type="datetime"
          value-format="YYYY-MM-DD HH:mm:ss"
          placeholder="请选择过期时间"
        >
        </el-date-picker>
      </el-form-item>
    </el-col>
    <el-col :span="12">
      <el-form-item label="用户数量" prop="accountCount">
        <el-input v-model="form.accountCount" placeholder="请输入用户数量" />
      </el-form-item>
    </el-col>
    <el-col :span="12">
      <el-form-item label="绑定域名" prop="domain">
        <el-input v-model="form.domain" placeholder="请输入绑定域名" />
      </el-form-item>
    </el-col>
    <el-col :span="12">
      <el-form-item label="企业地址" prop="address">
        <el-input v-model="form.address" placeholder="请输入企业地址" />
      </el-form-item>
    </el-col>
    <el-col :span="12">
      <el-form-item label="企业代码" prop="licenseNumber">
        <el-input v-model="form.licenseNumber" placeholder="请输入统一社会信用代码" />
      </el-form-item>
    </el-col>
    <el-col :span="12">
      <el-form-item label="企业简介" prop="intro">
        <el-input v-model="form.intro" type="textarea" placeholder="请输入企业简介" />
      </el-form-item>
    </el-col>
    <el-col :span="24">
      <el-form-item label="备注" prop="remark">
        <el-input v-model="form.remark" placeholder="请输入备注" />
      </el-form-item>
    </el-col>
  </el-row>
</template>
