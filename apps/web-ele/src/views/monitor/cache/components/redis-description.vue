<script setup lang="ts">
import type { RedisInfo } from '#/api/monitor/cache';

import { computed } from 'vue';

interface IRedisInfo extends RedisInfo {
  dbSize: string;
}

// 直接使用 defineProps 的返回值
const { data } = defineProps<{ data: IRedisInfo }>();

// 根据屏幕宽度计算列数
const columnCount = computed(() => {
  const width = window.innerWidth;
  if (width >= 1920) return 4; // xl
  if (width >= 1200) return 4; // lg
  if (width >= 992) return 3; // md
  if (width >= 768) return 2; // sm
  return 1; // xs
});
</script>

<template>
  <el-descriptions border :column="columnCount" size="default">
    <el-descriptions-item label="redis版本">
      {{ data.redis_version }}
    </el-descriptions-item>
    <el-descriptions-item label="redis模式">
      {{ data.redis_mode === 'standalone' ? '单机模式' : '集群模式' }}
    </el-descriptions-item>
    <el-descriptions-item label="tcp端口">
      {{ data.tcp_port }}
    </el-descriptions-item>
    <el-descriptions-item label="客户端数">
      {{ data.connected_clients }}
    </el-descriptions-item>
    <el-descriptions-item label="运行时间">
      {{ data.uptime_in_days }} 天
    </el-descriptions-item>
    <el-descriptions-item label="使用内存">
      {{ data.used_memory_human }}
    </el-descriptions-item>
    <el-descriptions-item label="使用CPU">
      {{ Number.parseFloat(data?.used_cpu_user_children ?? '0').toFixed(2) }}
    </el-descriptions-item>
    <el-descriptions-item label="内存配置">
      {{ data.maxmemory_human }}
    </el-descriptions-item>
    <el-descriptions-item label="AOF是否开启">
      {{ data.aof_enabled === '0' ? '否' : '是' }}
    </el-descriptions-item>
    <el-descriptions-item label="RDB是否成功">
      {{ data.rdb_last_bgsave_status }}
    </el-descriptions-item>
    <el-descriptions-item label="key数量">
      {{ data.dbSize }}
    </el-descriptions-item>
    <el-descriptions-item label="网络入口/出口">
      {{
        `${data.instantaneous_input_kbps}kps/${data.instantaneous_output_kbps}kps`
      }}
    </el-descriptions-item>
  </el-descriptions>
</template>
