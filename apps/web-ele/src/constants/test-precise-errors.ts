// 测试精准的错误提示
import { BaseSchema } from './zod-enum';

console.log('=== 测试精准的错误提示 ===');

// 1. GAJGJGDM 测试
console.log('\n1. GAJGJGDM 各种错误类型测试:');

// 1.1 全角数字 - 应该显示全角字符错误
const gajgjgdmFullWidth = BaseSchema.GAJGJGDM.safeParse('１２３４５６７８９０１２');
console.log('全角数字结果:', gajgjgdmFullWidth);
if (!gajgjgdmFullWidth.success) {
  console.log('全角数字错误:', gajgjgdmFullWidth.error.issues[0].message);
  console.log('期望: "不能包含全角字符，请使用半角字符"');
}

// 1.2 半角但长度不够 - 应该显示长度错误
const gajgjgdmShort = BaseSchema.GAJGJGDM.safeParse('12345');
console.log('半角长度不够结果:', gajgjgdmShort);
if (!gajgjgdmShort.success) {
  console.log('半角长度不够错误:', gajgjgdmShort.error.issues[0].message);
  console.log('期望: "请输入12位数字"');
}

// 1.3 半角但包含字母 - 应该显示格式错误
const gajgjgdmLetters = BaseSchema.GAJGJGDM.safeParse('abcdefghijkl');
console.log('半角字母结果:', gajgjgdmLetters);
if (!gajgjgdmLetters.success) {
  console.log('半角字母错误:', gajgjgdmLetters.error.issues[0].message);
  console.log('期望: "请输入12位数字"');
}

// 1.4 半角但长度过长 - 应该显示长度错误
const gajgjgdmLong = BaseSchema.GAJGJGDM.safeParse('1234567890123');
console.log('半角长度过长结果:', gajgjgdmLong);
if (!gajgjgdmLong.success) {
  console.log('半角长度过长错误:', gajgjgdmLong.error.issues[0].message);
  console.log('期望: "请输入12位数字"');
}

// 1.5 正确的情况
const gajgjgdmCorrect = BaseSchema.GAJGJGDM.safeParse('123456789012');
console.log('正确情况:', gajgjgdmCorrect);
console.log('期望: success: true');

// 2. EMAIL 测试
console.log('\n2. EMAIL 各种错误类型测试:');

// 2.1 全角字符 - 应该显示全角字符错误
const emailFullWidth = BaseSchema.EMAIL.safeParse('测试@example.com');
console.log('EMAIL 全角字符结果:', emailFullWidth);
if (!emailFullWidth.success) {
  console.log('EMAIL 全角字符错误:', emailFullWidth.error.issues[0].message);
  console.log('期望: "不能包含全角字符，请使用半角字符"');
}

// 2.2 半角但格式错误 - 应该显示邮箱格式错误
const emailFormat = BaseSchema.EMAIL.safeParse('invalid-email');
console.log('EMAIL 格式错误结果:', emailFormat);
if (!emailFormat.success) {
  console.log('EMAIL 格式错误:', emailFormat.error.issues[0].message);
  console.log('期望: "请输入正确的邮箱"');
}

// 2.3 正确的情况
const emailCorrect = BaseSchema.EMAIL.safeParse('<EMAIL>');
console.log('EMAIL 正确情况:', emailCorrect);
console.log('期望: success: true');

// 3. PHONE 测试
console.log('\n3. PHONE 各种错误类型测试:');

// 3.1 全角数字 - 应该显示全角字符错误
const phoneFullWidth = BaseSchema.PHONE.safeParse('１３８１２３４５６７８');
console.log('PHONE 全角数字结果:', phoneFullWidth);
if (!phoneFullWidth.success) {
  console.log('PHONE 全角数字错误:', phoneFullWidth.error.issues[0].message);
  console.log('期望: "不能包含全角字符，请使用半角字符"');
}

// 3.2 半角但格式错误 - 应该显示手机号格式错误
const phoneFormat = BaseSchema.PHONE.safeParse('12345678901');
console.log('PHONE 格式错误结果:', phoneFormat);
if (!phoneFormat.success) {
  console.log('PHONE 格式错误:', phoneFormat.error.issues[0].message);
  console.log('期望: "请输入正确的手机号"');
}

// 3.3 正确的情况
const phoneCorrect = BaseSchema.PHONE.safeParse('13812345678');
console.log('PHONE 正确情况:', phoneCorrect);
console.log('期望: success: true');

// 4. STRING 测试
console.log('\n4. STRING 测试:');

// 4.1 全角字符 - 应该显示全角字符错误
const stringFullWidth = BaseSchema.STRING.safeParse('你好世界');
console.log('STRING 全角字符结果:', stringFullWidth);
if (!stringFullWidth.success) {
  console.log('STRING 全角字符错误:', stringFullWidth.error.issues[0].message);
  console.log('期望: "不能包含全角字符，请使用半角字符"');
}

// 4.2 半角字符 - 应该成功
const stringHalfWidth = BaseSchema.STRING.safeParse('hello world');
console.log('STRING 半角字符结果:', stringHalfWidth);
console.log('期望: success: true');

// 5. 边界情况测试
console.log('\n5. 边界情况测试:');

// 5.1 空字符串
console.log('空字符串 GAJGJGDM:', BaseSchema.GAJGJGDM.safeParse(''));
console.log('空字符串 EMAIL:', BaseSchema.EMAIL.safeParse(''));
console.log('空字符串 STRING:', BaseSchema.STRING.safeParse(''));

// 5.2 混合字符（半角+全角）
const mixedChars = BaseSchema.GAJGJGDM.safeParse('123４５６789012');
console.log('混合字符结果:', mixedChars);
if (!mixedChars.success) {
  console.log('混合字符错误:', mixedChars.error.issues[0].message);
  console.log('期望: "不能包含全角字符，请使用半角字符"');
}

export {
  gajgjgdmFullWidth,
  gajgjgdmShort,
  gajgjgdmLetters,
  emailFullWidth,
  emailFormat,
  phoneFullWidth,
  phoneFormat
};
