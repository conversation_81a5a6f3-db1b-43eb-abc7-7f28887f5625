# 省市区组件库 - sk-xzqh 使用文档

## 组件介绍

`sk-xzqh` 是一个用于选择中国省市区数据的 Vue 组件库。它提供了两种实现方式：

1. **级联选择器 (Cascader)**: 基于 Element Plus 的 `el-cascader` 实现，适用于层级选择。
2. **下拉选择器 (Select)**: 基于 Element Plus 的 `el-select-v2` 实现，适用于单个或多个下拉选择框联动。

两种实现方式共享同一个行政区划数据源，并提供了一致的配置选项。您可以通过 `type` 属性来选择使用哪种实现方式。

## 安装方式

### 局部引入

在需要使用组件的 `.vue` 文件中局部引入：

```vue
<script setup lang="ts">
import { ref } from 'vue';
// 从组件库中引入组件
import SkXzqh from '@/components/sk-xzqh'; // 根据您的项目结构调整路径

const value = ref<string | string[]>(''); // 组件绑定的值
</script>

<template>
  <!-- 使用级联选择器 -->
  <sk-xzqh v-model="value" type="cascader" />

  <!-- 使用下拉选择器 -->
  <sk-xzqh v-model="value" type="select-v2" />
</template>
```

## 组件使用

### 基本用法

```vue
<template>
  <!-- 基本用法 (默认 type="cascader", level=3, valueType="code") -->
  <sk-xzqh v-model="value" />

  <!-- 使用下拉选择器，选择到市级，返回名称 -->
  <sk-xzqh v-model="cityValue" type="select-v2" :level="2" value-type="name" />

  <!-- 使用级联选择器，只选择到省级，返回代码-名称 -->
  <sk-xzqh
    v-model="provinceCodeName"
    type="cascader"
    :level="1"
    value-type="code-name"
  />
</template>

<script setup lang="ts">
import { ref } from 'vue';
import SkXzqh from '@/components/sk-xzqh';

const value = ref<string | string[]>('');
const cityValue = ref<string[]>([]);
const provinceCodeName = ref<string>('');
</script>
```

## 属性说明

| 属性名 | 说明 | 类型 | 可选值 | 默认值 |
| --- | --- | --- | --- | --- |
| `type` | 组件实现方式 | `'cascader' \| 'select-v2'` | - | `'cascader'` |
| `v-model` | 双向绑定的选中值 | `string \| string[]` | - | `''` |
| `level` | 层级深度 | `1 \| 2 \| 3` | - | `3` |
| `valueType` | 返回值类型 | `'code' \| 'name' \| 'code-name'` | - | `'code'` |
| `size` | 组件大小 | `'large' \| 'default' \| 'small'` | - | `'default'` |
| `placeholders` | 占位符文本数组，根据 `level` 自动使用前 `level` 个占位符 | `string[]` | - | `['请选择省份', '请选择城市', '请选择区县']` |
| `disabled` | 是否禁用 | `boolean` | - | `false` |
| `clearable` | 是否可清空 | `boolean` | - | `true` |
| `multiple` | 是否多选 | `boolean` | - | `false` |

## 事件说明

| 事件名   | 说明                 | 回调参数                      |
| -------- | -------------------- | ----------------------------- |
| `change` | 选中值发生变化时触发 | `(value: string \| string[])` |

## 方法说明

通过 ref 可以调用以下方法：

| 方法名 | 说明 | 参数 | 返回值 |
| --- | --- | --- | --- |
| `getSelectData` | 获取选中数据的详细信息 | - | `XzqhData` |
| `xzqhData` | 获取原始区划数据 | - | `XzqhDataOption[]` |
| `element` | 获取组件实例 | - | `CascaderInstance \| undefined` |

使用示例：

```vue
<template>
  <sk-xzqh ref="xzqhRef" v-model="value" type="cascader" />
</template>

<script setup lang="ts">
import { ref } from 'vue';
import SkXzqh from '@/components/sk-xzqh';

const xzqhRef = ref();
const value = ref('');

// 获取选中数据
const getSelectedData = () => {
  const data = xzqhRef.value?.getSelectData();
  console.log(data);
};

// 获取原始数据
const getXzqhData = () => {
  const data = xzqhRef.value?.xzqhData;
  console.log(data);
};

// 获取组件实例
const getElement = () => {
  const element = xzqhRef.value?.element;
  console.log(element);
};
</script>
```

## 返回值说明

返回值类型和格式取决于 `type`、`valueType` 属性以及 `level` 设置。

### Cascader 类型返回值 (`string | string[]`)

- 当 `level` 为 1 时，返回值是单个字符串。
- 当 `level` 大于 1 时，返回值是字符串数组，包含从根级到选中级别的所有值。
- 具体返回内容取决于 `valueType`:
  - `valueType="code"`（默认）：返回行政区划代码。
    ```typescript
    // level = 1: '110000'
    // level = 2: ['110000', '110100']
    // level = 3: ['110000', '110100', '110101']
    ```
  - `valueType="name"`：返回行政区划名称。
    ```typescript
    // level = 1: '北京市'
    // level = 2: ['北京市', '市辖区']
    // level = 3: ['北京市', '市辖区', '东城区']
    ```
  - `valueType="code-name"`：返回代码和名称的组合。
    ```typescript
    // level = 1: '110000-北京市'
    // level = 2: ['110000-北京市', '110100-市辖区']
    // level = 3: ['110000-北京市', '110100-市辖区', '110101-东城区']
    ```

### Select 类型返回值 (`string[]`)

- 返回值类型始终为 `string[]`。
- 数组的长度取决于 `level` 设置。如果某个级别未选择，则对应位置为 `''`。
- 具体返回内容取决于 `valueType`:
  - `valueType="code"`（默认）：返回各级选中的行政区划代码数组。
    ```typescript
    // level = 1: ['110000']
    // level = 2: ['110000', '110100']
    // level = 3: ['110000', '110100', '110101']
    ```
  - `valueType="name"`：返回各级选中的行政区划名称数组。
    ```typescript
    // level = 1: ['北京市']
    // level = 2: ['北京市', '市辖区']
    // level = 3: ['北京市', '市辖区', '东城区']
    ```
  - `valueType="code-name"`：返回各级选中的行政区划代码和名称组合数组。
    ```typescript
    // level = 1: ['110000-北京市']
    // level = 2: ['110000-北京市', '110100-市辖区']
    // level = 3: ['110000-北京市', '110100-市辖区', '110101-东城区']
    ```

## 注意事项

1. **数据获取**: 组件会自动调用 `listXzqhOption({})` 方法获取行政区划数据。请确保此方法可用并返回正确格式的数据。
2. **Select 联动**: 当 `type="select-v2"` 且 `level` 大于 1 时，会显示多个独立的选择器，它们之间存在联动关系。选择上级后才能选择下级，上级变化时下级会被清空。
3. **占位符**: `placeholders` 属性默认值为 `['请选择省份', '请选择城市', '请选择区县']`，会根据 `level` 自动截取使用。您也可以手动传入数组来自定义各级占位符。
4. **组件宽度**: 组件根元素默认 `display: inline-block; width: 100%;`，通常需要通过父容器来控制其宽度。当使用 `type="select-v2"` 时，内部的多个选择器会平分父容器宽度。

## 示例代码（完整）

```vue
<template>
  <div>
    <h2>省市区组件示例</h2>

    <div style="width: 300px; margin-bottom: 20px;">
      <!-- 基本用法 (默认 type="cascader", level=3, valueType="code") -->
      <p>基本用法 (级联选择器):</p>
      <sk-xzqh v-model="value1" />
      <p>选中值: {{ value1 }}</p>
    </div>

    <div style="width: 300px; margin-bottom: 20px;">
      <!-- 使用下拉选择器，选择到市级，返回名称 -->
      <p>下拉选择器 (到市级，返回名称):</p>
      <sk-xzqh
        v-model="value2"
        type="select-v2"
        :level="2"
        value-type="name"
        :placeholders="['省', '市']"
      />
      <p>选中值: {{ value2 }}</p>
    </div>

    <div style="width: 300px; margin-bottom: 20px;">
      <!-- 使用级联选择器，只选择到省级，返回代码-名称 -->
      <p>级联选择器 (到省级，返回代码-名称):</p>
      <sk-xzqh
        v-model="value3"
        type="cascader"
        :level="1"
        value-type="code-name"
        :placeholders="['请选择省']"
      />
      <p>选中值: {{ value3 }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import SkXzqh from '@/components/sk-xzqh';

const value1 = ref<string | string[]>(''); // 默认配置
const value2 = ref<string[]>([]); // select-v2, level=2, name
const value3 = ref<string>(''); // cascader, level=1, code-name
</script>

<style scoped>
/* 示例页面样式，控制组件容器宽度 */
div {
  margin-bottom: 20px;
  border: 1px solid #eee;
  padding: 15px;
}
p {
  margin-top: 5px;
  margin-bottom: 10px;
  font-size: 14px;
  color: #666;
}
</style>
```
