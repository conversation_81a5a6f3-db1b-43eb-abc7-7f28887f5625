package ${packageName}.domain.vo.${lowerClassName};

import lombok.Data;
import java.io.Serial;
import java.io.Serializable;
#foreach ($column in $columns)
    #if($column.javaType == 'LocalDateTime')
import java.time.LocalDateTime;
    #end
#end
#if($table.sub)
import java.util.List;
import com.mybatisflex.annotation.RelationOneToMany;
import ${packageName}.domain.po.${subClassName};
#end

/**
 * ${functionName}视图对象 ${tableName}
 *
 * <AUTHOR>
 * @since ${datetime}
 */
@Data
public class ${ClassName}DetailVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

#foreach ($column in $columns)
#if($column.list || $column.edit)
#set($parentheseIndex=$column.columnComment.indexOf("（"))
#if($parentheseIndex != -1)
#set($comment=$column.columnComment.substring(0, $parentheseIndex))
#else
#set($comment=$column.columnComment)
#end
    /** $column.columnComment */
    private $column.javaType $column.javaField;
#end
#end

#if($table.sub)
    /** $table.subTable.functionName信息 */
    @RelationOneToMany(selfField = "${pkColumn.javaField}", targetField = "${subTableFkclassName}")
    private List<${subClassName}> ${subclassName}List;
#end

}
