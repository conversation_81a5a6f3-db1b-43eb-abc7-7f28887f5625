# 项目相关配置
ruoyi:
  # 名称
  name: Ruoyi-Flex
  # 版本
  version: ${revision}
  # 版权年份
  copyrightYear: 2023 ~ 2024
  # 实例演示开关
  demoEnabled: true
  # 文件路径 示例（ Windows配置D:/ruoyi/uploadPath，Linux配置 /home/<USER>/uploadPath）
  profile: D:/ruoyi/uploadPath
  # 获取ip地址开关
  addressEnabled: false

captcha:
  enable: true
  # 页面 <参数设置> 可开启关闭 验证码校验
  # 验证码类型 math 数组计算 char 字符验证
  type: MATH
  # line 线段干扰 circle 圆圈干扰 shear 扭曲干扰
  category: CIRCLE
  # 数字验证码位数
  numberLength: 1
  # 字符验证码长度
  charLength: 4

# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 8080
  servlet:
    # 应用的访问路径
    context-path: /

  # tomcat web容器配置
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100
    accept-count: 1000

  # undertow web容器配置
#  undertow:
#      # HTTP post内容的最大大小。当值为-1时，默认值为大小是无限的
#      max-http-post-size: -1
#      # 以下的配置会影响buffer,这些buffer会用于服务器连接的IO操作,有点类似netty的池化内存管理
#      # 每块buffer的空间大小,越小的空间被利用越充分
#      buffer-size: 512
#      # 是否分配的直接内存
#      direct-buffers: true
#      threads:
#        # 设置IO线程数, 它主要执行非阻塞的任务,它们会负责多个连接, 默认设置每个CPU核心一个线程
#        io: 8
#        # 阻塞任务线程池, 当执行类似servlet请求阻塞操作, undertow会从这个线程池中取得线程,它的值设置取决于系统的负载
#        worker: 256

# 日志配置
logging:
  level:
    com.ruoyi: @logging.level@
    org.springframework: warn
    org.mybatis.spring.mapper: error
  config: classpath:logback.xml

# 用户配置
user:
  password:
    # 密码最大错误次数
    maxRetryCount: 5
    # 密码锁定时间（默认10分钟）
    lockTime: 10

# Spring配置
spring:
  application:
    name: ${ruoyi.name}
  threads:
    # 启用JAVA21虚拟线程
    virtual:
      enabled: true
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  profiles:
    active: @profiles.active@
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 10MB
      # 设置总上传的文件大小
      max-request-size: 20MB
  mvc:
    format:
      date-time: yyyy-MM-dd HH:mm:ss
  jackson:
    # 日期格式化
    date-format: yyyy-MM-dd HH:mm:ss
    serialization:
      # 格式化输出
      indent_output: false
      # 忽略无法转换的对象
      fail_on_empty_beans: false
    deserialization:
      # 允许对象忽略json中不存在的属性
      fail_on_unknown_properties: false
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: true

# PageHelper分页插件
pagehelper:
  #helperDialect: mysql、postgresql pagehelper分页插件会自动检测当前的数据库链接，自动选择合适的分页方式。
  supportMethodsArguments: true
  params: count=countSql

# MyBatisFlex公共配置
# https://mybatis-flex.com/zh/base/configuration.html
mybatis-flex:
  # 搜索指定包别名
  type-aliases-package: com.ruoyi.**.domain
  # 多包名使用 例如 org.dromara.**.mapper,org.xxx.**.mapper
  mapper-package: com.ruoyi.**.mapper
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapper-locations: classpath*:mapper/**/*Mapper.xml
  configuration:
    ## 以下为mybatis原生配置 https://mybatis.org/mybatis-3/zh/configuration.html
    # 自动驼峰命名规则（camel case）映射
    map_underscore_to_camel_case: true
    # MyBatis 自动映射策略
    # NONE：不启用 PARTIAL：只对非嵌套 resultMap 自动映射 FULL：对所有 resultMap 自动映射
    auto_mapping_behavior: FULL
    # MyBatis 自动映射时未知列或未知属性处理策
    # NONE：不做处理 WARNING：打印相关警告 FAILING：抛出异常和详细信息
    auto_mapping_unknown_column_behavior: NONE
    # 更详细的日志输出 会有性能损耗 org.apache.ibatis.logging.stdout.StdOutImpl
    # 关闭日志记录 org.apache.ibatis.logging.nologging.NoLoggingImpl
    # 默认日志输出 org.apache.ibatis.logging.slf4j.Slf4jImpl
    #log_impl: org.apache.ibatis.logging.stdout.StdOutImpl
    logImpl: org.apache.ibatis.logging.nologging.NoLoggingImpl
    cacheEnabled: true
  global-config:
    # 是否控制台打印 MyBatis-Flex 的 LOGO 及版本号
    print-banner: true
    # 全局的 ID 生成策略配置:雪花算法
    key-config:
      key-type: Generator
      value: snowFlakeId
    # 逻辑未删除值
    normal-value-of-logic-delete: 1
    # 逻辑已删除值(框架表均使用此值 禁止随意修改)
    deleted-value-of-logic-delete: 0
    # 默认的逻辑删除字段
    logic-delete-column: jlzt
    # 默认的多租户字段
    tenant-column: tenant_id
    # 默认的乐观锁字段
    # version-column: version

# 数据加密
mybatis-encryptor:
  # 是否开启加密
  enable: false
  # 默认加密算法
  algorithm: BASE64
  # 编码方式 BASE64/HEX。默认BASE64
  encode: BASE64
  # 安全秘钥 对称算法的秘钥 如：AES，SM4
  password:
  # 公私钥 非对称算法的公私钥 如：SM2，RSA
  publicKey:
  privateKey:

# api接口加密
api-decrypt:
  # 是否开启全局接口加密
  enabled: true
  # SM4 加密头标识
  headerFlag: encrypt-key
  # 响应加密公钥 非对称算法的公私钥 如：SM2，RSA 使用者请自行更换,此版本使用SM2
  # 对应前端解密私钥 SM2: cb8d30fcee43dd35b53fe4b68cb8d19e6e6b2d2ef73eeb2a795b28d1e40dcf10
  publicKey: 04bd42eb7a85b73036b6a4ab1c8db79a92ec326fc6c9fb8e97efee1a57f02ece1853aaf7e10d9617a2a7b7fd5092f69912024cdf81ba16ccef6b3981bc42eb3c52
  # 请求解密私钥 非对称算法的公私钥 如：SM2，RSA 使用者请自行更换
  # 对应前端加密公钥 SM2: 049eee13e2add61597243c14f1ae6411856ef02a4e2c51ee37d0475918398c196c956acede43b682ce0af199c65443e3786f96a1b403842bfaab973e9c77f6546f
  privateKey: 360e8793892bb18fd1cb6ac78c386dad072767f1fe5c723e3d186e8352ffc479

# SpringDoc配置
springdoc:
  #需要扫描的包,可以配置多个，使用逗号分割
  packages-to-scan: com.ruoyi
  paths-to-exclude: #配置不包含在swagger文档中的api
    - /api/test/**
    - /api/mockito/data
  swagger-ui:
    enabled: true  #开启/禁止swagger,prod可以设置为false
    version: 5.10.3  #指定swagger-ui的版本号
    disable-swagger-default-url: true  #禁用default petstore url
    path: /swagger-ui.html  #swagger页面
    persistAuthorization: true  # 持久化认证数据，如果设置为 true，它会保留授权数据并且不会在浏览器关闭/刷新时丢失
    csrf:
      enabled: true # 启用CSRF支持
  api-docs:
    enabled: true #开启/禁止api-docs, prod可以设置为false
  use-management-port: false
  enable-spring-security: true
  info:
    # 标题
    title: 'Ruoyi-Flex API Doc'
    # 描述
    description: 'Ruoyi-Flex SrpingDoc demo'
    # 版本
    version: '版本号: ${ruoyi.version}'
    # 作者信息
    contact:
      name: 数据小王子
      email: <EMAIL>
      url: https://gitee.com/dataprince/ruoyi-flex
  components:
    # 鉴权方式配置
    security-schemes:
      apiKey:
        type: APIKEY
        in: HEADER
        name: ${sa-token.token-name}
  group-configs:
    - group: 1.演示模块
      packages-to-scan: com.ruoyi.demo
    - group: 2.通用模块
      packages-to-scan: com.ruoyi.common
    - group: 3.系统模块
      packages-to-scan: com.ruoyi.system
    - group: 4.代码生成模块
      packages-to-scan: com.ruoyi.generator
    - group: 5.设备管理模块
      packages-to-scan: com.ruoyi.device

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*,/demo/*,/device/*

# 分布式锁 lock4j 全局配置
lock4j:
  # 获取分布式锁超时时间，默认为 3000 毫秒
  acquire-timeout: 3000
  # 分布式锁的超时时间，默认为 30 秒
  expire: 30000

# Sa-Token配置
sa-token:
  # token名称 (同时也是cookie名称)
  token-name: Authorization
  # token固定超时 设为七天 (必定过期) 单位: 秒
  timeout: 604800
  # 多端不同 token 有效期 可查看 LoginHelper.loginByDevice 方法自定义
  # token最低活跃时间 (指定时间无操作就过期) 单位: 秒
  active-timeout: 1800
  # 是否允许同一账号并发登录 (为true时允许一起登录, 为false时新登录挤掉旧登录)
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个token (为true时所有登录共用一个token, 为false时每次登录新建一个token)
  is-share: false
  # token 风格（默认可取值：uuid、simple-uuid、random-32、random-64、random-128、tik）
  token-style: uuid
  # 是否输出操作日志
  is-log: true
  # jwt秘钥
  jwt-secret-key: abcdefghijklmnopqrstuvwxyz

# security配置
security:
  # 排除路径
  excludes:
    # 静态资源
    - /*.html
    - /**/*.html
    - /**/*.css
    - /**/*.js
    - /profile/**
    # 公共路径
    - /favicon.ico
    - /error
    # swagger 文档配置
    - /*/api-docs
    - /*/api-docs/**
    # actuator 监控配置
    - /actuator
    - /actuator/**
    # 其它链接
    - /login
    - /register
    - /captchaImage
    - /captcha/get
    - /captcha/check
    - /warm-flow-ui/**
    - /warm-flow/**
# 多租户配置
tenant:
  # 是否开启
  enable: true
  # 排除表
  excludes:
    - sys_menu
    - sys_tenant
    - sys_tenant_package
    - sys_role_dept
    - sys_role_menu
    - sys_user_post
    - sys_user_role
    - sys_client
    - sys_oss_config

--- # Actuator 监控端点的配置项
management:
  endpoints:
    web:
      exposure:
        include: '*'
  endpoint:
    health:
      show-details: ALWAYS
    logfile:
      external-file: ./logs/sys-console.log

--- # 默认/推荐使用sse推送
sse:
  enabled: true
  path: /resource/sse

--- # websocket
websocket:
  enabled: false
  # 路径
  path: /resource/websocket
  # 设置访问源地址
  allowedOrigins: '*'

