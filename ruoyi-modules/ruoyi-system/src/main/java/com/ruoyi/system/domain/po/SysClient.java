package com.ruoyi.system.domain.po;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Table;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 系统授权对象 sys_client
 *
 * <AUTHOR>
 * @since 2025-05-10 10:07:06
 */
@Data
@Table(value = "sys_client")
public class SysClient implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 主建 */
    @Id
    private String id;

    /** 客户端id */
    private String clientId;

    /** 客户端key */
    private String clientKey;

    /** 客户端秘钥 */
    private String clientSecret;

    /** 授权类型 */
    private String grantType;

    /** 设备类型 */
    private String deviceType;

    /** token活跃超时时间 */
    private Integer activeTimeout;

    /** token固定超时 */
    private Integer timeout;

    /** 状态（0正常 1停用） */
    private String status;

    /** 删除标志（1代表存在 0代表删除） */
    @Column(isLogicDelete = true)
    private Integer jlzt;

    /**
     * 乐观锁
     */
    @Column(version = true)
    private Integer version;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" )
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" )
    private LocalDateTime updateTime;


}
