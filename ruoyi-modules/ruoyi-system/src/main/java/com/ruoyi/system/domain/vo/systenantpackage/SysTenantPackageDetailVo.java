package com.ruoyi.system.domain.vo.systenantpackage;

import lombok.Data;
import java.io.Serial;
import java.io.Serializable;

/**
 * 租户套餐视图对象 sys_tenant_package
 *
 * <AUTHOR>
 * @since 2025-04-27 21:19:08
 */
@Data
public class SysTenantPackageDetailVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 租户套餐id */
    private String id;
    /** 套餐名称 */
    private String packageName;
    /** 关联菜单id */
    private String menuIds;
    /** 备注 */
    private String remark;
    /** 菜单树选择项是否关联显示 */
    private Boolean menuCheckStrictly;
    /** 状态（0正常 1停用） */
    private String status;
    /** 乐观锁 */
    private Integer version;


}
