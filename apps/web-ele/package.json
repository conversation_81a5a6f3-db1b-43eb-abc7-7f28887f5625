{"name": "@vben/web-ele", "version": "5.5.7", "homepage": "https://vben.pro", "bugs": "https://github.com/vbenjs/vue-vben-admin/issues", "repository": {"type": "git", "url": "git+https://github.com/vbenjs/vue-vben-admin.git", "directory": "apps/web-ele"}, "license": "MIT", "author": {"name": "vben", "email": "<EMAIL>", "url": "https://github.com/anncwb"}, "type": "module", "scripts": {"build": "pnpm vite build --mode production", "build:analyze": "pnpm vite build --mode analyze", "dev": "pnpm vite --mode development", "preview": "vite preview", "typecheck": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>"}, "imports": {"#/*": "./src/*"}, "dependencies": {"@vben-core/menu-ui": "workspace:*", "@vben-core/shadcn-ui": "workspace:*", "@vben-core/shared": "workspace:*", "@vben/access": "workspace:*", "@vben/common-ui": "workspace:*", "@vben/constants": "workspace:*", "@vben/hooks": "workspace:*", "@vben/icons": "workspace:*", "@vben/layouts": "workspace:*", "@vben/locales": "workspace:*", "@vben/plugins": "workspace:*", "@vben/preferences": "workspace:*", "@vben/request": "workspace:*", "@vben/stores": "workspace:*", "@vben/styles": "workspace:*", "@vben/types": "workspace:*", "@vben/utils": "workspace:*", "@vueuse/core": "catalog:", "@tinymce/tinymce-vue": "catalog:", "dayjs": "catalog:", "element-plus": "catalog:", "lodash-es": "catalog:", "pinia": "catalog:", "sm-crypto": "catalog:", "vue": "catalog:", "vue-router": "catalog:", "monaco-editor": "^0.52.0", "tinymce": "catalog:"}, "devDependencies": {"@types/lodash-es": "^4.17.12", "@types/sm-crypto": "^0.3.4", "unplugin-auto-import": "^19.1.1", "unplugin-element-plus": "^0.10.0", "unplugin-vue-components": "^28.4.1", "vite-plugin-monaco-editor": "^1.1.0"}}