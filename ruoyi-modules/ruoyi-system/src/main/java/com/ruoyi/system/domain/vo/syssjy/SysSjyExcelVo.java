package com.ruoyi.system.domain.vo.syssjy;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 数据元管理视图对象 sys_sjy
 *
 * <AUTHOR>
 * @date 2025-01-03 17:31:57
 */
@Data
@ExcelIgnoreUnannotated
public class SysSjyExcelVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键编号
     */
    @ExcelProperty(value = "主键编号")
    private String id;

    /**
     * 删除标志（1代表存在 0代表删除）
     */
    @ExcelProperty(value = "删除标志（1代表存在 0代表删除）")
    private Integer jlzt;

    /**
     * 租户号
     */
    @ExcelProperty(value = "租户号")
    private String tenantId;

    /**
     * 列名称
     */
    @ExcelProperty(value = "列名称")
    private String columnName;

    /**
     * JAVA字段名
     */
    @ExcelProperty(value = "JAVA字段名")
    private String javaField;

    /**
     * 列描述
     */
    @ExcelProperty(value = "列描述")
    private String columnComment;

    /**
     * 乐观锁
     */
    @ExcelProperty(value = "乐观锁")
    private Integer version;

    /**
     * 创建者
     */
    @ExcelProperty(value = "创建者")
    private String createBy;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新者
     */
    @ExcelProperty(value = "更新者")
    private String updateBy;

    /**
     * 更新时间
     */
    @ExcelProperty(value = "更新时间")
    private Date updateTime;


}
