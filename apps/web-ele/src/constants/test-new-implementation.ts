// 测试新的 withNoFullWidth 实现
import { BaseSchema } from './zod-enum';

console.log('=== 测试新的 withNoFullWidth 实现 ===');

// 1. 测试 PHONE 字段
console.log('\n1. PHONE 字段测试:');

// 1.1 全角数字
const phoneFullWidth = BaseSchema.PHONE.safeParse('１３８１２３４５６７８');
console.log('PHONE 全角数字结果:', phoneFullWidth);
if (!phoneFullWidth.success) {
  console.log('PHONE 全角数字错误信息:', phoneFullWidth.error.issues[0].message);
  console.log('期望: "不能包含全角字符，请使用半角字符"');
}

// 1.2 半角但格式错误
const phoneFormat = BaseSchema.PHONE.safeParse('12345678901');
console.log('PHONE 格式错误结果:', phoneFormat);
if (!phoneFormat.success) {
  console.log('PHONE 格式错误信息:', phoneFormat.error.issues[0].message);
  console.log('期望: "请输入正确的手机号"');
}

// 1.3 正确的手机号
const phoneCorrect = BaseSchema.PHONE.safeParse('13812345678');
console.log('PHONE 正确结果:', phoneCorrect);

// 2. 测试 GAJGJGDM 字段
console.log('\n2. GAJGJGDM 字段测试:');

// 2.1 全角数字
const gajgjgdmFullWidth = BaseSchema.GAJGJGDM.safeParse('１１１１１１１１１１１１');
console.log('GAJGJGDM 全角数字结果:', gajgjgdmFullWidth);
if (!gajgjgdmFullWidth.success) {
  console.log('GAJGJGDM 全角数字错误信息:', gajgjgdmFullWidth.error.issues[0].message);
  console.log('期望: "不能包含全角字符，请使用半角字符"');
}

// 2.2 半角但长度错误
const gajgjgdmLength = BaseSchema.GAJGJGDM.safeParse('12345');
console.log('GAJGJGDM 长度错误结果:', gajgjgdmLength);
if (!gajgjgdmLength.success) {
  console.log('GAJGJGDM 长度错误信息:', gajgjgdmLength.error.issues[0].message);
  console.log('期望: "请输入12位数字"');
}

// 2.3 半角但格式错误
const gajgjgdmFormat = BaseSchema.GAJGJGDM.safeParse('abcdefghijkl');
console.log('GAJGJGDM 格式错误结果:', gajgjgdmFormat);
if (!gajgjgdmFormat.success) {
  console.log('GAJGJGDM 格式错误信息:', gajgjgdmFormat.error.issues[0].message);
  console.log('期望: "请输入12位数字"');
}

// 2.4 正确的GAJGJGDM
const gajgjgdmCorrect = BaseSchema.GAJGJGDM.safeParse('123456789012');
console.log('GAJGJGDM 正确结果:', gajgjgdmCorrect);

// 3. 测试 EMAIL 字段
console.log('\n3. EMAIL 字段测试:');

// 3.1 全角字符
const emailFullWidth = BaseSchema.EMAIL.safeParse('测试@example.com');
console.log('EMAIL 全角字符结果:', emailFullWidth);
if (!emailFullWidth.success) {
  console.log('EMAIL 全角字符错误信息:', emailFullWidth.error.issues[0].message);
  console.log('期望: "不能包含全角字符，请使用半角字符"');
}

// 3.2 半角但格式错误
const emailFormat = BaseSchema.EMAIL.safeParse('invalid-email');
console.log('EMAIL 格式错误结果:', emailFormat);
if (!emailFormat.success) {
  console.log('EMAIL 格式错误信息:', emailFormat.error.issues[0].message);
  console.log('期望: "请输入正确的邮箱"');
}

// 3.3 正确的邮箱
const emailCorrect = BaseSchema.EMAIL.safeParse('<EMAIL>');
console.log('EMAIL 正确结果:', emailCorrect);

// 4. 测试 STRING 字段
console.log('\n4. STRING 字段测试:');

// 4.1 全角字符
const stringFullWidth = BaseSchema.STRING.safeParse('你好世界');
console.log('STRING 全角字符结果:', stringFullWidth);
if (!stringFullWidth.success) {
  console.log('STRING 全角字符错误信息:', stringFullWidth.error.issues[0].message);
  console.log('期望: "不能包含全角字符，请使用半角字符"');
}

// 4.2 半角字符
const stringHalfWidth = BaseSchema.STRING.safeParse('hello world');
console.log('STRING 半角字符结果:', stringHalfWidth);

// 5. 测试各种全角字符类型
console.log('\n5. 各种全角字符类型测试:');

const fullWidthTypes = [
  { name: '全角数字', input: '１２３４５' },
  { name: '中文字符', input: '你好世界' },
  { name: '全角字母', input: 'ａｂｃｄｅ' },
  { name: '全角符号', input: '！＠＃＄％' },
  { name: '全角空格', input: 'hello　world' },
  { name: '日文平假名', input: 'ひらがな' },
  { name: '日文片假名', input: 'カタカナ' },
];

fullWidthTypes.forEach(test => {
  const result = BaseSchema.STRING.safeParse(test.input);
  console.log(`${test.name} "${test.input}":`, result.success ? '通过' : result.error.issues[0].message);
});

export {
  phoneFullWidth,
  phoneFormat,
  gajgjgdmFullWidth,
  gajgjgdmLength,
  emailFullWidth,
  emailFormat,
  stringFullWidth
};
