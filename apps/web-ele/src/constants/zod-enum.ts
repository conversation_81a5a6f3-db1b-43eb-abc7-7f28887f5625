import { z } from '#/adapter/form';

// 基础验证规则（避免循环引用）
export const BaseSchema = {
  STRING: z.string(),
  EMAIL: z.string().email('请输入正确的邮箱'),
  PHONE: z.string().regex(/^1[3-9]\d{9}$/, '请输入正确的手机号'),
  GAJGJGDM: z
    .string()
    .length(12)
    .regex(/^\d{12}$/, '请输入12位数字'),
};

// 可选值包装器（函数形式）
export const optional = <T extends z.ZodTypeAny>(schema: T) =>
  schema.optional().or(z.literal(''));

// 枚举式 Zod 模式集合
export const ZodEnum = {
  STRING: BaseSchema.STRING,
  EMAIL: BaseSchema.EMAIL,
  EMAIL_EMPTY: optional(BaseSchema.EMAIL),
  PHONE: BaseSchema.PHONE,
  PHONE_EMPTY: optional(BaseSchema.PHONE),
  /**
   *公安机关机构代码
   */
  GAJGJGDM: BaseSchema.GAJGJGDM,
  /**
   *公安机关机构代码，可为空
   */
  GAJGJGDM_EMPTY: optional(BaseSchema.GAJGJGDM),
} as const;

export type ZodEnumKey = keyof typeof ZodEnum;
