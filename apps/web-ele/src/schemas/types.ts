/**
 * Schema 相关的类型定义
 */

import { z } from '#/adapter/form';

/**
 * 表单验证 Schema 类型
 */
export type FormValidationSchema<T = any> = z.ZodSchema<T>;

/**
 * 验证规则类型
 */
export interface ValidationRule {
  /** 验证器函数 */
  validator: (rule: any, value: any, callback: Function) => void;
  /** 触发时机 */
  trigger?: 'blur' | 'change' | 'submit';
  /** 是否必填 */
  required?: boolean;
  /** 错误信息 */
  message?: string;
}

/**
 * 基础 Schema 类型
 */
export type BaseSchemaType = {
  EMAIL: z.ZodEffects<z.ZodString, string, string>;
  GAJGJGDM: z.ZodEffects<z.ZodString, string, string>;
  ID_CARD: z.ZodEffects<z.ZodString, string, string>;
  PHONE: z.ZodEffects<z.ZodString, string, string>;
  STRING: z.ZodEffects<z.ZodString, string, string>;
};

/**
 * Zod 枚举类型
 */
export type ZodEnumType = BaseSchemaType & {
  EMAIL_EMPTY: z.ZodUnion<
    [z.ZodLiteral<''>, z.ZodEffects<z.ZodString, string, string>]
  >;
  GAJGJGDM_EMPTY: z.ZodUnion<
    [z.ZodLiteral<''>, z.ZodEffects<z.ZodString, string, string>]
  >;
  ID_CARD_EMPTY: z.ZodUnion<
    [z.ZodLiteral<''>, z.ZodEffects<z.ZodString, string, string>]
  >;
  PHONE_EMPTY: z.ZodUnion<
    [z.ZodLiteral<''>, z.ZodEffects<z.ZodString, string, string>]
  >;
};

/**
 * 全角字符包装器类型
 */
export type WithNoFullWidthWrapper = <T extends z.ZodTypeAny>(
  schema: T,
  message?: string,
) => z.ZodEffects<z.ZodString, string, string>;
