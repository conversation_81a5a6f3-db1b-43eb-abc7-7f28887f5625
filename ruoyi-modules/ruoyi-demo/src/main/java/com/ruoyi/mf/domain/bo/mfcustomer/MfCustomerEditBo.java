package com.ruoyi.mf.domain.bo.mfcustomer;

import com.ruoyi.mf.domain.po.MfCustomer;
import com.ruoyi.mf.domain.po.MfGoods;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import jakarta.validation.constraints.*;
import java.util.List;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serial;
import java.io.Serializable;

/**
 * 客户主表业务对象 mf_customer
 *
 * <AUTHOR>
 * @since 2025-03-17 17:46:47
 */
@Data
@AutoMapper(target = MfCustomer.class, reverseConvertGenerate = false)
public class MfCustomerEditBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 客户id
     */
    @NotBlank(message = "客户id不能为空")
    private String id;
    /**
     * 客户姓名
     */
    @NotBlank(message = "客户姓名不能为空")
    private String customerName;
    /**
     * 手机号码
     */
    @NotBlank(message = "手机号码不能为空")
    private String phonenumber;
    /**
     * 客户性别
     */
    @NotBlank(message = "客户性别不能为空")
    private String gender;
    /**
     * 客户生日
     */
    @NotNull(message = "客户生日不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date birthday;
    /**
     * 客户描述
     */
    @NotBlank(message = "客户描述不能为空")
    private String remark;
    /**
     * 乐观锁
     */
    private Integer version;

    /** 商品子信息 */
    private List<MfGoods> mfGoodsList;
}
