package com.ruoyi.system.domain.bo.sysuser;

import com.ruoyi.common.core.core.domain.bo.BaseListBo;
import com.ruoyi.system.domain.po.SysUser;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

/**
 * 用户信息业务对象 sys_user
 *
 * <AUTHOR>
 * @since 2025-04-29 14:00:02
 */
@Data
@AutoMapper(target = SysUser.class, reverseConvertGenerate = false)
public class SysUserRoleListBo extends BaseListBo {

    /**
     * 用户账号
     */
    private String userName;

    /**
     * 手机号码
     */
    private String phonenumber;

    /**
     * 数据权限 当前角色ID
     */
    private String roleId;


}
