

<template>
  <div class="table-box">
    <SkTable
      ref="skTable"
      row-key="${treeCode}"
      :columns="columns"
      :request-api="listDefinition"
      :search-col="{ xs: 1, sm: 1, md: 2, lg: 3, xl: 3 }"
    >
      <!-- 表格 header 按钮 -->
      <template #tableHeader>

        <el-button v-hasPermi="['ruoyi.flow:definition:add']" type="primary" icon="CirclePlus" @click="openDialog('新增')">
          新增
        </el-button>
        <el-button v-hasPermi="['ruoyi.flow:definition:export']" type="primary" plain icon="Download" @click="handleExport">
          导出
        </el-button>
      </template>
      <!-- 表格操作 -->
      <template #operation="scope">
        <div>
          <el-button v-hasPermi="['ruoyi.flow:definition']" type="primary" link icon="CirclePlus" @click="handleDesign('设计流程', scope.row)">
            设计流程
          </el-button>
          <el-button v-hasPermi="['ruoyi.flow:publish']" v-if="scope.row.isPublish === 0" type="primary" link icon="CirclePlus" @click="pubLish(scope.row.id)">
            流程发布
          </el-button>
          <el-button v-hasPermi="['ruoyi.flow:unpublish']" v-if="scope.row.isPublish === 1" type="primary" link icon="CirclePlus" @click="unPubLish(scope.row.id)">
            取消发布
          </el-button>
          <el-button  v-if="scope.row.activityStatus === 0" type="primary" link icon="CirclePlus" @click="active(scope.row.id)">
            流程激活
          </el-button>
          <el-button v-if="scope.row.activityStatus === 1" type="primary" link icon="CirclePlus" @click="unActive(scope.row.id)">
            流程挂起
          </el-button>
          <el-button v-hasPermi="['ruoyi.flow:definition:edit']" type="primary" link icon="EditPen" @click="openDialog('编辑', scope.row)">
            编辑
          </el-button>
          <el-button v-hasPermi="['ruoyi.flow:definition']" type="danger" link icon="Delete" @click="handleDelete(scope.row)">
            删除
          </el-button>
        </div>
      </template>
    </SkTable>

    
  </div>
</template>

<script setup name="Definition" lang="ts">

const { skTable, columns, openDialog, handleExport, handleDelete,handleDesign,pubLish ,unPubLish,active,unActive} = useDefinition();
import { listDefinition } from '@/api/flow/definition';
import { useDefinition } from '@/views/flow/definition/utils';


//const url = ref(import.meta.env.VITE_APP_BASE_API+`/warm-flow-ui/index.html?id=1&disabled=true`);
</script>
