import type { ColumnProps, SkTableInstance } from '@/components/SkUi/SkTable/interface';
import { addDialog } from '@/components/SkUi/SkReDialog';
import { h, reactive, ref } from 'vue';
import type { UserVO } from '@/api/system/user/types';
import SelectUser from '@/views/system/role/components/SelectUser.vue';
import { authUserCancel, authUserCancelAll, authUserSelectAll } from '@/api/system/role';
import type { RouteLocationRaw } from 'vue-router';

export function useAuthUser() {
  const skTable = ref<SkTableInstance>();
  const skTableAddUser = ref<SkTableInstance>();
  const addUserRef = ref();
  const route = useRoute();
  const roleId = route.params.roleId as string;
  const addUserColumns = reactive<ColumnProps<UserVO>[]>([
    { type: 'selection', label: '#', width: 55 },
    { type: 'index', label: '#', width: 80 },
    {
      prop: 'userName',
      align: 'center',
      label: '用户名称',
      search: { el: 'input' }
    },
    {
      prop: 'nickName',
      align: 'center',
      label: '用户昵称'
    },
    {
      prop: 'email',
      align: 'center',
      label: '邮箱'
    },
    {
      prop: 'phonenumber',
      align: 'center',
      label: '手机',
      search: { el: 'input' }
    },
    {
      prop: 'status',
      align: 'center',
      label: '状态'
    },
    {
      prop: 'createTime',
      align: 'center',
      label: '创建时间'
    }
  ]);
  /**
   * 表格配置项
   */
  const columns = reactive<ColumnProps<UserVO>[]>([
    { type: 'selection', label: '#', width: 55 },
    { type: 'index', label: '#', width: 80 },
    {
      prop: 'userName',
      align: 'center',
      label: '用户名称',
      search: { el: 'input' }
    },
    {
      prop: 'nickName',
      align: 'center',
      label: '用户昵称'
    },
    {
      prop: 'email',
      align: 'center',
      label: '邮箱'
    },
    {
      prop: 'phonenumber',
      align: 'center',
      label: '手机',
      search: { el: 'input' }
    },
    {
      prop: 'status',
      align: 'center',
      label: '状态'
    },
    {
      prop: 'createTime',
      align: 'center',
      label: '创建时间'
    },
    { prop: 'operation', slot: 'operation', label: '操作', width: 300, fixed: 'right' }
  ]);

  const initParam = reactive({ roleId: roleId });

  /***
   * 编辑，新增,数据交换
   */
  const openDialog = async () => {
    addDialog({
      title: '用户信息',
      width: '1024px',
      contentRenderer: () => h(SelectUser, { ref: addUserRef }),
      beforeSure: async (done) => {
        const ids = addUserRef.value.getUserIds();
        if (ids.length == 0) {
          await window.skynet.alertError('请选择要添加的用户');
          return;
        }
        await authUserSelectAll({ roleId: roleId, userIds: ids })
          .then((res: any) => {
            if (res.code === 200) {
              window.skynet.alert(`添加成功！`);
              done();
              skTable.value?.getTableList();
            }
          })
          .catch(() => {});
      }
    });
  };

  /** 取消授权按钮操作 */
  const cancelAuthUser = async (row: UserVO) => {
    await window.skynet
      .confirm('确认要取消该用户"' + row.userName + '"角色吗？')
      .then(async () => {
        await authUserCancel({ userId: row.id, roleId: roleId });
        await skTable.value?.getTableList();
        window.skynet.alert('取消授权成功');
      })
      .catch(() => {});
  };

  /** 批量取消授权按钮操作 */
  const cancelAuthUserAll = async (uIds: string[]) => {
    await window.skynet
      .confirm('是否取消选中用户授权数据项?')
      .then(async () => {
        await authUserCancelAll({ roleId: roleId, userIds: uIds });
        await skTable.value?.getTableList();
        window.skynet.alert('取消授权成功');
      })
      .catch(() => {});
  };
  // 返回按钮
  const handleClose = () => {
    const obj: RouteLocationRaw = {
      path: '/system/role',
      hash: '',
      query: undefined
    };
    window.skynet.tab.closeOpenPage(obj);
  };
  return {
    skTable,
    skTableAddUser,
    columns,
    addUserColumns,
    initParam,
    openDialog,
    cancelAuthUser,
    cancelAuthUserAll,
    handleClose
  };
}
