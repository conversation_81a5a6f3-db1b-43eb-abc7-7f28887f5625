import type {
  NoticeForm,
  NoticeQuery,
  NoticeVO,
} from '#/api/system/notice/types';

import { requestClient } from '#/api/request';

/**
 * 查询通知公告列表
 * @param query
 */
export const listNotice = (query?: NoticeQuery) => {
  return requestClient.get<PageData<NoticeVO>>('/system/notice/list', query);
};

/**
 * 查询通知公告详细
 * @param id
 */
export const getNotice = (id: number | string) => {
  return requestClient.get<NoticeVO>(`/system/notice/${id}`);
};

/**
 * 新增通知公告
 * @param data
 */
export const addNotice = (data: NoticeForm) => {
  return requestClient.post('/system/notice', data);
};

/**
 * 修改通知公告
 * @param data
 */
export const editNotice = (data: NoticeForm) => {
  return requestClient.put('/system/notice', data);
};

/**
 * 删除通知公告权限
 * @param id
 */
export const removeNotice = (id: Array<number | string> | number | string) => {
  return requestClient.delete(`/system/notice/${id}`);
};
