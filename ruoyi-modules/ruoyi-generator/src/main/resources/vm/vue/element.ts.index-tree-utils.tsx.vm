import type { ColumnProps, SkTableInstance } from '@/components/SkUi/SkTable/interface';
import type { ${BusinessName}DataOption, ${BusinessName}Form, ${BusinessName}VO } from '@/api/${moduleName}/${businessName}/types';
import ${className}Form from '@/views/${moduleName}/${businessName}/components/${ClassName}Form.vue';
import { add${BusinessName}, edit${BusinessName}, get${BusinessName}, list${BusinessName}, list${BusinessName}Option, remove${BusinessName} } from '@/api/${moduleName}/${businessName}';
#if($isDialog || $isDrawer)
import { ${addFormMethod} } from '@/components/SkUi/Sk${formComponent}';
#end
import { h, reactive, ref, type Ref } from 'vue';
import { useValidator } from '@/hooks/skUi/web/useValidator';

/**
 * ${functionName}管理Hook
 * @returns ${functionName}管理相关的状态和方法
 */
export function use${BusinessName}() {
  const skTable = ref<SkTableInstance>();
  const { required } = useValidator();
  const loadMap = new Map<
    string | number,
    {
      /** 当前行数据 */
      row: ${BusinessName}VO;
      /** 树节点信息 */
      treeNode: unknown;
      /** 异步加载完成的回调函数 */
      resolve: (data: ${BusinessName}VO[]) => void;
    }
  >();
  const isExpandAll = ref(false);

  const columns = reactive<ColumnProps<${BusinessName}VO>[]>([
    { type: 'index', label: '#', width: 80 },
#foreach ($column in $columns)
#set($javaField=$column.javaField)
#set($parentheseIndex=$column.columnComment.indexOf("（"))
#if($parentheseIndex != -1)
#set($comment=$column.columnComment.substring(0, $parentheseIndex))
#else
#set($comment=$column.columnComment)
#end
#if($column.list)
    {
      prop: '$column.javaField',
#if($column.query)
      search: {
#if($column.htmlType == "input" || $column.htmlType == "textarea")
        el: 'input'
#elseif($column.htmlType == "select")
        el: 'sk-dict', props: { data: '${column.dictType}' }
#elseif($column.htmlType == "radio-group")
        el: 'sk-dict',
        props: {
          data: '${column.dictType}',
          'type': 'radio-group'
        }
#elseif($column.htmlType == "datetime")
#if($column.queryType == "BETWEEN")
        el: 'date-picker',
        span: 1,
        props: {
          type: 'daterange',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          rangeSeparator: '-',
          startPlaceholder: '开始日期',
          endPlaceholder: '结束日期'
        }
#else
        el: 'date-picker',
        span: 1,
        props: {
          type: 'date',
          valueFormat: 'YYYY-MM-DD',
          placeholder: '请选择${comment}'
        }
#end
#end
      },
#end
      align: 'center',
      label: '$comment'
    },
#end
#end
    { prop: 'operation', slot: 'operation', label: '操作', width: 300, fixed: 'right' }
  ]);

  const rules = reactive<Record<string, any>>({
#foreach ($column in $columns)
#if($column.required && $column.javaField!='tenantId' && $column.javaField!='version' && $column.javaField!='jlzt')
#set($parentheseIndex=$column.columnComment.indexOf("（"))
#if($parentheseIndex != -1)
#set($comment=$column.columnComment.substring(0, $parentheseIndex))
#else
#set($comment=$column.columnComment)
#end
    $column.javaField: [required('$comment')]#if($foreach.count != $columns.size()),#end
#end
#end
  });

  /**
   * 异步加载树节点数据
   * @param {${BusinessName}VO} row - 当前行数据
   * @param {unknown} treeNode - 树节点信息
   * @param {Function} resolve - 加载完成回调
   * @returns {Promise<void>}
   */
  const load = async (row: ${BusinessName}VO, treeNode: unknown, resolve: (data: ${BusinessName}VO[]) => void): Promise<void> => {
    loadMap.set(row.${treeCode}, { row, treeNode, resolve });
    const res = await list${BusinessName}({ ${treeParentCode}: row.${treeCode} });
    resolve(res.data);
  };

  /**
   * 重新加载指定节点的子节点数据
   * @param {string | number} ${treeCode} - 节点ID
   * @returns {Promise<void>}
   */
  const refreshLoad = async (${treeCode}: string | number): Promise<void> => {
    const cached = loadMap.get(${treeCode});
    if (cached) {
      const { row, resolve } = cached;
      await load(row, null, resolve);
    }
  };

  /**
   * 获取${functionName}树形选择数据
   * @returns {Promise<${BusinessName}DataOption[]>} 树形结构的数据
   */
  const getTreeSelect = async (): Promise<${BusinessName}DataOption[]> => {
    const res = await list${BusinessName}Option({ isTree: true });
    const rootNode: ${BusinessName}DataOption = {
      id: '0',
      value: '0',
      label: '顶级节点',
      isLeaf: false,
      children: res.data
    };
    return [rootNode];
  };

  /**
   * 初始化表单数据
   * @param {Partial<${BusinessName}VO>} ${businessName}Vo - ${functionName}数据
   * @returns {Promise<Ref<${BusinessName}Form>>} 表单响应式对象
   */
  const initFormProps = async (${businessName}Vo: Partial<${BusinessName}VO>): Promise<Ref<${BusinessName}Form>> => {
    const formInline = ref<${BusinessName}Form>({ ${treeParentCode}: '0' });

    if (${businessName}Vo.${treeCode}) {
      const res = await get${BusinessName}(${businessName}Vo.${treeCode});
      formInline.value = structuredClone(res.data);
      return formInline;
    }

    if (${businessName}Vo.${treeParentCode}) {
      Object.assign(formInline.value, ${businessName}Vo);
    }

    return formInline;
  };

  /**
   * 父节点选择事件处理
   * @param {string} value - 选中的值
   * @param {${BusinessName}Form} formInline - 表单数据
   * @returns {Promise<void>}
   */
  const handleChane = async (value: string, formInline: ${BusinessName}Form): Promise<void> => {
    if (!value) return;

    formInline.${treeParentCode} = value === '0' ? '0' : value;

    if (value !== '0') {
      const { data } = await get${BusinessName}(value);
      formInline.${treeCode} = data.${treeCode};
    }
  };

  /**
   * 打开编辑/新增对话框
   * @param {string} title - 对话框标题
   * @param {Partial<${BusinessName}VO>} [row={}] - 行数据
   * @returns {Promise<void>}
   */
  const openDialog = async (title: string, row: Partial<${BusinessName}VO> = {}): Promise<void> => {
#if($isDialog || $isDrawer)
    ${addFormMethod}({
#end
      title: `${title}${functionName}`,
      width: '1024px',
      props: {
        model: await initFormProps(row),
        labelWidth: '140px',
        rules,
        ${businessName}DataOptions: await getTreeSelect()
      },
      contentRenderer: () => h(${className}Form),
      beforeSure: async (done, { options }, formRef) => {
        const formData = options.props.model as ${BusinessName}Form;

        formRef.validate(async (valid: boolean) => {
          if (!valid) return;

          const api = title === '新增' ? add${BusinessName} : edit${BusinessName};
          const res = await api(formData);

          if (res.code === 200) {
            window.skynet.alert(`${title}成功！`);
            done();

            if (!row.${treeParentCode} || row.${treeParentCode} === '0') {
              await skTable.value?.getTableList();
            } else {
              await refreshLoad(row.${treeParentCode});
            }
          }
        });
      }
    });
  };

  /**
   * 导出数据
   * @returns {void}
   */
  const handleExport = (): void => {
    window.skynet
      .download(
        '${moduleName}/${businessName}/export',
        {
          ...skTable.value.searchParam
        },
        `${businessName}_#[[${new Date().getTime()}]]#.xlsx`
      )
      .then(() => {});
  };

  /**
   * 删除${functionName}
   * @param {${BusinessName}VO} [row] - 要删除的数据
   * @returns {Promise<void>}
   * @throws {Error} 删除失败时抛出错误
   */
  const handleDelete = async (row?: ${BusinessName}VO): Promise<void> => {
    if (!row?.${pkColumn.javaField}) return;

    const _ids = row?.${pkColumn.javaField};
    await window.skynet.confirm('是否确认删除${functionName}编号为"' + _ids + '"的数据项？');

    try {
      await remove${BusinessName}(row.${pkColumn.javaField});
      window.skynet.alert('删除成功');

      if (!row.${treeParentCode} || row.${treeParentCode} === '0') {
        await skTable.value?.getTableList();
      } else {
        await refreshLoad(row.${treeParentCode});
      }
    } catch (error) {
      console.error('删除失败:', error);
    }
  };

  return {
    skTable,
    columns,
    load,
    rules,
    openDialog,
    handleExport,
    handleDelete,
    handleChane,
    isExpandAll
  };
}
