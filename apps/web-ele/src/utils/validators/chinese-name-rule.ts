/**
 * 完全按照您提供的规则实现的中文姓名验证
 *
 * 原规则：
 * $.validator.addMethod("isChinese", function(value, element) {
 *     var flag = /^[\u0391-\uFFE5]+$|^[\u0391-\uFFE5]+·?[\u0391-\uFFE5]+$/.test(value);
 *     if (flag){
 *         flag = /^[^【】、﹒▪•]+$/.test(value);
 *     }
 *     return this.optional(element) || flag;
 * }, "只能输入中文字符");
 */

import { RegexEnum } from '#/constants/regex-enum';

/**
 * 使用 regex-enum.ts 中统一定义的正则表达式
 */
const CHINESE_FORMAT_REGEX = RegexEnum.CHINESE_NAME;
const FORBIDDEN_CHARS_REGEX = RegexEnum.FORBIDDEN_CHARS;

/**
 * 完全按照您的规则进行中文姓名验证
 * @param value 要验证的值
 * @param allowEmpty 是否允许空值（对应原规则中的 this.optional(element)）
 * @returns 验证结果
 */
export const isChinese = (
  value: string,
  allowEmpty: boolean = false,
): boolean => {
  // 对应 this.optional(element) - 如果允许空值且为空，则返回 true
  if (allowEmpty && (!value || value.trim() === '')) {
    return true;
  }

  // 如果不允许空值但为空，则返回 false
  if (!value || value.trim() === '') {
    return false;
  }

  const trimmedValue = value.trim();

  // 第一步：检查中文字符格式
  let flag = CHINESE_FORMAT_REGEX.test(trimmedValue);

  // 第二步：如果第一步通过，再检查禁用字符
  if (flag) {
    flag = FORBIDDEN_CHARS_REGEX.test(trimmedValue);
  }

  // return this.optional(element) || flag;
  return flag;
};
