<script setup lang="ts">
// 接收父组件参数并设置默认值
import { DictForm } from '@/api/system/dict/types';
import { useDict } from '@/views/system/dict/utils';

const { handleChane } = useDict();
//定义属性并指定默认值
const contentProps = defineProps<{
  model?: DictForm;
  dictOptions?: [];
}>();
const newFormInline = ref(contentProps.model);
</script>

<template>
  <el-row>
    <el-col :span="12">
      <el-form-item label="父结点ID" prop="parentId">
        <SkDict
          v-model="newFormInline.parentId"
          placeholder="请选择父结点ID"
          :data="dictOptions"
          model="list"
          type="tree-select"
          node-key="id"
          check-strictly
          @change="handleChane($event, newFormInline)"
        ></SkDict>
      </el-form-item>
    </el-col>
    <el-col :span="12">
      <el-form-item label="标准类型" prop="bzlxdm">
        <SkDict v-model="newFormInline.bzlxdm" type="radio-group-button" data="bzlx" placeholder="请输入标准类型"></SkDict>
      </el-form-item>
    </el-col>
    <el-col :span="12">
      <el-form-item label="代码" prop="dm">
        <el-input v-model="newFormInline.dm" placeholder="请输入代码" />
      </el-form-item>
    </el-col>
    <el-col :span="12">
      <el-form-item label="名称" prop="mc">
        <el-input v-model="newFormInline.mc" placeholder="请输入名称" />
      </el-form-item>
    </el-col>
    <el-col :span="12">
      <el-form-item label="简称" prop="jc">
        <el-input v-model="newFormInline.jc" placeholder="请输入简称" />
      </el-form-item>
    </el-col>
    <el-col :span="12">
      <el-form-item label="数据项类型" prop="sjlx">
        <SkDict v-model="newFormInline.sjlx" type="radio-group-button" data="sjlx" placeholder="请选择数据类型"></SkDict>
      </el-form-item>
    </el-col>
    <el-col :span="12">
      <el-form-item label="显示顺序" prop="orderNum">
        <el-input-number v-model="newFormInline.orderNum" placeholder="请输入显示顺序" style="width: 100%" />
      </el-form-item>
    </el-col>
    <el-col v-if="newFormInline.bzlxdm == '2'" :span="12">
      <el-form-item label="对应公安部代码" prop="gabdm">
        <el-input v-model="newFormInline.gabdm" placeholder="请输入对应公安部代码" />
      </el-form-item>
    </el-col>
    <el-col v-if="newFormInline.bzlxdm == '1'" :span="12">
      <el-form-item label="数据元内部标识符" prop="nbbsf">
        <el-input v-model="newFormInline.nbbsf" placeholder="请输入数据元内部标识符" />
      </el-form-item>
    </el-col>
    <el-col v-if="newFormInline.bzlxdm == '1'" :span="24">
      <el-form-item label="采用标准值域" prop="cybzzy">
        <el-input v-model="newFormInline.cybzzy" placeholder="请输入采用标准值域" />
      </el-form-item>
    </el-col>
    <el-col :span="12">
      <el-form-item label="其他配置1代码" prop="qtdm">
        <el-input v-model="newFormInline.qtdm1" placeholder="请输入其他配置代码1" />
      </el-form-item>
    </el-col>
    <el-col :span="12">
      <el-form-item label="其他配置1说明" prop="qtdm">
        <el-input v-model="newFormInline.qtdmsm1" placeholder="请输入其他配置说明1" />
      </el-form-item>
    </el-col>
    <el-col :span="12">
      <el-form-item label="其他配置2代码" prop="qtdm">
        <el-input v-model="newFormInline.qtdm2" placeholder="请输入其他配置代码2" />
      </el-form-item>
    </el-col>
    <el-col :span="12">
      <el-form-item label="其他配置2说明" prop="qtdm">
        <el-input v-model="newFormInline.qtdmsm2" placeholder="请输入其他配置说明2" />
      </el-form-item>
    </el-col>
  </el-row>
</template>
