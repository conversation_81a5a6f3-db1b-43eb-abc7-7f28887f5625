package com.ruoyi.flow.domain.vo.testleave;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.ruoyi.common.excel.annotation.ExcelDictFormat;
import com.ruoyi.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import com.ruoyi.common.orm.core.domain.BaseEntity;

/**
 * 流程测试视图对象 test_leave
 *
 * <AUTHOR>
 * @date 2025-01-14 15:32:53
 */
@Data
@ExcelIgnoreUnannotated
public class TestLeaveExcelVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

     /** id */
    @ExcelProperty(value = "id")
    private Long id;

     /** folw_type */
    @ExcelProperty(value = "folw_code")
    private String flowCode;

     /** ywmc */
    @ExcelProperty(value = "ywmc")
    private String ywmc;

     /** start_time */
    @ExcelProperty(value = "start_time")
    private Date startTime;

     /** end_time */
    @ExcelProperty(value = "end_time")
    private Date endTime;

     /** instance_id */
    @ExcelProperty(value = "instance_id")
    private Long instanceId;

     /** node_code */
    @ExcelProperty(value = "node_code")
    private String nodeCode;

     /** node_name */
    @ExcelProperty(value = "node_name")
    private String nodeName;

     /** node_type */
    @ExcelProperty(value = "node_type")
    private Integer nodeType;

     /** flow_status */
    @ExcelProperty(value = "flow_status")
    private String flowStatus;

     /** create_by */
    @ExcelProperty(value = "create_by")
    private String createBy;

     /** create_time */
    @ExcelProperty(value = "create_time")
    private Date createTime;

     /** update_by */
    @ExcelProperty(value = "update_by")
    private String updateBy;

     /** update_time */
    @ExcelProperty(value = "update_time")
    private Date updateTime;

     /** version */
    @ExcelProperty(value = "version")
    private Long version;

     /** jlzt */
    @ExcelProperty(value = "jlzt")
    private Integer jlzt;



}
