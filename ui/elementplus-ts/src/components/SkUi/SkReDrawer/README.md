# SkReDrawer 抽屉组件

基于Element Plus的Drawer组件封装的可复用抽屉组件，支持动态创建、销毁、事件处理等功能。

## 功能特点

- **动态创建销毁**：无需预先定义，可以在任何地方动态创建抽屉
- **灵活的内容渲染**：支持使用渲染函数动态生成内容
- **丰富的回调函数**：提供完整的生命周期回调
- **表单集成**：内置表单支持和验证功能
- **自定义底部按钮**：可配置按钮样式、行为和确认提示
- **支持多种方向**：可从右、左、上、下四个方向打开抽屉
- **支持嵌套使用**：可在抽屉中打开新的抽屉
- **兼容SkReDialog**：提供与SkReDialog组件相同的接口，便于统一使用
- **全屏模式**：支持全屏显示抽屉
- **自定义图标**：支持自定义关闭图标
- **全屏按钮**：标题栏右侧提供全屏切换按钮，可随时切换抽屉全屏状态

## 安装

组件已经集成在项目中，无需额外安装。

## 使用方法

### 导入组件

```typescript
import { addDrawer, closeDrawer, updateDrawer, closeAllDrawer, DrawerOptions } from '@/components/SkUi/SkReDrawer';
```

### 基本使用

```typescript
// 打开抽屉
addDrawer({
  title: '抽屉标题',
  width: '30%',  // 可以使用width属性代替size属性，与SkReDialog保持一致
  direction: 'rtl', // 可选值：'rtl'(右侧), 'ltr'(左侧), 'ttb'(顶部), 'btt'(底部)
  contentRenderer: ({ options, index }) => {
    return h('div', '这是抽屉内容');
  },
  // 打开抽屉时的回调
  open: ({ options, index }) => {
    console.log('抽屉已打开');
  },
  // 关闭抽屉时的回调
  close: ({ options, index }) => {
    console.log('抽屉已关闭');
  }
});
```

### 全屏抽屉

```typescript
// 打开全屏抽屉
addDrawer({
  title: '全屏抽屉',
  fullscreen: true,  // 启用全屏模式
  contentRenderer: ({ options, index }) => {
    return h('div', { class: 'p-4' }, [
      h('p', '这是一个全屏抽屉示例'),
      h('p', '使用fullscreen属性可以设置抽屉全屏显示')
    ]);
  }
});
```

### 自定义关闭图标

```typescript
import { Close } from '@element-plus/icons-vue';  // 导入Element Plus图标

// 使用自定义关闭图标
addDrawer({
  title: '自定义图标',
  width: '30%',
  closeIcon: Close,  // 使用Element Plus的Close图标
  contentRenderer: ({ options, index }) => {
    return h('div', { class: 'p-4' }, [
      h('p', '这个抽屉使用了自定义关闭图标'),
      h('p', '可以传入任何组件作为关闭图标')
    ]);
  }
});
```

### 表单抽屉

```typescript
import { ref } from 'vue';

const formData = ref({
  name: '',
  age: 18,
  address: ''
});

addDrawer({
  title: '编辑信息',
  width: '40%',  // 使用width属性设置抽屉宽度，与SkReDialog保持一致
  contentRenderer: ({ options, index }) => {
    // 使用Vue的h函数渲染表单组件
    return h('div', [
      h('el-form-item', { label: '姓名' }, [
        h('el-input', { 
          modelValue: formData.value.name, 
          'onUpdate:modelValue': val => formData.value.name = val,
          placeholder: '请输入姓名'
        })
      ]),
      h('el-form-item', { label: '年龄' }, [
        h('el-input-number', { 
          modelValue: formData.value.age, 
          'onUpdate:modelValue': val => formData.value.age = val,
          min: 0,
          max: 120
        })
      ]),
      h('el-form-item', { label: '地址' }, [
        h('el-input', { 
          modelValue: formData.value.address, 
          'onUpdate:modelValue': val => formData.value.address = val,
          type: 'textarea',
          rows: 3,
          placeholder: '请输入地址'
        })
      ])
    ]);
  },
  beforeSure: (done, { options, index }, formRef) => {
    // 表单提交前的验证
    if (formRef) {
      formRef.validate(valid => {
        if (valid) {
          // 验证通过，保存数据
          saveData().then(() => {
            done(); // 关闭抽屉
          });
        }
      });
    } else {
      done();
    }
  }
});
```

### 自定义底部按钮

```typescript
addDrawer({
  title: '自定义底部',
  contentRenderer: () => h('div', '内容'),
  footerButtons: [
    {
      label: '保存',
      type: 'primary',
      btnClick: ({ drawer: { options, index } }) => {
        // 处理保存逻辑
        closeDrawer(options, index);
      }
    },
    {
      label: '保存并新建',
      type: 'success',
      btnClick: ({ drawer: { options, index } }) => {
        // 保存并新建
        saveAndCreate().then(() => {
          // 更新当前抽屉内容
          updateDrawer(options, index, { 
            title: '新建记录' 
          });
        });
      }
    },
    {
      label: '取消',
      btnClick: ({ drawer: { options, index } }) => {
        closeDrawer(options, index);
      }
    }
  ]
});
```

### 确认对话框

```typescript
addDrawer({
  title: '危险操作',
  contentRenderer: () => h('div', '此操作将永久删除数据，是否继续？'),
  popconfirm: {
    title: '确认删除？',
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    confirmButtonType: 'danger',
    icon: 'WarningFilled',
    iconColor: '#ff4949'
  },
  beforeSure: (done, { options, index }) => {
    // 确认后的操作
    deleteData().then(() => {
      ElMessage.success('删除成功');
      done();
    });
  }
});
```

### 不同方向的抽屉

```typescript
// 右侧抽屉（默认）
addDrawer({
  title: '右侧抽屉',
  direction: 'rtl',
  contentRenderer: () => h('div', '右侧打开的抽屉')
});

// 左侧抽屉
addDrawer({
  title: '左侧抽屉',
  direction: 'ltr',
  contentRenderer: () => h('div', '左侧打开的抽屉')
});

// 顶部抽屉
addDrawer({
  title: '顶部抽屉',
  direction: 'ttb',
  contentRenderer: () => h('div', '顶部打开的抽屉')
});

// 底部抽屉
addDrawer({
  title: '底部抽屉',
  direction: 'btt',
  contentRenderer: () => h('div', '底部打开的抽屉')
});
```

### 嵌套抽屉

```typescript
addDrawer({
  title: '第一层抽屉',
  width: '40%',
  contentRenderer: () => {
    return h('div', [
      h('p', '这是第一层抽屉'),
      h('el-button', {
        type: 'primary',
        onClick: () => {
          addDrawer({
            title: '第二层抽屉',
            width: '30%',
            contentRenderer: () => h('div', '这是嵌套在第一层抽屉中的第二层抽屉')
          });
        }
      }, '打开第二层抽屉')
    ]);
  }
});
```

## 组件API

### DrawerOptions 属性

| 属性名 | 类型 | 默认值 | 说明 |
|-------|------|-------|------|
| visible | boolean | false | 抽屉是否可见 |
| title | string | - | 抽屉标题 |
| width | string/number | '30%' | 抽屉宽度，支持百分比或具体像素值（**推荐使用**，与SkReDialog保持一致） |
| size | string/number | '30%' | 抽屉大小，与width功能相同，为保持与Element Plus原生属性一致而保留 |
| direction | 'rtl'/'ltr'/'ttb'/'btt' | 'rtl' | 抽屉方向 |
| modal | boolean | true | 是否显示遮罩层 |
| appendToBody | boolean | true | 是否将抽屉内容插入至body元素 |
| lockScroll | boolean | true | 是否在抽屉出现时将body滚动锁定 |
| modalMaskClick | boolean | true | 点击遮罩层是否可以关闭抽屉 |
| closeOnClickModal | boolean | true | 是否可以通过点击遮罩层关闭抽屉 |
| closeOnPressEscape | boolean | true | 是否可以通过按下ESC关闭抽屉 |
| showClose | boolean | true | 是否显示关闭按钮 |
| withHeader | boolean | true | 是否显示标题栏 |
| destroyOnClose | boolean | false | 关闭时是否销毁抽屉内容 |
| headerRenderer | Function | - | 自定义标题区域渲染函数 |
| contentRenderer | Function | - | 内容区域渲染函数（必须） |
| footerRenderer | Function | - | 自定义底部区域渲染函数 |
| footerButtons | Array\<ButtonProps\> | - | 底部按钮配置 |
| hideFooter | boolean | false | 是否隐藏底部操作区 |
| beforeSure | Function | - | 点击确定前的回调函数 |
| beforeCancel | Function | - | 点击取消前的回调函数 |
| beforeClose | Function | - | 关闭前的回调函数 |
| props | object | - | 传递给content组件的props |
| popconfirm | object | - | 确认按钮的气泡确认框配置 |
| open | Function | - | 打开抽屉时的回调 |
| close | Function | - | 关闭抽屉时的回调 |
| closeCallBack | Function | - | 抽屉关闭后的回调，包含关闭的方式信息 |
| openAutoFocus | Function | - | 输入焦点聚焦在抽屉内容时的回调 |
| closeAutoFocus | Function | - | 输入焦点从抽屉内容失焦时的回调 |
| openDelay | number | 0 | 打开抽屉的延时时间(毫秒) |
| closeDelay | number | 200 | 关闭抽屉的延时时间(毫秒) |

### ButtonProps 属性

| 属性名 | 类型 | 默认值 | 说明 |
|-------|------|-------|------|
| label | string | - | 按钮文字 |
| type | string | - | 按钮类型，可选值: 'primary', 'success', 'warning', 'danger', 'info' |
| size | string | - | 按钮大小，可选值: 'large', 'default', 'small' |
| plain | boolean | false | 是否为朴素按钮 |
| bg | boolean | false | 是否显示文字按钮背景颜色 |
| disabled | boolean | false | 是否禁用按钮 |
| loading | boolean | false | 是否为加载中状态 |
| popconfirm | object | - | 气泡确认框配置 |
| btnClick | Function | - | 点击按钮的回调函数 |

### 方法

| 方法名 | 参数 | 返回值 | 说明 |
|-------|------|-------|------|
| addDrawer | DrawerOptions | void | 添加并显示抽屉 |
| closeDrawer | (options, index, args) | void | 关闭指定抽屉，args可以传递额外参数 |
| updateDrawer | (options, index, newOptions) | void | 更新抽屉配置，newOptions会与原配置合并 |
| closeAllDrawer | - | void | 关闭所有抽屉 |

## 注意事项

1. `contentRenderer` 函数是必须的，它用于渲染抽屉的主体内容
2. 使用表单时，可以在 `beforeSure` 回调中获取表单实例进行验证
3. 当使用嵌套抽屉时，每个抽屉都有自己的索引，请确保正确使用索引关闭对应的抽屉
4. 当设置 `destroyOnClose` 为 true 时，关闭抽屉会销毁其中的组件，导致状态丢失
5. 为了保持与 `SkReDialog` 组件的一致性，推荐使用 `width` 属性来设置抽屉宽度 