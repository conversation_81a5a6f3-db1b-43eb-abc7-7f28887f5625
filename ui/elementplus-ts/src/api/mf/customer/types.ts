export interface CustomerVO extends BaseEntity {
  /**
   * 客户id
   */
  id: string | number;
  /**
   * 客户姓名
   */
  customerName: string;
  /**
   * 手机号码
   */
  phonenumber: string;
  /**
   * 客户性别
   */
  gender: string;
  /**
   * 客户生日
   */
  birthday: string;
  /**
   * 客户描述
   */
  remark: string;
  /** 商品子信息 */
  mfGoodsList: Array<MfGoodsForm>;
}

export interface CustomerForm {
  /**
   * 客户id
   */
  id?: string | number;
  /**
   * 客户姓名
   */
  customerName?: string;
  /**
   * 手机号码
   */
  phonenumber?: string;
  /**
   * 客户性别
   */
  gender?: string;
  /**
   * 客户生日
   */
  birthday?: string;
  /**
   * 客户描述
   */
  remark?: string;
  /**
   * 乐观锁
   */
  version?: number;
  /** 商品子信息 */
  mfGoodsList: Array<MfGoodsForm>;
}

export interface MfGoodsForm {
  /**
   * 列表序号
   */
  rId?: string | number;
  /**
   * 商品id
   */
  id: string | number;
  /**
   * 客户id
   */
  customerId: string | number;
  /**
   * 商品名称
   */
  name: string;
  /**
   * 商品重量
   */
  weight: number;
  /**
   * 商品价格
   */
  price: number;
  /**
   * 商品时间
   */
  date: string;
  /**
   * 商品种类
   */
  type: string;
}

export interface CustomerQuery extends PageQuery {
  /**
   * 客户姓名
   */
  customerName?: string;
  /**
   * 手机号码
   */
  phonenumber?: string;
  /**
   * 客户性别
   */
  gender?: string;
  /**
   * 客户生日
   */
  birthday?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
