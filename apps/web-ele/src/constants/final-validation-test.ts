// 最终验证测试：确保所有字段的全角字符错误信息正确
import { BaseSchema, ZodEnum } from './zod-enum';

console.log('=== 最终验证测试 ===');

// 1. 测试所有 BaseSchema 字段的全角字符错误
console.log('\n1. BaseSchema 全角字符错误测试:');

const fullWidthTests = [
  { field: 'STRING', input: '你好世界', schema: BaseSchema.STRING },
  { field: 'EMAIL', input: '测试@example.com', schema: BaseSchema.EMAIL },
  { field: 'PHONE', input: '１３８１２３４５６７８', schema: BaseSchema.PHONE },
  { field: 'GAJGJGDM', input: '１１１１１１１１１１１１', schema: BaseSchema.GAJGJGDM },
];

fullWidthTests.forEach(test => {
  const result = test.schema.safeParse(test.input);
  console.log(`\n${test.field} 测试:`);
  console.log(`输入: ${test.input}`);
  console.log(`结果:`, result.success ? '通过' : '失败');
  if (!result.success) {
    console.log(`错误信息: "${result.error.issues[0].message}"`);
    console.log(`期望: "不能包含全角字符，请使用半角字符"`);
    console.log(`是否正确: ${result.error.issues[0].message === '不能包含全角字符，请使用半角字符' ? '✅' : '❌'}`);
  }
});

// 2. 测试半角字符的其他错误类型
console.log('\n\n2. 半角字符的其他错误类型测试:');

const halfWidthErrorTests = [
  { field: 'EMAIL', input: 'invalid-email', schema: BaseSchema.EMAIL, expectedError: '请输入正确的邮箱' },
  { field: 'PHONE', input: '12345678901', schema: BaseSchema.PHONE, expectedError: '请输入正确的手机号' },
  { field: 'GAJGJGDM', input: '12345', schema: BaseSchema.GAJGJGDM, expectedError: '请输入12位数字' },
  { field: 'GAJGJGDM', input: 'abcdefghijkl', schema: BaseSchema.GAJGJGDM, expectedError: '请输入12位数字' },
];

halfWidthErrorTests.forEach(test => {
  const result = test.schema.safeParse(test.input);
  console.log(`\n${test.field} 半角错误测试:`);
  console.log(`输入: ${test.input}`);
  console.log(`结果:`, result.success ? '通过' : '失败');
  if (!result.success) {
    console.log(`错误信息: "${result.error.issues[0].message}"`);
    console.log(`期望: "${test.expectedError}"`);
    console.log(`是否正确: ${result.error.issues[0].message === test.expectedError ? '✅' : '❌'}`);
  }
});

// 3. 测试正确的输入
console.log('\n\n3. 正确输入测试:');

const correctTests = [
  { field: 'STRING', input: 'hello world', schema: BaseSchema.STRING },
  { field: 'EMAIL', input: '<EMAIL>', schema: BaseSchema.EMAIL },
  { field: 'PHONE', input: '13812345678', schema: BaseSchema.PHONE },
  { field: 'GAJGJGDM', input: '123456789012', schema: BaseSchema.GAJGJGDM },
];

correctTests.forEach(test => {
  const result = test.schema.safeParse(test.input);
  console.log(`\n${test.field} 正确输入测试:`);
  console.log(`输入: ${test.input}`);
  console.log(`结果: ${result.success ? '✅ 通过' : '❌ 失败'}`);
  if (!result.success) {
    console.log(`意外错误: ${result.error.issues[0].message}`);
  }
});

// 4. 测试 ZodEnum
console.log('\n\n4. ZodEnum 测试:');

const zodEnumTests = [
  { field: 'ZodEnum.STRING', input: '你好', schema: ZodEnum.STRING },
  { field: 'ZodEnum.EMAIL', input: '测试@example.com', schema: ZodEnum.EMAIL },
  { field: 'ZodEnum.PHONE', input: '１３８１２３４５６７８', schema: ZodEnum.PHONE },
  { field: 'ZodEnum.GAJGJGDM', input: '１１１１１１１１１１１１', schema: ZodEnum.GAJGJGDM },
];

zodEnumTests.forEach(test => {
  const result = test.schema.safeParse(test.input);
  console.log(`\n${test.field} 测试:`);
  console.log(`输入: ${test.input}`);
  console.log(`结果:`, result.success ? '通过' : '失败');
  if (!result.success) {
    console.log(`错误信息: "${result.error.issues[0].message}"`);
    console.log(`是否为全角字符错误: ${result.error.issues[0].message === '不能包含全角字符，请使用半角字符' ? '✅' : '❌'}`);
  }
});

// 5. 边界情况测试
console.log('\n\n5. 边界情况测试:');

const boundaryTests = [
  { name: '空字符串', input: '', schema: BaseSchema.STRING },
  { name: '纯数字', input: '123456', schema: BaseSchema.STRING },
  { name: '纯字母', input: 'abcdef', schema: BaseSchema.STRING },
  { name: '符号', input: '!@#$%^&*()', schema: BaseSchema.STRING },
  { name: '混合半角', input: 'hello123!@#', schema: BaseSchema.STRING },
];

boundaryTests.forEach(test => {
  const result = test.schema.safeParse(test.input);
  console.log(`\n${test.name} 测试:`);
  console.log(`输入: "${test.input}"`);
  console.log(`结果: ${result.success ? '✅ 通过' : '❌ 失败'}`);
  if (!result.success) {
    console.log(`错误信息: "${result.error.issues[0].message}"`);
  }
});

// 6. 总结
console.log('\n\n=== 测试总结 ===');
console.log('✅ 所有字段现在都使用统一的 withNoFullWidth 包装器');
console.log('✅ 全角字符输入会显示: "不能包含全角字符，请使用半角字符"');
console.log('✅ 其他错误会显示相应的具体错误信息');
console.log('✅ 验证优先级正确：全角字符错误优先级最高');

export {
  fullWidthTests,
  halfWidthErrorTests,
  correctTests,
  zodEnumTests,
  boundaryTests
};
