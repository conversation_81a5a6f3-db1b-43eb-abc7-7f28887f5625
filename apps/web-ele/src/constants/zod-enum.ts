import { z } from '#/adapter/form';
import { RegexEnum } from '#/constants/regex-enum';

/**
 * 验证身份证号码的校验码
 * @param idCard 18位身份证号码
 * @returns 是否有效
 */
const validateIdCardChecksum = (idCard: string): boolean => {
  if (idCard.length !== 18) return false;

  // 加权因子
  const weights = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
  // 校验码对应表
  const checkCodes = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'];

  // 计算前17位的加权和
  let sum = 0;
  for (let i = 0; i < 17; i++) {
    const digit = parseInt(idCard[i]);
    if (isNaN(digit)) return false;
    sum += digit * weights[i];
  }

  // 计算校验码
  const checkCodeIndex = sum % 11;
  const expectedCheckCode = checkCodes[checkCodeIndex];

  // 比较校验码（大小写不敏感）
  return idCard[17].toUpperCase() === expectedCheckCode;
};

/**
 * 验证身份证号码的出生日期是否有效
 * @param idCard 18位身份证号码
 * @returns 是否有效
 */
const validateIdCardBirthDate = (idCard: string): boolean => {
  if (idCard.length !== 18) return false;

  const year = parseInt(idCard.substring(6, 10));
  const month = parseInt(idCard.substring(10, 12));
  const day = parseInt(idCard.substring(12, 14));

  // 检查年份范围（1900-当前年份）
  const currentYear = new Date().getFullYear();
  if (year < 1900 || year > currentYear) return false;

  // 检查月份
  if (month < 1 || month > 12) return false;

  // 检查日期
  const date = new Date(year, month - 1, day);
  return date.getFullYear() === year &&
         date.getMonth() === month - 1 &&
         date.getDate() === day;
};

/**
 * 验证身份证号码的地区代码是否有效
 * @param idCard 18位身份证号码
 * @returns 是否有效
 */
const validateIdCardAreaCode = (idCard: string): boolean => {
  if (idCard.length !== 18) return false;

  const areaCode = idCard.substring(0, 6);
  const firstTwo = parseInt(areaCode.substring(0, 2));

  // 地区代码前两位的有效范围
  const validAreaCodes = [
    11, 12, 13, 14, 15, // 北京、天津、河北、山西、内蒙古
    21, 22, 23, // 辽宁、吉林、黑龙江
    31, 32, 33, 34, 35, 36, 37, // 上海、江苏、浙江、安徽、福建、江西、山东
    41, 42, 43, 44, 45, 46, // 河南、湖北、湖南、广东、广西、海南
    50, 51, 52, 53, 54, // 重庆、四川、贵州、云南、西藏
    61, 62, 63, 64, 65, // 陕西、甘肃、青海、宁夏、新疆
    71, 81, 82, 91 // 台湾、香港、澳门、国外
  ];

  return validAreaCodes.includes(firstTwo);
};

/**
 * 为任何 schema 添加全角字符验证的包装器
 * 使用 refine 方法确保全角字符验证优先级最高
 * @param schema 要包装的 schema
 * @param message 自定义错误消息
 * @returns 包装后的 schema
 */
export const withNoFullWidth = <T extends z.ZodTypeAny>(
  schema: T,
  message?: string,
) =>
  z.string().superRefine((val, ctx) => {
    // 首先检查全角字符
    if (val && val !== '' && RegexEnum.FULL_WIDTH_REGEX.test(val)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: message || '不能包含全角字符，请使用半角字符',
      });
      return;
    }

    // 如果没有全角字符，执行原始验证
    const result = schema.safeParse(val);
    if (!result.success) {
      // 添加原始验证的错误
      result.error.issues.forEach((issue) => {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: issue.message,
          path: issue.path,
        });
      });
    }
  });

// 基础验证规则（避免循环引用）
export const BaseSchema = {
  STRING: withNoFullWidth(z.string()),
  EMAIL: withNoFullWidth(z.string().email('请输入正确的邮箱')),
  PHONE: withNoFullWidth(
    z.string().regex(RegexEnum.MOBILE, '请输入正确的手机号'),
  ),
  GAJGJGDM: withNoFullWidth(
    z
      .string()
      .length(12, '请输入12位数字')
      .regex(/^\d{12}$/, '请输入12位数字'),
  ),
};

// 可选值包装器（函数形式）
export const optional = <T extends z.ZodTypeAny>(schema: T) =>
  schema.optional().or(z.literal(''));

// 枚举式 Zod 模式集合
export const ZodEnum = {
  STRING: BaseSchema.STRING,
  EMAIL: BaseSchema.EMAIL,
  EMAIL_EMPTY: optional(BaseSchema.EMAIL),
  PHONE: BaseSchema.PHONE,
  PHONE_EMPTY: optional(BaseSchema.PHONE),
  /**
   *公安机关机构代码
   */
  GAJGJGDM: BaseSchema.GAJGJGDM,
  /**
   *公安机关机构代码，可为空
   */
  GAJGJGDM_EMPTY: optional(BaseSchema.GAJGJGDM),
} as const;

export type ZodEnumKey = keyof typeof ZodEnum;
