import type { ColumnProps, SkTableInstance } from '@/components/SkUi/SkTable/interface';
import { h, ref } from 'vue';
import type { PostForm, PostVO } from '@/api/system/post/types';
import PostDialog from '@/views/system/post/components/PostDialog.vue';
import { addPost, delPost, getPost, updatePost } from '@/api/system/post';
import { addDialog } from '@/components/SkUi/SkReDialog';
import { useValidator } from '@/hooks/skUi/web/useValidator';

export function postOptions() {
  // skTable 实例
  const skTable = ref<SkTableInstance>();
  // 表格配置项
  const columns = reactive<ColumnProps[]>([
    { type: 'index', label: '#', width: 80 },
    { prop: 'code', label: '岗位编码', align: 'center', search: { el: 'input' } },
    { prop: 'name', label: '岗位名称', align: 'center', search: { el: 'input' } },
    { prop: 'sort', label: '岗位排序', align: 'center' },
    {
      prop: 'status',
      label: '状态',
      width: 100,
      align: 'center',
      search: { el: 'sk-dict', props: { data: 'xtkg' } }
    },

    {
      prop: 'createTime',
      label: '创建时间',
      width: 180,
      search: {
        el: 'date-picker',
        span: 2,
        props: {
          type: 'daterange',
          valueFormat: 'YYYY-MM-DD',
          rangeSeparator: '-',
          startPlaceholder: '开始日期',
          endPlaceholder: '结束日期'
        }
      }
    },
    { prop: 'operation', label: '操作', width: 160, fixed: 'right' }
  ]);
  const { required } = useValidator();
  const rules = reactive({
    name: [required('岗位名称不能为空')],
    code: [required('岗位编码不能为空')],
    sort: [required('岗位顺序不能为空')],
    status: [required('状态不能为空')]
  });

  /**
   * @Description: 获取岗位信息
   * <AUTHOR>
   * @date 2024/09/26 14:34
   * @param row
   */
  const getPostDasta = async (row: PostVO) => {
    const postId = row.id;
    if (postId) {
      const { data } = await getPost(postId);
      Object.assign(row, data);
    }
    return row;
  };

  /**
   * 编辑，新增
   */
  const openDialog = async (title: string, row: Partial<PostVO> = {}) => {
    addDialog({
      title: title + '岗位',
      width: '60%',
      props: {
        model: await getPostDasta(<PostVO>row),
        labelWidth: '100px',
        rules: rules
      },
      contentRenderer: () => h(PostDialog),
      beforeSure: async (done, { options }, formRef) => {
        const curData = options.props.model as PostForm;
        formRef.validate(async (valid: boolean) => {
          if (valid) {
            const api = title === '新增' ? addPost : updatePost;
            await api(curData).then((res: any) => {
              if (res.code === 200) {
                window.skynet.alert(`${title}成功！`);
                done();
                skTable.value?.getTableList();
              }
            });
          }
        });
      }
    });
  };
  /** 导出按钮操作 */
  const handleExport = () => {
    window.skynet?.download(
      'system/post/export',
      {
        ...skTable.value.searchParam
      },
      `dict_${new Date().getTime()}.xlsx`
    );
  };
  /** 删除按钮操作 */
  const handleDelete = async (row?: PostVO) => {
    const ids = row?.id;
    await window.skynet.confirm('是否确认删除岗位编号为"' + ids + '"的数据项？');
    await delPost(ids);
    await skTable.value?.getTableList();
    window.skynet.alert('删除成功');
  };

  return {
    skTable,
    columns,
    rules,
    openDialog,
    handleDelete,
    handleExport
  };
}
