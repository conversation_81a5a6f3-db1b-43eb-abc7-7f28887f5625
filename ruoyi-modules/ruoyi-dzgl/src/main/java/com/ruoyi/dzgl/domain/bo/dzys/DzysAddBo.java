package com.ruoyi.dzgl.domain.bo.dzys;

import com.ruoyi.dzgl.domain.po.Dzys;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import jakarta.validation.constraints.*;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serial;
import java.io.Serializable;

/**
 * 地址元素管理业务对象 dz_dzys
 *
 * <AUTHOR>
 * @since 2025-06-04 16:50:12
 */
@Data
@AutoMapper(target = Dzys.class, reverseConvertGenerate = false)
public class DzysAddBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 代码
     */
    @NotBlank(message = "代码不能为空")
    private String dm;

    /**
     * 名称
     */
    @NotBlank(message = "名称不能为空")
    private String mc;

    /**
     * 简称
     */
    @NotBlank(message = "简称不能为空")
    private String jc;

    /**
     * 父结点ID
     */
    @NotBlank(message = "父结点ID不能为空")
    private String parentId;

    /**
     * 地址元素类型代码
     */
    @NotBlank(message = "地址元素类型代码不能为空")
    private String dzyslxdm;

    /**
     * 使用状态代码
     */
    @NotBlank(message = "使用状态代码不能为空")
    private String syztdm;

    /**
     * 数据归属单位ID
     */
    @NotBlank(message = "数据归属单位ID不能为空")
    private String sjgsDeptId;

    /**
     * 流动人口数据归属单位ID
     */
    @NotBlank(message = "流动人口数据归属单位ID不能为空")
    private String sjgsLdrkDeptId;

    /**
     * 设立日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" )
    private LocalDateTime slrq;

    /**
     * 抵边判断标志
     */
    private String dbPdbz;


}
