import http from '@/utils/http';
// 绑定账号
export const authBinding = (source: string, tenantId: string) => {
  return http.get('/auth/binding/' + source, {
    tenantId: tenantId,
    domain: window.location.host
  });
};

// 解绑账号
export const authUnlock = (authId: string) => {
  return http.delete('/auth/unlock/' + authId);
};
//获取授权列表
export const getAuthList = () => {
  return http.get('/system/social/list');
};
