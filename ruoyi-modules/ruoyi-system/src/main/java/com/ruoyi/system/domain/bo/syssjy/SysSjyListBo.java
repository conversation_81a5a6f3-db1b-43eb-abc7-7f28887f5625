package com.ruoyi.system.domain.bo.syssjy;

import com.ruoyi.system.domain.po.SysSjy;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 数据元管理业务对象 sys_sjy
 *
 * <AUTHOR>
 * @date 2025-01-03 17:31:57
 */
@Data
@AutoMapper(target = SysSjy.class, reverseConvertGenerate = false)
public class SysSjyListBo {

    /**
     * 列名称
     */
    @NotBlank(message = "列名称不能为空")
    private String columnName;

    /**
     * JAVA字段名
     */
    @NotBlank(message = "JAVA字段名不能为空")
    private String javaField;

    /**
     * 列描述
     */
    @NotBlank(message = "列描述不能为空")
    private String columnComment;


}
