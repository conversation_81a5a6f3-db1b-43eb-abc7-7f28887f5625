import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { VbenFormSchema } from '#/adapter/form';

/**
 * 获取编辑表单的字段配置
 * @returns VbenFormSchema[] 表单配置
 */
export function useFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      dependencies: {
        show: () => false,
        triggerFields: [''],
      },
      fieldName: 'id',
      label: '编号',
    },
    {
      component: 'Input',
      dependencies: {
        show: () => false,
        triggerFields: [''],
      },
      fieldName: 'version',
      label: '乐观锁',
    },
    {
      component: 'Input',
      fieldName: 'studentName',
      label: '学生名称',
      rules: 'required',
      componentProps: {
        placeholder: '请输入学生名称',
        clearable: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'studentAge',
      label: '年龄',
      rules: 'required',
      componentProps: {
        placeholder: '请输入年龄',
        clearable: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'studentHobby',
      label: '爱好',
      rules: 'required',
      componentProps: {
        placeholder: '请输入爱好',
        clearable: true,
      },
    },
    {
      component: 'SkDict',
      fieldName: 'studentGender',
      label: '性别',
      rules: 'required',
      componentProps: {
        data: 'xb',
        placeholder: '请选择性别',
        clearable: true,
      },
    },
    {
      component: 'SkDict',
      fieldName: 'studentStatus',
      label: '状态',
      rules: 'required',
      componentProps: {
        data: 'xtkg',
        placeholder: '请选择状态',
        clearable: true,
      },
    },
    {
      component: 'DatePicker',
      fieldName: 'studentBirthday',
      label: '生日',
      rules: 'required',
      componentProps: {
        type: 'date',
        valueFormat: 'YYYY-MM-DD',
        placeholder: '请选择生日',
        clearable: true,
      },
    },
  ];
}

/**
 * 获取查询表单的字段配置
 * @returns VbenFormSchema[] 查询表单配置
 */
export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'studentName',
      label: '学生名称',
      componentProps: {
        placeholder: '请输入学生名称',
        clearable: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'studentAge',
      label: '年龄',
      componentProps: {
        placeholder: '请输入年龄',
        clearable: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'studentHobby',
      label: '爱好',
      componentProps: {
        placeholder: '请输入爱好',
        clearable: true,
      },
    },
    {
      component: 'SkDict',
      fieldName: 'studentGender',
      label: '性别',
      componentProps: {
        data: 'xb',
        placeholder: '请选择性别',
        clearable: true,
      },
    },
    {
      component: 'SkDict',
      fieldName: 'studentStatus',
      label: '状态',
      componentProps: {
        data: 'xtkg',
        placeholder: '请选择状态',
        clearable: true,
      },
    },
    {
      component: 'DatePicker',
      fieldName: 'studentBirthday',
      label: '生日',
      componentProps: {
        type: 'daterange',
        valueFormat: 'YYYY-MM-DD HH:mm:ss',
        rangeSeparator: '-',
        startPlaceholder: '开始日期',
        endPlaceholder: '结束日期',
        clearable: true,
      },
    },
  ];
}

/**
 * 获取表格列配置
 * @returns VxeTableGridOptions['columns'] 表格列配置
 * @description 使用函数的形式返回列数据而不是直接export一个Array常量，是为了响应语言切换时重新翻译表头
 */
export function useGridColumns(): VxeTableGridOptions['columns'] {
  return [
    {
      title: '#',
      type: 'seq',
      width: 60,
      fixed: 'left',
    },
    {
      field: 'id',
      title: '编号',
      align: 'center',
    },
    {
      field: 'studentName',
      title: '学生名称',
      align: 'center',
    },
    {
      field: 'studentAge',
      title: '年龄',
      align: 'center',
    },
    {
      field: 'studentHobby',
      title: '爱好',
      align: 'center',
    },
    {
      field: 'studentGender',
      title: '性别',
      align: 'center',
    },
    {
      field: 'studentStatus',
      title: '状态',
      align: 'center',
    },
    {
      field: 'studentBirthday',
      title: '生日',
      align: 'center',
    },
    {
      title: '操作',
      field: 'action',
      fixed: 'right',
      slots: { default: 'action' },
      width: 240,
    },
  ];
}
