import type { OperLogQuery, OperLogVO } from './types';
import http from '@/utils/http';
import type { PageData } from '@/utils/http/interface';

// 查询操作日志列表
export const list = (query: OperLogQuery) => {
  return http.get<PageData<OperLogVO>>('/monitor/operlog/list', query);
};

// 删除操作日志
export const delOperlog = (id: string | number | Array<string | number>) => {
  return http.delete('/monitor/operlog/' + id);
};

// 清空操作日志
export const cleanOperlog = () => {
  return http.delete('/monitor/operlog/clean');
};
