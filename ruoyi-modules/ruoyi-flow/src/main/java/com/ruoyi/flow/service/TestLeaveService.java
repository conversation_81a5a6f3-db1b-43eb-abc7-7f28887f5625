package com.ruoyi.flow.service;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

import cn.hutool.core.lang.generator.SnowflakeGenerator;
import cn.hutool.core.util.ObjectUtil;
import com.mybatisflex.core.keygen.impl.SnowFlakeIDKeyGenerator;
import com.ruoyi.common.core.core.domain.PageData;
import com.ruoyi.common.core.core.domain.Result;
import com.ruoyi.common.core.core.domain.dto.RoleDTO;
import com.ruoyi.common.core.core.domain.model.LoginUser;
import com.ruoyi.common.orm.core.page.PageQuery;
import com.ruoyi.common.orm.core.page.PageResult;
import com.mybatisflex.core.query.QueryMethods;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.update.UpdateChain;
import com.mybatisflex.core.paginate.Page;
import com.ruoyi.common.core.utils.MapstructUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.orm.core.page.PageResult;
import com.ruoyi.common.orm.core.service.impl.BaseServiceImpl;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.satoken.utlis.LoginHelper;
import jakarta.annotation.Resource;
import org.dromara.warm.flow.core.FlowFactory;
import org.dromara.warm.flow.core.dto.FlowParams;
import org.dromara.warm.flow.core.entity.Instance;
import org.dromara.warm.flow.core.entity.Task;
import org.dromara.warm.flow.core.enums.SkipType;
import org.dromara.warm.flow.core.service.DefService;
import org.dromara.warm.flow.core.service.InsService;
import org.dromara.warm.flow.core.utils.IdUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.flow.mapper.TestLeaveMapper;
import com.ruoyi.flow.domain.po.TestLeave;
import com.ruoyi.flow.domain.po.table.TestLeaveTableDef;
import com.ruoyi.flow.domain.vo.testleave.TestLeaveDetailVo;
import com.ruoyi.flow.domain.vo.testleave.TestLeaveListVo;
import com.ruoyi.flow.domain.vo.testleave.TestLeaveExcelVo;
import com.ruoyi.flow.domain.vo.testleave.TestLeaveOptionVo;
import com.ruoyi.flow.domain.bo.testleave.TestLeaveImportBo;
import com.ruoyi.flow.domain.bo.testleave.TestLeaveAddBo;
import com.ruoyi.flow.domain.bo.testleave.TestLeaveEditBo;
import com.ruoyi.flow.domain.bo.testleave.TestLeaveListBo;
import com.ruoyi.flow.domain.bo.testleave.TestLeaveOptionBo;
import static com.mybatisflex.core.query.QueryMethods.case_;
import static com.ruoyi.flow.domain.po.table.TestLeaveTableDef.TEST_LEAVE;

/**
 * 流程测试Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-14 15:32:53
 */
@Service
public class TestLeaveService extends BaseServiceImpl<TestLeaveMapper, TestLeave> {
    @Resource
    private TestLeaveMapper testLeaveMapper;

    @Resource
    private DefService defService;

    @Resource
    private InsService insService;

    public QueryWrapper query() {
        return super.query().from(TEST_LEAVE);
    }

    private QueryWrapper buildQueryWrapper(TestLeaveListBo testLeaveListBo) {
        QueryWrapper queryWrapper = super.buildBaseQueryWrapper();
        queryWrapper.and(TEST_LEAVE.FLOW_CODE.eq(testLeaveListBo.getFlowCode()));
        queryWrapper.and(TEST_LEAVE.YWMC.eq(testLeaveListBo.getYwmc()));
        queryWrapper.and(TEST_LEAVE.START_TIME.eq(testLeaveListBo.getStartTime()));
        queryWrapper.and(TEST_LEAVE.END_TIME.eq(testLeaveListBo.getEndTime()));
        queryWrapper.and(TEST_LEAVE.INSTANCE_ID.eq(testLeaveListBo.getInstanceId()));
        queryWrapper.and(TEST_LEAVE.NODE_CODE.eq(testLeaveListBo.getNodeCode()));
        queryWrapper.and(TEST_LEAVE.NODE_NAME.like(testLeaveListBo.getNodeName()));
        queryWrapper.and(TEST_LEAVE.NODE_TYPE.eq(testLeaveListBo.getNodeType()));
        queryWrapper.and(TEST_LEAVE.FLOW_STATUS.eq(testLeaveListBo.getFlowStatus()));
        return queryWrapper;
    }


    /**
     * 分页查询流程测试列表
     *
     * @param testLeaveListBo 流程测试ListBo
     * @return 分页流程测试集合
     */
    public Result<PageData<TestLeaveListVo>> page(TestLeaveListBo testLeaveListBo) {
        QueryWrapper queryWrapper = buildQueryWrapper(testLeaveListBo);
            Page<TestLeaveListVo> page = this.pageAs(PageQuery.build(), queryWrapper, TestLeaveListVo.class);
        return PageResult.success(page);
    }


    /**
     * 查询流程测试列表
     *
     * @param testLeaveListBo 流程测试ListBo
     * @return 流程测试集合
     */
    public List<TestLeaveListVo> list(TestLeaveListBo testLeaveListBo) {
        QueryWrapper queryWrapper = buildQueryWrapper(testLeaveListBo);
            return this.listAs(queryWrapper, TestLeaveListVo.class);
    }


    /**
     * 获取流程测试选择项查询条件
     *
     * @param optionBo 流程测试表查询条件
     * @param a              流程测试表对象
     * @return 流程测试选择项查询条件
     */
    private static QueryWrapper getOptionQueryWrapper(TestLeaveOptionBo optionBo, TestLeaveTableDef a) {
        QueryWrapper queryWrapper = new QueryWrapper();
        //选项列表查询条件
        return queryWrapper;
    }

    /**
     * 查询流程测试管理选择列表
     *
     * @param optionBo 流程测试选择查询条件
     * @return 返回树形结构的流程测试信息列表
     */
    public Result<List<TestLeaveOptionVo>> optionSelect(TestLeaveOptionBo optionBo) {
        TestLeaveTableDef a = TEST_LEAVE.as("a");
        QueryWrapper queryWrapper = getOptionQueryWrapper(optionBo, a);

        List<TestLeaveOptionVo> optionVoList = this.listAs(queryWrapper, TestLeaveOptionVo.class);
        return Result.success(optionVoList);
    }
    /**
     * 导出流程测试列表
     *
     * @param testLeaveListBo 流程测试ListBo
     * @return 流程测试集合
     */
    public List<TestLeaveExcelVo> export(TestLeaveListBo testLeaveListBo) {
        QueryWrapper queryWrapper = buildQueryWrapper(testLeaveListBo);
            return this.listAs(queryWrapper, TestLeaveExcelVo.class);
    }



    @Transactional
    public Result<Void> InsertFsd(TestLeaveAddBo testLeaveAddBo,String flowStatus) {
        Long instanceId = IdUtils.nextId();
        LoginUser loginUser = LoginHelper.getLoginUser();
        FlowParams flowParams = FlowParams.build().flowCode(testLeaveAddBo.getFlowCode());

        flowParams.handler(loginUser.getUserId());

        Task task = FlowFactory.newTask();
        Map<String, Object> variable = new HashMap<>();
        variable.put("businessData", testLeaveAddBo);
        variable.put("businessType", "testLeave");
        variable.put("handler1", task);

        flowParams.variable(variable);


        Instance instance = insService.start(instanceId.toString(), flowParams);
        testLeaveAddBo.setInstanceId(instance.getId());
        testLeaveAddBo.setNodeCode(instance.getNodeCode());
        testLeaveAddBo.setNodeName(instance.getNodeName());
        testLeaveAddBo.setNodeType(instance.getNodeType());
        testLeaveAddBo.setFlowStatus(instance.getFlowStatus());
        TestLeave convert = MapstructUtils.convert(testLeaveAddBo, TestLeave.class);
        convert.setId(instanceId.intValue());

        //Integer.valueOf("237405572219203584");

        boolean save = this.save(convert);
        if(!save){
            return Result.fail("记录添加失败");
        }

        return Result.success();
    }
    @Transactional
    public Result<Void> submit(Long id) {


        //TestLeaveDetailVo oneAs = this.getOneAs(query().where(TEST_LEAVE.ID.eq(id)), TestLeaveDetailVo.class);

        TestLeave testleave = this.getOne(query().where(TEST_LEAVE.ID.eq(id)));
        //TestLeave oneAs = this.getOneAs(query().where(TEST_LEAVE.ID.eq(id)), TestLeave.class);
        LoginUser loginUser = LoginHelper.getLoginUser();
        FlowParams flowParams = FlowParams.build().skipType(SkipType.PASS.getKey());

        flowParams.handler(loginUser.getUserId());

        List<RoleDTO> roles = loginUser.getRoles();

        List<String> permissionList = new ArrayList<>();
        if(ObjectUtil.isNotEmpty(roles)){
            permissionList = roles.stream().map(role -> "role:" + role.getRoleId()).collect(Collectors.toList());
        }

        permissionList.add("dept:"+loginUser.getDeptId());
        permissionList.add("user:"+loginUser.getUserId());
        flowParams.permissionFlag(permissionList);

        Instance instance = insService.skipByInsId(testleave.getInstanceId(), flowParams);
        testleave.setNodeCode(instance.getNodeCode());
        testleave.setNodeName(instance.getNodeName());
        testleave.setNodeType(instance.getNodeType());
        testleave.setFlowStatus(instance.getFlowStatus());

        boolean b = this.updateById(testleave);
        if(!b){
            return Result.fail("记录添加失败");
        }
        return Result.success();
    }




    /**
     * 新增流程测试
     *
     * @param testLeaveAddBo 流程测试AddBo
     * @return 结果:true 操作成功，false 操作失败
     */
    @Transactional
    public Result<Void> add(TestLeaveAddBo testLeaveAddBo) {
        TestLeave testLeave = MapstructUtils.convert(testLeaveAddBo, TestLeave.class);
        boolean inserted = this.save(testLeave);
        if (!inserted) {
            return Result.fail("新增流程测试记录失败！");
        }
        return Result.success();
    }

    /**
     * 修改流程测试
     *
     * @param testLeaveEditBo 流程测试EditBo
     * @return 结果:true 更新成功，false 更新失败
     */
    public Result<Void> edit(TestLeaveEditBo testLeaveEditBo) {
        TestLeave testLeave = MapstructUtils.convert(testLeaveEditBo, TestLeave.class);
        boolean updated = false;
        if(ObjectUtil.isNotNull(testLeave) && ObjectUtil.isNotNull(testLeave.getId())) {
        updated = this.updateById(testLeave);
        }
        if (!updated) {
            return Result.fail("修改流程测试记录失败!");
        }
        return Result.success();
    }


    /**
     * 批量删除流程测试
     *
     * @param ids 需要删除的流程测试主键集合
     * @return 结果:true 删除成功，false 删除失败
     */
    @Transactional
    public Result<Void> removeByIds(Long[] ids) {
        boolean deleted = this.removeByIds(Arrays.asList(ids));
        if (!deleted) {
            return Result.fail("删除流程测试记录失败!");
        }
        return Result.success();
    }


    /**
     * 查询流程测试
     *
     * @param id 流程测试主键
     * @return 流程测试
     */
    public TestLeaveDetailVo detail(Long id) {
        return this.getOneAs(query().where(TEST_LEAVE.ID.eq(id)), TestLeaveDetailVo.class);
    }

    /**
     * 新增流程测试，前台提供主键值，一般用于导入的场合
     *
     * @param testLeaveAddBo 流程测试AddBo
     * @return 结果:true 操作成功，false 操作失败
     */
    public Result<Void> insertWithPk(TestLeaveAddBo testLeaveAddBo) {
        TestLeave testLeave = MapstructUtils.convert(testLeaveAddBo, TestLeave.class);
        boolean inserted;
            inserted=testLeaveMapper.insertWithPk(testLeave) > 0;//前台传来主键值
        if (!inserted) {
            return Result.fail("新增流程测试记录失败！");
        }
        return Result.success();
    }

    public Result<String> flowChart(Long id) throws IOException {
       return Result.success(defService.flowChart(id)) ;
    }

}
