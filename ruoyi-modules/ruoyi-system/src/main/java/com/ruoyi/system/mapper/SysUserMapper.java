package com.ruoyi.system.mapper;

import com.mybatisflex.core.BaseMapper;
import com.mybatisflex.core.query.QueryMethods;
import com.mybatisflex.core.query.QueryWrapper;
import org.apache.ibatis.annotations.Mapper;
import com.ruoyi.system.domain.po.SysUser;

import static com.ruoyi.system.domain.po.table.SysDeptTableDef.SYS_DEPT;
import static com.ruoyi.system.domain.po.table.SysRoleTableDef.SYS_ROLE;
import static com.ruoyi.system.domain.po.table.SysUserRoleTableDef.SYS_USER_ROLE;
import static com.ruoyi.system.domain.po.table.SysUserTableDef.SYS_USER;

/**
 * 用户信息数据访问层接口
 * 提供用户相关的数据库操作，包括用户-部门关联查询和用户-角色关联查询
 *
 * <AUTHOR>
 * @since 2025-04-29 14:00:02
 */
@Mapper
public interface SysUserMapper extends BaseMapper<SysUser> {

    /**
     * 构建用户-部门关联查询的QueryWrapper
     * 查询用户基本信息及其所属部门信息，包括部门名称和负责人信息
     *
     * @return QueryWrapper 包含用户和部门信息的查询包装器
     * @since 1.0.0
     */
    default QueryWrapper selectUserDeptQueryWrapper() {
        return QueryWrapper.create()
            // 选择用户基本信息字段
            .select(
                SYS_USER.ID,
                SYS_USER.TENANT_ID,
                SYS_USER.DEPT_ID,
                SYS_USER.NICK_NAME,
                SYS_USER.USER_NAME,
                SYS_USER.USER_TYPE,
                SYS_USER.EMAIL,
                SYS_USER.AVATAR,
                SYS_USER.PHONENUMBER,
                SYS_USER.GENDER,
                SYS_USER.STATUS,
                SYS_USER.VERSION,
                SYS_USER.JLZT,
                SYS_USER.LOGIN_IP,
                SYS_USER.LOGIN_DATE,
                SYS_USER.CREATE_BY,
                SYS_USER.CREATE_TIME,
                SYS_USER.REMARK,
                // 选择部门相关字段
                SYS_DEPT.QC.as("DEPT_NAME")
            )
            .from(SYS_USER.as("u"))
            // 关联部门表
            .leftJoin(SYS_DEPT).as("d")
                .on(SYS_DEPT.ID.eq(SYS_USER.DEPT_ID))
            // 只查询有效记录
            .where(SYS_USER.JLZT.eq(1));
    }

    /**
     * 构建用户详细信息查询的QueryWrapper
     * 查询用户基本信息及其所属部门的详细信息，包括部门层级关系
     *
     * @return QueryWrapper 包含用户和部门详细信息的查询包装器
     * @since 1.0.0
     */
    default QueryWrapper buildOneQueryWrapper() {
        return QueryWrapper.create()
            .select(QueryMethods.distinct(
                // 用户表字段
                SYS_USER.ID,
                SYS_USER.TENANT_ID,
                SYS_USER.DEPT_ID,
                SYS_USER.NICK_NAME,
                SYS_USER.USER_NAME,
                SYS_USER.USER_TYPE,
                SYS_USER.EMAIL,
                SYS_USER.AVATAR,
                SYS_USER.PHONENUMBER,
                SYS_USER.PASSWORD,
                SYS_USER.GENDER,
                SYS_USER.STATUS,
                SYS_USER.VERSION,
                SYS_USER.JLZT,
                SYS_USER.LOGIN_IP,
                SYS_USER.LOGIN_DATE,
                SYS_USER.CREATE_BY,
                SYS_USER.CREATE_TIME,
                SYS_USER.REMARK,
                // 部门表字段
                SYS_DEPT.ID,
                SYS_DEPT.PARENT_ID,
                SYS_DEPT.ANCESTORS,
                SYS_DEPT.QC.as("DEPT_NAME"),
                SYS_DEPT.ORDER_NUM,
                SYS_DEPT.SYZTDM.as("dept_status")
            ))
            .from(SYS_USER.as("u"))
            .leftJoin(SYS_DEPT)
            .as("d")
            .on(SYS_DEPT.ID.eq(SYS_USER.DEPT_ID));
    }

    /**
     * 构建用户-角色关联查询的QueryWrapper
     * 查询用户基本信息、所属部门及其角色信息，用于用户角色管理
     *
     * @return QueryWrapper 包含用户、部门和角色信息的查询包装器
     * @since 1.0.0
     */
    default QueryWrapper selectUserRoleQueryWrapper() {
        return QueryWrapper.create()
            // 选择用户基本信息字段（去重）
            .select(QueryMethods.distinct(
                SYS_USER.ID,
                SYS_USER.TENANT_ID,
                SYS_USER.DEPT_ID,
                SYS_USER.USER_NAME,
                SYS_USER.NICK_NAME,
                SYS_USER.USER_TYPE,
                SYS_USER.EMAIL,
                SYS_USER.PHONENUMBER,
                SYS_USER.GENDER,
                SYS_USER.STATUS,
                SYS_USER.CREATE_TIME
            ))
            .from(SYS_USER.as("u"))
            // 关联部门表
            .leftJoin(SYS_DEPT).as("d")
                .on(SYS_DEPT.ID.eq(SYS_USER.DEPT_ID))
            // 关联用户角色表
            .leftJoin(SYS_USER_ROLE).as("ur")
                .on(SYS_USER_ROLE.USER_ID.eq(SYS_USER.ID))
            // 关联角色表
            .leftJoin(SYS_ROLE).as("r")
                .on(SYS_ROLE.ID.eq(SYS_USER_ROLE.ROLE_ID))
            // 只查询有效且状态正常的用户
            .where(SYS_USER.JLZT.eq(1))
            .and(SYS_USER.STATUS.eq("0"));
    }

    /**
     * 构建用户个人资料查询的QueryWrapper
     * 根据用户ID查询用户的个人资料信息，包括基本信息但不包含部门信息
     *
     * @return QueryWrapper 包含用户个人资料信息的查询包装器
     * @since 1.0.0
     */
    default QueryWrapper selectProfileUserById() {
        // 构建查询条件
        return QueryWrapper.create()
            .select(QueryMethods.distinct(
                SYS_USER.ID,
                SYS_USER.TENANT_ID,
                SYS_USER.DEPT_ID,
                SYS_USER.NICK_NAME,
                SYS_USER.USER_NAME,
                SYS_USER.USER_TYPE,
                SYS_USER.EMAIL,
                SYS_USER.AVATAR,
                SYS_USER.PHONENUMBER,
                SYS_USER.PASSWORD,
                SYS_USER.GENDER,
                SYS_USER.STATUS,
                SYS_USER.JLZT,
                SYS_USER.LOGIN_IP,
                SYS_USER.LOGIN_DATE,
                SYS_USER.CREATE_BY,
                SYS_USER.CREATE_TIME,
                SYS_USER.REMARK
            ))
            .from(SYS_USER.as("u"))
            .where(SYS_USER.JLZT.eq(1));
    }
}
