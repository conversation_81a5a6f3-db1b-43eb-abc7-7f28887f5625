<script setup lang="ts">
import { ${BusinessName}Form #if($table.sub), ${subClassName}Form #end} from '@/api/${moduleName}/${businessName}/types';
#if($table.sub)
import { Delete } from '@element-plus/icons-vue';
import { generateUUID } from '@/utils';
#end

defineOptions({
  name: '${BusinessName}Dialog'
});
//定义属性并指定默认值
const contentProps = defineProps<{
  model?: ${BusinessName}Form;
#if($table.sub)
  rules?: any;
#end
}>();
const newFormInline = ref(contentProps.model);
#if($table.sub)
const ${subclassName}List = toRef(contentProps.model, '${subclassName}List');
/** ${subTable.functionName}添加按钮操作 */
const handleAdd${subClassName} = () => {
  const obj: ${subClassName}Form = {
    rId: generateUUID(),
#foreach ($column in $subTable.columns)
  #if($column.edit && $column.javaField!='tenantId' && $column.javaField!='version' && $column.javaField!='jlzt')
    #if($column.javaType == 'String')
    $column.javaField: ''#if($foreach.count != $columns.size()),#end
    #else
    $column.javaField: undefined#if($foreach.count != $columns.size()),#end
    #end
  #end
#end
  };
  ${subclassName}List.value.push(obj);
};
/** ${subTable.functionName}删除按钮操作 */
const handleDelete${subClassName} = (selectedList: { [key: string]: any }[]) => {
  selectedList.forEach((selectItem) => {
    if (selectItem.${pkColumn.javaField} != undefined && selectItem.${pkColumn.javaField}.length > 0) {
      ${subclassName}List.value = ${subclassName}List.value.filter((item) => item.${pkColumn.javaField} !== selectItem.${pkColumn.javaField});
    } else {
      ${subclassName}List.value = ${subclassName}List.value.filter((item) => item.rId !== selectItem.rId);
    }
  });
};
#end
</script>
<template>
  <div>
    <el-row>
#foreach($column in $columns)
#set($field=$column.javaField)
#if(($column.insert || $column.edit) && !$column.pk && $field !='version')
  #set($parentheseIndex=$column.columnComment.indexOf("（"))
  #if($parentheseIndex != -1)
    #set($comment=$column.columnComment.substring(0, $parentheseIndex))
  #else
    #set($comment=$column.columnComment)
  #end
  #set($dictType=$column.dictType)
  #if(${editColumns}==1)
      <el-col :span="24">
  #end
  #if(${editColumns}==2)
      <el-col :span="12">
  #end
  #if(${editColumns}==3)
      <el-col :span="8">
  #end
  #if(${editColumns}==4)
      <el-col :span="6">
  #end
  #if($column.htmlType == "input")
        <el-form-item label="${comment}" prop="${field}">
          <el-input v-model="newFormInline.${field}" clearable placeholder="请输入${comment}" />
        </el-form-item>
  #elseif($column.htmlType == "textarea")
        <el-form-item label="${comment}" prop="${field}">
          <el-input v-model="newFormInline.${field}" :rows="2" type="textarea" clearable placeholder="请输入${comment}" />
        </el-form-item>
  #elseif($column.htmlType == "select")
        <el-form-item label="${comment}" prop="${field}">
          <SkDict v-model="newFormInline.${field}" data="${dictType}" placeholder="请输入${comment}"></SkDict>
        </el-form-item>
  #elseif($column.htmlType == "radio-group")
        <el-form-item label="${comment}" prop="${field}">
          <SkDict v-model="newFormInline.${field}" type="radio-group" data="${dictType}" placeholder="请输入${comment}"></SkDict>
        </el-form-item>
  #elseif($column.htmlType == "radio-group-button")
        <el-form-item label="${comment}" prop="${field}">
          <SkDict v-model="newFormInline.${field}" type="radio-group-button" data="${dictType}" placeholder="请输入${comment}"></SkDict>
        </el-form-item>
  #elseif($column.htmlType == "checkbox")
        <el-form-item label="${comment}" prop="${field}">
          <SkDict v-model="newFormInline.${field}" data="${dictType}" placeholder="请输入${comment}"></SkDict>
        </el-form-item>
  #elseif($column.htmlType == "datetime")
        <el-form-item label="${comment}" prop="studentBirthday">
          <el-date-picker
            v-model="newFormInline.${field}"
            type="date"
            value-format="YYYY-MM-DD"
            clearable
            placeholder="请选择${comment}"
          />
        </el-form-item>
  #elseif($column.htmlType == "imageUpload")
        <el-form-item label="${comment}" prop="${field}">
          <image-upload v-model="form.${field}"/>
        </el-form-item>
  #elseif($column.htmlType == "fileUpload")
        <el-form-item label="${comment}" prop="${field}">
          <file-upload v-model="form.${field}"/>
        </el-form-item>
  #elseif($column.htmlType == "editor")
        <el-form-item label="${comment}">
          <editor v-model="form.${field}" :min-height="192"/>
        </el-form-item>
  #end
      </el-col>
#end
#end
    </el-row>
#if($table.sub)
    <el-divider content-position="center">${subTable.functionName}信息</el-divider>
    <sk-table :data="${subclassName}List" :pagination="false" :toolButton="false" row-key="${pkColumn.javaField}">
      <!-- 表格 header 按钮 -->
      <template #tableHeader="scope">
        <el-button v-hasPermi="['${moduleName}:${businessName}:add']" type="primary" icon="CirclePlus" @click="handleAdd${subClassName}"> 新增 </el-button>
        <el-button
          v-hasPermi="['${moduleName}:${businessName}:remove']"
          type="danger"
          :icon="Delete"
          :disabled="!scope.isSelected"
          @click="handleDelete${subClassName}(scope.selectedList)"
        >
          删除
        </el-button>
      </template>
      <el-table-column type="selection" width="50" align="center" />
      <el-table-column fixed label="序号" type="index" width="80" />
      #foreach($column in $subTable.columns)
        #set($javaField=$column.javaField)
        #set($parentheseIndex=$column.columnComment.indexOf("（"))
        #if($parentheseIndex != -1)
          #set($comment=$column.columnComment.substring(0, $parentheseIndex))
        #else
          #set($comment=$column.columnComment)
        #end
        #if(($column.insert || $column.edit) && !$column.pk && $javaField !='version' && $javaField !=${subTableFkclassName})
      <el-table-column label="$comment">
        <template #default="scope">
          <el-form-item :prop="'${subclassName}List.' + scope.$index + '.$javaField'" :rules="contentProps.rules.${subclassName}List.$javaField">
            #if($column.htmlType == "input")
            <el-input v-model="scope.row.$javaField" clearable placeholder="请输入${comment}" />
            #elseif($column.htmlType == "textarea")
            <el-input v-model="scope.row.$javaField" :rows="2" type="textarea" clearable placeholder="请输入${comment}" />
            #elseif($column.htmlType == "select")
            <SkDict v-model="scope.row.$javaField" data="${dictType}" placeholder="请输入${comment}"></SkDict>
            #elseif($column.htmlType == "radio-group")
            <SkDict v-model="scope.row.$javaField" type="radio-group" data="${dictType}" placeholder="请输入${comment}"></SkDict>
            #elseif($column.htmlType == "radio-group-button")
            <SkDict v-model="scope.row.$javaField" type="radio-group-button" data="${dictType}" placeholder="请输入${comment}"></SkDict>
            #elseif($column.htmlType == "checkbox")
            <SkDict v-model="scope.row.$javaField" data="${dictType}" placeholder="请输入${comment}"></SkDict>
            #elseif($column.htmlType == "datetime")
            <el-date-picker
              v-model="scope.row.$javaField"
              type="date"
              value-format="YYYY-MM-DD"
              clearable
              placeholder="请选择${comment}"
            />
            #end
          </el-form-item>
        </template>
      </el-table-column>
        #end
      #end
    </sk-table>
#end
  </div>
</template>
<style lang="scss" scoped>
:deep(.el-form-item__content) {
  margin-left: 0 !important;
}

:deep(.el-table .el-form-item--large) {
  margin-bottom: 0 !important;
}
</style>
