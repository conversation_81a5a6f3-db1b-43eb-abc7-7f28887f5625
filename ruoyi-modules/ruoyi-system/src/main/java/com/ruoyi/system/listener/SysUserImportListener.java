package com.ruoyi.system.listener;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.crypto.digest.BCrypt;
import cn.idev.excel.context.AnalysisContext;
import cn.idev.excel.event.AnalysisEventListener;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.SpringUtils;
import com.ruoyi.common.core.utils.StreamUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.utils.ValidatorUtils;
import com.ruoyi.common.excel.core.ExcelListener;
import com.ruoyi.common.excel.core.ExcelResult;
import com.ruoyi.common.satoken.utlis.LoginHelper;
import com.ruoyi.system.domain.bo.sysuser.SysUserAddBo;
import com.ruoyi.system.domain.bo.sysuser.SysUserEditBo;
import com.ruoyi.system.domain.po.SysUser;
import com.ruoyi.system.domain.vo.SysDeptVo;
import com.ruoyi.system.domain.vo.sysuser.SysUserImportVo;
import com.ruoyi.system.service.SysConfigService;
import com.ruoyi.system.service.SysDeptService;
import com.ruoyi.system.service.SysUserService;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 系统用户自定义导入
 *
 * <AUTHOR> Li
 */
@Slf4j
public class SysUserImportListener extends AnalysisEventListener<SysUserImportVo> implements ExcelListener<SysUserImportVo> {

    private final SysUserService userService;

    private final SysDeptService deptService;

    private final String password;

    private final Boolean isUpdateSupport;

    private final String operateUserId;

    private int successNum = 0;
    private int failureNum = 0;
    private final StringBuilder successMsg = new StringBuilder();
    private final StringBuilder failureMsg = new StringBuilder();

    public SysUserImportListener(Boolean isUpdateSupport) {
        String initPassword = SpringUtils.getBean(SysConfigService.class).selectConfigByKey("sys.user.initPassword" ).getValue();
        this.userService = SpringUtils.getBean(SysUserService.class);
        this.deptService = SpringUtils.getBean(SysDeptService.class);
        this.password = BCrypt.hashpw(initPassword);
        this.isUpdateSupport = isUpdateSupport;
        this.operateUserId = LoginHelper.getUserId();
    }

    @Override
    public void invoke(SysUserImportVo userVo, AnalysisContext context) {
        SysUser sysUser = this.userService.selectUserByUserName(userVo.getUserName());
        try {
            if (StringUtils.isNotEmpty(userVo.getDeptName())) {
                long deptCount = deptService.selectDeptCountByName(userVo.getDeptName());
                if (deptCount == 1) {
                    SysDeptVo dept = deptService.selectDeptByName(userVo.getDeptName());
                    if (ObjectUtil.isNotNull(dept)) {
                        // 验证是否存在这个用户
                        if (ObjectUtil.isNull(sysUser)) {
                            //用户不存在，插入
                            userVo.setUserId(null);//屏蔽掉前台传过来的用户ID，使用系统的雪花算法值
                            SysUserAddBo user = BeanUtil.toBean(userVo, SysUserAddBo.class);
                            user.setDeptId(dept.getId());//获取用户部门ID
                            ValidatorUtils.validate(user);
                            user.setPassword(password);
                            user.setCreateBy(operateUserId);
                            userService.add(user);
                            successNum++;
                            successMsg.append("<br/>" ).append(successNum).append("、账号 " ).append(user.getUserName()).append(" 导入成功" );
                        } else if (isUpdateSupport) {
                            String userId = sysUser.getId();
                            SysUserEditBo user = BeanUtil.toBean(userVo, SysUserEditBo.class);
                            user.setId(userId);
                            user.setDeptId(dept.getId());//获取用户部门ID
                            ValidatorUtils.validate(user);
                            userService.checkUserAllowed(user.getId());
                            userService.checkUserDataScope(user.getId());
                            user.setUpdateBy(operateUserId);
                            userService.edit(user);
                            successNum++;
                            successMsg.append("<br/>" ).append(successNum).append("、账号 " ).append(user.getUserName()).append(" 更新成功" );
                        } else {
                            failureNum++;
                            failureMsg.append("<br/>" ).append(failureNum).append("、账号 " ).append(sysUser.getUserName()).append(" 已存在" );
                        }

                    } else {
                        failureNum++;
                        failureMsg.append("<br/>" ).append(failureNum).append("、账号 " ).append(sysUser.getUserName()).append(" 的部门名称 " ).append(userVo.getDeptName()).append(" 在部门表中不存在，无法导入" );
                    }
                }

                if (deptCount == 0) {
                    failureNum++;
                    failureMsg.append("<br/>" ).append(failureNum).append("、账号 " ).append(userVo.getUserName()).append(" 的部门名称 " ).append(userVo.getDeptName()).append(" 在部门表中不存在，无法导入" );
                }
                if (deptCount > 1) {
                    failureNum++;
                    failureMsg.append("<br/>" ).append(failureNum).append("、账号 " ).append(userVo.getUserName()).append(" 的部门名称 " ).append(userVo.getDeptName()).append(" 存在两条以上部门记录，无法导入" );
                }
            } else {
                failureNum++;
                failureMsg.append("<br/>" ).append(failureNum).append("、账号 " ).append(userVo.getUserName()).append(" 的部门名称为空，无法导入" );
            }

        } catch (Exception e) {
            failureNum++;
            String msg = "<br/>" + failureNum + "、账号 " + userVo.getUserName() + " 导入失败：";
            String message = e.getMessage();
            if (e instanceof ConstraintViolationException cvException) {
                message = StreamUtils.join(cvException.getConstraintViolations(), ConstraintViolation::getMessage, ", " );
            }
            failureMsg.append(msg).append(message);
            log.error(msg, e);
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {

    }

    @Override
    public ExcelResult<SysUserImportVo> getExcelResult() {
        return new ExcelResult<>() {

            @Override
            public String getAnalysis() {
                if (failureNum > 0) {
                    failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据没有成功导入，错误如下：" );
                    throw new ServiceException(failureMsg.toString());
                } else {
                    successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：" );
                }
                return successMsg.toString();
            }

            @Override
            public List<SysUserImportVo> getList() {
                return null;
            }

            @Override
            public List<String> getErrorList() {
                return null;
            }
        };
    }
}
