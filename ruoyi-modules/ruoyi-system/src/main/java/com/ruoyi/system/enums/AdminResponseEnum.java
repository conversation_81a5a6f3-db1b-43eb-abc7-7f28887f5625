package com.ruoyi.system.enums;

import com.ruoyi.common.core.enums.ErrorPrefixEnum;
import com.ruoyi.common.core.exception.common.ServiceExceptionCustomAssert;


/**
 * 异常枚举类 适用于Admin模块的
 */
public enum AdminResponseEnum implements ServiceExceptionCustomAssert {

    // @formatter:off
    XZQH_ID_EXISTS(1001, "行政区划代码已存在"),
    MENU_NAME_EXISTS(1101,"菜单名称已经存在"),
    DEPT_NAME_EXISTS(1201,"部门名称已经存在");
    // @formatter:on

    /**
     * 返回码
     */
    private int code;

    /**
     * 返回消息
     */
    private String msg;

    AdminResponseEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    /**
     * 自定义断言，支持提供错误码和消息
     *
     * @param code    自定义错误码
     * @param msg 自定义错误消息
     * @return 当前枚举常量
     */
    public AdminResponseEnum msg(int code, String msg) {
        this.setCode(code);
        this.setMsg(msg);
        return this;
    }

    private void setCode(int code) {
        this.code = code;
    }

    private void setMsg(String msg) {
        this.msg = msg;
    }

    @Override
    public int getCode() {
        return this.code;
    }

    @Override
    public String getMsg() {
        return msg;
    }

    @Override
    public ErrorPrefixEnum getCodePrefixEnum() {
        return ErrorPrefixEnum.ADMIN;
    }

}
