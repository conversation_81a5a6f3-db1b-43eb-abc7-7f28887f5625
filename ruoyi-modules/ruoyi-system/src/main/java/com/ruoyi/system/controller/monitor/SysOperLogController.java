package com.ruoyi.system.controller.monitor;

import lombok.RequiredArgsConstructor;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.ruoyi.common.core.core.domain.Result;
import com.ruoyi.common.core.core.domain.PageData;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.web.core.BaseController;
import jakarta.annotation.Resource;
import com.ruoyi.system.domain.vo.sysoperlog.SysOperLogDetailVo;
import com.ruoyi.system.domain.vo.sysoperlog.SysOperLogListVo;
import com.ruoyi.system.domain.bo.sysoperlog.SysOperLogListBo;
import com.ruoyi.system.service.SysOperLogService;

/**
 * 操作日志记录控制器
 * <p>
 * 提供操作日志记录地增删改查、导入导出等功能，主要包含：
 * <ul>
 *   <li>操作日志记录地增删改查</li>
 *   <li>操作日志记录的分页查询</li>
 *   <li>操作日志记录的下拉选择</li>
 * </ul>
 *
 * <AUTHOR>
 * @since 2025-05-08 16:31:34
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/monitor/operlog")
public class SysOperLogController extends BaseController {
    @Resource
    private SysOperLogService sysOperLogService;

    /**
     * 分页查询操作日志记录列表
     * <p>
     * 支持多条件组合查询，返回分页数据
     *
     * @param sysOperLogListBo 查询条件对象，包含分页参数和查询条件
     * @return 包含分页信息和操作日志记录列表的结果对象
     */
    @SaCheckPermission("system:operLog:list")
    @GetMapping("/list")
    public Result<PageData<SysOperLogListVo>> page(SysOperLogListBo sysOperLogListBo) {
        return sysOperLogService.page(sysOperLogListBo);
    }

    /**
     * 删除操作日志记录
     * <p>
     * 支持批量删除操作，可同时删除多个操作日志记录
     *
     * @param ids 操作日志记录ID数组，包含要删除的ID列表
     * @return 操作结果，成功或失败信息
     */
    @SaCheckPermission("system:operLog:remove")
    @Log(title = "操作日志记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result<Void> remove(@PathVariable String[] ids) {
        return sysOperLogService.removeByIds(ids);
    }

    /**
     * 获取操作日志记录详细信息
     * <p>
     * 根据ID查询单个操作日志记录的完整信息，包含所有字段
     *
     * @param id 操作日志记录ID
     * @return 包含操作日志记录所有字段的详细信息对象
     */
    @SaCheckPermission("system:operLog:query")
    @GetMapping(value = "/{id}")
    public Result<SysOperLogDetailVo> detail(@PathVariable String id) {
        return Result.success(sysOperLogService.detail(id));
    }


}
