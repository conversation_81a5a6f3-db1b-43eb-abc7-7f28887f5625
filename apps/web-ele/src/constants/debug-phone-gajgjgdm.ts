// 调试 PHONE 和 GAJGJGDM 的全角字符错误信息
import { BaseSchema } from './zod-enum';

console.log('=== 调试 PHONE 和 GAJGJGDM 全角字符错误 ===');

// 1. 测试 PHONE 字段
console.log('\n1. PHONE 字段测试:');

const phoneFullWidthInput = '１３８１２３４５６７８'; // 11位全角数字
console.log('PHONE 输入:', phoneFullWidthInput);
console.log('PHONE 输入长度:', phoneFullWidthInput.length);

const phoneResult = BaseSchema.PHONE.safeParse(phoneFullWidthInput);
console.log('PHONE 结果:', phoneResult);

if (!phoneResult.success) {
  console.log('PHONE 错误数量:', phoneResult.error.issues.length);
  phoneResult.error.issues.forEach((issue, index) => {
    console.log(`PHONE 错误 ${index + 1}:`, {
      message: issue.message,
      code: issue.code,
      path: issue.path
    });
  });
  console.log('PHONE 第一个错误信息:', phoneResult.error.issues[0].message);
  console.log('期望: "不能包含全角字符，请使用半角字符"');
}

// 2. 测试 GAJGJGDM 字段
console.log('\n2. GAJGJGDM 字段测试:');

const gajgjgdmFullWidthInput = '１１１１１１１１１１１１'; // 12位全角数字
console.log('GAJGJGDM 输入:', gajgjgdmFullWidthInput);
console.log('GAJGJGDM 输入长度:', gajgjgdmFullWidthInput.length);

const gajgjgdmResult = BaseSchema.GAJGJGDM.safeParse(gajgjgdmFullWidthInput);
console.log('GAJGJGDM 结果:', gajgjgdmResult);

if (!gajgjgdmResult.success) {
  console.log('GAJGJGDM 错误数量:', gajgjgdmResult.error.issues.length);
  gajgjgdmResult.error.issues.forEach((issue, index) => {
    console.log(`GAJGJGDM 错误 ${index + 1}:`, {
      message: issue.message,
      code: issue.code,
      path: issue.path
    });
  });
  console.log('GAJGJGDM 第一个错误信息:', gajgjgdmResult.error.issues[0].message);
  console.log('期望: "不能包含全角字符，请使用半角字符"');
}

// 3. 测试正则表达式
console.log('\n3. 正则表达式测试:');

const FULL_WIDTH_REGEX = /[\u3000\uFF00-\uFFEF\u4E00-\u9FFF\u3040-\u309F\u30A0-\u30FF]/;

console.log('PHONE 输入是否匹配全角正则:', FULL_WIDTH_REGEX.test(phoneFullWidthInput));
console.log('GAJGJGDM 输入是否匹配全角正则:', FULL_WIDTH_REGEX.test(gajgjgdmFullWidthInput));

// 4. 测试每个字符
console.log('\n4. 字符分析:');

console.log('PHONE 输入字符分析:');
[...phoneFullWidthInput].forEach((char, index) => {
  console.log(`字符 ${index}: "${char}", Unicode: U+${char.charCodeAt(0).toString(16).toUpperCase()}, 是否全角: ${FULL_WIDTH_REGEX.test(char)}`);
});

console.log('\nGAJGJGDM 输入字符分析:');
[...gajgjgdmFullWidthInput].forEach((char, index) => {
  console.log(`字符 ${index}: "${char}", Unicode: U+${char.charCodeAt(0).toString(16).toUpperCase()}, 是否全角: ${FULL_WIDTH_REGEX.test(char)}`);
});

// 5. 直接测试 withNoFullWidth 包装器
console.log('\n5. 直接测试包装器:');

import { withNoFullWidth } from './zod-enum';
import { z } from '#/adapter/form';

// 测试简单的字符串包装器
const simpleWrapper = withNoFullWidth(z.string());
const simpleResult = simpleWrapper.safeParse(phoneFullWidthInput);
console.log('简单包装器结果:', simpleResult);
if (!simpleResult.success) {
  console.log('简单包装器错误信息:', simpleResult.error.issues[0].message);
}

// 测试带正则的包装器
const regexWrapper = withNoFullWidth(z.string().regex(/^1[3-9]\d{9}$/, '请输入正确的手机号'));
const regexResult = regexWrapper.safeParse(phoneFullWidthInput);
console.log('正则包装器结果:', regexResult);
if (!regexResult.success) {
  console.log('正则包装器错误信息:', regexResult.error.issues[0].message);
}

// 6. 测试原始 schema
console.log('\n6. 测试原始 schema:');

const originalPhoneSchema = z.string().regex(/^1[3-9]\d{9}$/, '请输入正确的手机号');
const originalPhoneResult = originalPhoneSchema.safeParse(phoneFullWidthInput);
console.log('原始 PHONE schema 结果:', originalPhoneResult);
if (!originalPhoneResult.success) {
  console.log('原始 PHONE schema 错误信息:', originalPhoneResult.error.issues[0].message);
}

const originalGajgjgdmSchema = z.string().length(12, '请输入12位数字').regex(/^\d{12}$/, '请输入12位数字');
const originalGajgjgdmResult = originalGajgjgdmSchema.safeParse(gajgjgdmFullWidthInput);
console.log('原始 GAJGJGDM schema 结果:', originalGajgjgdmResult);
if (!originalGajgjgdmResult.success) {
  console.log('原始 GAJGJGDM schema 错误信息:', originalGajgjgdmResult.error.issues[0].message);
}

export {
  phoneResult,
  gajgjgdmResult,
  simpleResult,
  regexResult
};
