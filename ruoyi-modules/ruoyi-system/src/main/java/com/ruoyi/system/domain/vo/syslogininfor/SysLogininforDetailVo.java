package com.ruoyi.system.domain.vo.syslogininfor;

import lombok.Data;
import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 系统访问记录视图对象 sys_logininfor
 *
 * <AUTHOR>
 * @since 2025-05-08 10:26:02
 */
@Data
public class SysLogininforDetailVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 访问ID */
    private String id;
    /** 用户账号 */
    private String userName;
    /** 客户端 */
    private String clientKey;
    /** 设备类型 */
    private String deviceType;
    /** IP地址 */
    private String ipaddr;
    /** 登录地点 */
    private String loginLocation;
    /** 浏览器类型 */
    private String browser;
    /** 操作系统 */
    private String os;
    /** 登录状态（0成功 1失败） */
    private String status;
    /** 提示消息 */
    private String msg;
    /** 访问时间 */
    private LocalDateTime loginTime;


}
