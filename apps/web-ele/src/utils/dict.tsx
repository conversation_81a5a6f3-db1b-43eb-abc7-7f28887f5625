import DictTag from '#/components/dict-tag/index.vue';

/**
 * 渲染一个字典标签组件
 *
 * 此函数的作用是根据给定的值和类型参数，渲染一个字典标签组件（DictTag）
 * 它主要用于在React组件中展示基于字典数据的标签，其中`value`参数用于指定标签的值，
 * 而`lx`参数用于指定与标签相关的类型信息，帮助组件更好地呈现内容
 *
 * @param value - 标签的值，可以是数字或字符串类型
 * @param lx - 标签的类型信息，用于帮助组件呈现内容
 * @returns 返回渲染后的DictTag组件
 */
export function renderDictTag(value: number | string, lx: string) {
  return <DictTag data={lx} value={value}></DictTag>;
}
