import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { VbenFormSchema } from '#/adapter/form';

import { ZodEnum } from '#/schemas';
import { renderDictTag } from '#/utils/dict';

/**
 * 获取编辑表单的字段配置
 * @returns VbenFormSchema[] 表单配置
 */
export function useFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      dependencies: {
        show: () => false,
        triggerFields: [''],
      },
      fieldName: 'id',
      label: '部门ID',
    },
    {
      component: 'Input',
      dependencies: {
        show: () => false,
        triggerFields: [''],
      },
      fieldName: 'version',
      label: '乐观锁',
    },
    {
      component: 'SkDept',
      fieldName: 'parentId',
      label: '上级管辖单位',
      rules: 'required',
      formItemClass: 'col-span-2 items-baseline',
      componentProps: {
        type: 'tree-select',
        level: 2,
        checkStrictly: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'qc',
      label: '部门全称',
      rules: 'required',
      formItemClass: 'col-span-2 items-baseline',
      componentProps: {
        placeholder: '请输入部门全称',
        clearable: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'jc',
      label: '部门简称',
      rules: 'required',
      formItemClass: 'col-span-2 items-baseline',
      componentProps: {
        placeholder: '请输入部门简称',
        clearable: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'gajgjgdm',
      label: '公安机关机构代码',
      rules: ZodEnum.GAJGJGDM,
      componentProps: {
        maxlength: 12,
        placeholder: '请输入公安机关机构代码',
        clearable: true,
        showWordLimit: true,
      },
    },
    {
      component: 'SkDict',
      fieldName: 'jzfldm',
      label: '警种分类代码',
      rules: 'required',
      componentProps: {
        data: 'jzfl',
        placeholder: '请选择警种分类代码',
        clearable: true,
        in: '03,38,96',
      },
    },
    {
      component: 'SkDict',
      fieldName: 'dwjbdm',
      label: '单位级别代码',
      rules: 'required',
      componentProps: {
        data: 'dwjb',
        type: 'tree-select',
        placeholder: '请选择单位级别代码',
        clearable: true,
      },
    },
    {
      component: 'InputNumber',
      fieldName: 'orderNum',
      label: '显示顺序',
      rules: 'required',
      componentProps: {
        placeholder: '请输入显示顺序',
        clearable: true,
        step: 50,
      },
    },
    {
      component: 'SkDzys',
      fieldName: 'xzqhId',
      label: '行政区划',
      formItemClass: 'col-span-2 items-baseline',
      componentProps: {
        level: 2,
        type: 'cascader',
      },
      dependencies: {
        if: (values) => values.dwjbdm && values.dwjbdm.startsWith('4'),
        triggerFields: ['dwjbdm'],
      },
      rules: 'required',
    },
    {
      component: 'SkDept',
      fieldName: 'qfjgDeptId',
      label: '签发机关',
      formItemClass: 'col-span-2 items-baseline',
      componentProps: {
        level: 2,
      },
      dependencies: {
        if: (values) => {
          return values.jzfldm === '96';
        },
        triggerFields: ['jzfldm'],
      },
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'dwlxdh',
      label: '单位联系电话',
      rules: 'required',
      componentProps: {
        placeholder: '请输入单位联系电话',
        clearable: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'fzrXm',
      label: '负责人姓名',
      componentProps: {
        placeholder: '请输入负责人姓名',
        clearable: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'fzrLxdh',
      label: '负责人联系电话',
      componentProps: {
        placeholder: '请输入负责人联系电话',
        clearable: true,
      },
    },
    {
      component: 'SkDict',
      fieldName: 'syztdm',
      label: '使用状态',
      rules: 'required',
      defaultValue: '10',
      componentProps: {
        data: 'syzt',
        placeholder: '请选择使用状态',
        clearable: true,
      },
    },
  ];
}

/**
 * 获取查询表单的字段配置
 * @returns VbenFormSchema[] 查询表单配置
 */
export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'SkDept',
      fieldName: 'parentId',
      label: '上级管辖单位',
      componentProps: {
        type: 'tree-select',
        level: 2,
        checkStrictly: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'qc',
      label: '部门全称',
      componentProps: {
        placeholder: '请输入部门全称',
        clearable: true,
      },
    },
    {
      component: 'SkDict',
      fieldName: 'dwjbdm',
      label: '单位级别代码',
      componentProps: {
        data: 'dwjb',
        type: 'tree-select',
        checkStrictly: true,
        placeholder: '请选择单位级别代码',
        clearable: true,
      },
    },
    {
      component: 'SkDict',
      fieldName: 'jzfldm',
      label: '警种分类代码',
      componentProps: {
        data: 'jzfl',
        placeholder: '请选择警种分类代码',
        clearable: true,
        in: '03,38,96',
      },
    },
    {
      component: 'SkDict',
      fieldName: 'syztdm',
      label: '使用状态',
      componentProps: {
        data: 'syzt',
        placeholder: '请选择使用状态',
        clearable: true,
      },
    },
  ];
}

/**
 * 获取表格列配置
 * @returns VxeTableGridOptions['columns'] 表格列配置
 * @description 使用函数的形式返回列数据而不是直接export一个Array常量，是为了响应语言切换时重新翻译表头
 */
export function useGridColumns(): VxeTableGridOptions['columns'] {
  return [
    {
      type: 'checkbox',
      width: 60,
    },
    {
      title: '#',
      type: 'seq',
      width: 60,
      fixed: 'left',
    },
    {
      field: 'qc',
      title: '部门全称',
      align: 'left',
    },
    {
      field: 'gajgjgdm',
      title: '公安机关机构代码',
      align: 'center',
      width: 140,
    },
    {
      field: 'dwjbdm',
      title: '单位级别',
      align: 'center',
      width: 140,
      slots: {
        default: ({ row }) => {
          return renderDictTag(row.dwjbdm, 'dwjb');
        },
      },
    },
    {
      field: 'jzfldm',
      title: '警种分类',
      align: 'center',
      width: 80,
      slots: {
        default: ({ row }) => {
          return renderDictTag(row.jzfldm, 'jzfl');
        },
      },
    },
    {
      field: 'orderNum',
      title: '排序',
      align: 'center',
      width: 60,
    },
    {
      field: 'syztdm',
      title: '使用状态',
      align: 'center',
      width: 100,
      slots: { default: 'status' },
    },
    {
      title: '操作',
      field: 'action',
      fixed: 'right',
      slots: { default: 'action' },
      width: 240,
    },
  ];
}
