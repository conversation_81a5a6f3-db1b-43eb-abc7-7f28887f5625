package com.ruoyi.safetyoutline.service;

import cn.hutool.core.util.ObjectUtil;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.ruoyi.common.core.core.domain.PageData;
import com.ruoyi.common.core.core.domain.Result;
import com.ruoyi.common.core.utils.MapstructUtils;
import com.ruoyi.common.orm.core.page.PageQuery;
import com.ruoyi.common.orm.core.page.PageResult;
import com.ruoyi.common.orm.core.service.impl.BaseServiceImpl;
import com.ruoyi.safetyoutline.domain.bo.syslevel3protectconfig.SysLevel3protectConfigAddBo;
import com.ruoyi.safetyoutline.domain.bo.syslevel3protectconfig.SysLevel3protectConfigEditBo;
import com.ruoyi.safetyoutline.domain.bo.syslevel3protectconfig.SysLevel3protectConfigListBo;
import com.ruoyi.safetyoutline.domain.bo.syslevel3protectconfig.SysLevel3protectConfigOptionBo;
import com.ruoyi.safetyoutline.domain.po.SysLevel3protectConfig;
import com.ruoyi.safetyoutline.domain.po.table.SysLevel3protectConfigTableDef;
import com.ruoyi.safetyoutline.domain.vo.syslevel3protectconfig.SysLevel3protectConfigDetailVo;
import com.ruoyi.safetyoutline.domain.vo.syslevel3protectconfig.SysLevel3protectConfigExcelVo;
import com.ruoyi.safetyoutline.domain.vo.syslevel3protectconfig.SysLevel3protectConfigListVo;
import com.ruoyi.safetyoutline.domain.vo.syslevel3protectconfig.SysLevel3protectConfigOptionVo;
import com.ruoyi.safetyoutline.mapper.SysLevel3protectConfigMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;

import static com.ruoyi.safetyoutline.domain.po.table.SysLevel3protectConfigTableDef.SYS_LEVEL3PROTECT_CONFIG;

/**
 * 三级等保配置信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-13 15:56:35
 */
@Service
public class SysLevel3protectConfigService extends BaseServiceImpl<SysLevel3protectConfigMapper, SysLevel3protectConfig> {
    @Resource
    private SysLevel3protectConfigMapper sysLevel3protectConfigMapper;

    public QueryWrapper query() {
        return super.query().from(SYS_LEVEL3PROTECT_CONFIG);
    }

    private QueryWrapper buildQueryWrapper(SysLevel3protectConfigListBo sysLevel3protectConfigListBo) {
        QueryWrapper queryWrapper = super.buildBaseQueryWrapper();
        queryWrapper.and(SYS_LEVEL3PROTECT_CONFIG.PZSYZDLMSBZ.eq(sysLevel3protectConfigListBo.getPzsyzdlmsbz()));
        queryWrapper.and(SYS_LEVEL3PROTECT_CONFIG.ZDLXDLSBCS.eq(sysLevel3protectConfigListBo.getZdlxdlsbcs()));
        queryWrapper.and(SYS_LEVEL3PROTECT_CONFIG.LXDLSBSDSJ.eq(sysLevel3protectConfigListBo.getLxdlsbsdsj()));
        queryWrapper.and(SYS_LEVEL3PROTECT_CONFIG.DLWCZZDTCSJ.eq(sysLevel3protectConfigListBo.getDlwczzdtcsj()));
        queryWrapper.and(SYS_LEVEL3PROTECT_CONFIG.KQMMFZDBZ.eq(sysLevel3protectConfigListBo.getKqmmfzdbz()));
        queryWrapper.and(SYS_LEVEL3PROTECT_CONFIG.DQXGMMSJJG.eq(sysLevel3protectConfigListBo.getDqxgmmsjjg()));
        queryWrapper.and(SYS_LEVEL3PROTECT_CONFIG.DQXGMMBXXCFCS.eq(sysLevel3protectConfigListBo.getDqxgmmbxxcfcs()));
        queryWrapper.and(SYS_LEVEL3PROTECT_CONFIG.WJAQJCBZ.eq(sysLevel3protectConfigListBo.getWjaqjcbz()));
        queryWrapper.and(SYS_LEVEL3PROTECT_CONFIG.SCWJDXXZ.eq(sysLevel3protectConfigListBo.getScwjdxxz()));
        queryWrapper.and(SYS_LEVEL3PROTECT_CONFIG.CREATE_TIME.between(sysLevel3protectConfigListBo.getParams().get("beginTime"), sysLevel3protectConfigListBo.getParams().get("endTime")));
        return queryWrapper;
    }


    /**
     * 分页查询三级等保配置信息列表
     *
     * @param sysLevel3protectConfigListBo 三级等保配置信息ListBo
     * @return 分页三级等保配置信息集合
     */
    public Result<PageData<SysLevel3protectConfigListVo>> page(SysLevel3protectConfigListBo sysLevel3protectConfigListBo) {
        QueryWrapper queryWrapper = buildQueryWrapper(sysLevel3protectConfigListBo);
        Page<SysLevel3protectConfigListVo> page = this.pageAs(PageQuery.build(), queryWrapper, SysLevel3protectConfigListVo.class);
        return PageResult.success(page);
    }


    /**
     * 查询三级等保配置信息列表
     *
     * @param sysLevel3protectConfigListBo 三级等保配置信息ListBo
     * @return 三级等保配置信息集合
     */
    public List<SysLevel3protectConfigListVo> list(SysLevel3protectConfigListBo sysLevel3protectConfigListBo) {
        QueryWrapper queryWrapper = buildQueryWrapper(sysLevel3protectConfigListBo);
        return this.listAs(queryWrapper, SysLevel3protectConfigListVo.class);
    }


    /**
     * 获取三级等保配置信息选择项查询条件
     *
     * @param optionBo 三级等保配置信息表查询条件
     * @param a        三级等保配置信息表对象
     * @return 三级等保配置信息选择项查询条件
     */
    private static QueryWrapper getOptionQueryWrapper(SysLevel3protectConfigOptionBo optionBo, SysLevel3protectConfigTableDef a) {
        QueryWrapper queryWrapper = new QueryWrapper();
        //选项列表查询条件
        return queryWrapper;
    }

    /**
     * 查询三级等保配置信息管理选择列表
     *
     * @param optionBo 三级等保配置信息选择查询条件
     * @return 返回树形结构的三级等保配置信息信息列表
     */
    public Result<List<SysLevel3protectConfigOptionVo>> optionSelect(SysLevel3protectConfigOptionBo optionBo) {
        SysLevel3protectConfigTableDef a = SYS_LEVEL3PROTECT_CONFIG.as("a");
        QueryWrapper queryWrapper = getOptionQueryWrapper(optionBo, a);

        List<SysLevel3protectConfigOptionVo> optionVoList = this.listAs(queryWrapper, SysLevel3protectConfigOptionVo.class);
        return Result.success(optionVoList);
    }

    /**
     * 导出三级等保配置信息列表
     *
     * @param sysLevel3protectConfigListBo 三级等保配置信息ListBo
     * @return 三级等保配置信息集合
     */
    public List<SysLevel3protectConfigExcelVo> export(SysLevel3protectConfigListBo sysLevel3protectConfigListBo) {
        QueryWrapper queryWrapper = buildQueryWrapper(sysLevel3protectConfigListBo);
        return this.listAs(queryWrapper, SysLevel3protectConfigExcelVo.class);
    }


    /**
     * 新增三级等保配置信息
     *
     * @param sysLevel3protectConfigAddBo 三级等保配置信息AddBo
     * @return 结果:true 操作成功，false 操作失败
     */
    @Transactional
    public Result<Void> add(SysLevel3protectConfigAddBo sysLevel3protectConfigAddBo) {
        QueryWrapper queryWrapper = buildQueryWrapper(new SysLevel3protectConfigListBo());
        long czsl = this.count(queryWrapper);
        if (czsl > 0) {
            return Result.fail("已经存在三级等保配置信息记录，请编辑信息！");
        }
        SysLevel3protectConfig sysLevel3protectConfig = MapstructUtils.convert(sysLevel3protectConfigAddBo, SysLevel3protectConfig.class);
        boolean inserted = this.save(sysLevel3protectConfig);
        if (!inserted) {
            return Result.fail("新增三级等保配置信息记录失败！");
        }
        return Result.success();
    }

    /**
     * 修改三级等保配置信息
     *
     * @param sysLevel3protectConfigEditBo 三级等保配置信息EditBo
     * @return 结果:true 更新成功，false 更新失败
     */
    public Result<Void> edit(SysLevel3protectConfigEditBo sysLevel3protectConfigEditBo) {
        SysLevel3protectConfig sysLevel3protectConfig = MapstructUtils.convert(sysLevel3protectConfigEditBo, SysLevel3protectConfig.class);
        boolean updated = false;
        if (ObjectUtil.isNotNull(sysLevel3protectConfig) && ObjectUtil.isNotNull(sysLevel3protectConfig.getId())) {
            updated = this.updateById(sysLevel3protectConfig);
        }
        if (!updated) {
            return Result.fail("修改三级等保配置信息记录失败!");
        }
        return Result.success();
    }


    /**
     * 批量删除三级等保配置信息
     *
     * @param ids 需要删除的三级等保配置信息主键集合
     * @return 结果:true 删除成功，false 删除失败
     */
    @Transactional
    public Result<Void> removeByIds(String[] ids) {
        boolean deleted = this.removeByIds(Arrays.asList(ids));
        if (!deleted) {
            return Result.fail("删除三级等保配置信息记录失败!");
        }
        return Result.success();
    }


    /**
     * 查询三级等保配置信息
     *
     * @param id 三级等保配置信息主键
     * @return 三级等保配置信息
     */
    public SysLevel3protectConfigDetailVo detail(String id) {
        return this.getOneAs(query().where(SYS_LEVEL3PROTECT_CONFIG.ID.eq(id)), SysLevel3protectConfigDetailVo.class);
    }

    /**
     * 新增三级等保配置信息，前台提供主键值，一般用于导入的场合
     *
     * @param sysLevel3protectConfigAddBo 三级等保配置信息AddBo
     * @return 结果:true 操作成功，false 操作失败
     */
    public Result<Void> insertWithPk(SysLevel3protectConfigAddBo sysLevel3protectConfigAddBo) {
        SysLevel3protectConfig sysLevel3protectConfig = MapstructUtils.convert(sysLevel3protectConfigAddBo, SysLevel3protectConfig.class);
        boolean inserted;
        inserted = sysLevel3protectConfigMapper.insertWithPk(sysLevel3protectConfig) > 0;//前台传来主键值
        if (!inserted) {
            return Result.fail("新增三级等保配置信息记录失败！");
        }
        return Result.success();
    }

}
