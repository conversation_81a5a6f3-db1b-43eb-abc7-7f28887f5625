// 测试完全按照您规则实现的中文姓名验证
import {
  isChinese,
  validateChineseNameWithDetails,
  CHINESE_NAME_REGEXES,
} from '@/utils/validators/chinese-name-rule';

describe('中文姓名验证规则测试', () => {
  describe('isChinese - 完全按照原规则实现', () => {
    test('有效的中文姓名', () => {
      const validNames = [
        '张三',
        '李四',
        '王小明',
        '欧阳修',
        '司马懿',
        '诸葛亮',
        '上官婉儿',
        '爱新觉罗·玄烨',
        '叶赫那拉·慧征',
        '博尔济吉特·布木布泰',
        '钮祜禄·甄嬛',
        // 中文点可选的情况
        '爱新觉罗玄烨',
        '叶赫那拉慧征',
      ];

      validNames.forEach(name => {
        expect(isChinese(name, false)).toBe(true);
      });
    });

    test('无效的姓名 - 包含英文或数字', () => {
      const invalidNames = [
        '张San',
        '<PERSON>三',
        '李4',
        '王小明123',
        '<PERSON>',
        '123',
        'abc',
      ];

      invalidNames.forEach(name => {
        expect(isChinese(name, false)).toBe(false);
      });
    });

    test('无效的姓名 - 包含禁用字符', () => {
      const invalidNames = [
        '张【三】',
        '李、四',
        '王﹒小明',
        '赵▪钱',
        '孙•李',
      ];

      invalidNames.forEach(name => {
        expect(isChinese(name, false)).toBe(false);
      });
    });

    test('中文点使用规则', () => {
      // 有效的中文点使用
      expect(isChinese('爱新觉罗·玄烨', false)).toBe(true);
      expect(isChinese('叶赫那拉·慧征', false)).toBe(true);
      
      // 中文点可选（·?表示可选）
      expect(isChinese('爱新觉罗玄烨', false)).toBe(true);
      expect(isChinese('叶赫那拉慧征', false)).toBe(true);
      
      // 无效的中文点使用
      expect(isChinese('·张三', false)).toBe(false); // 开头
      expect(isChinese('张三·', false)).toBe(false); // 结尾
      expect(isChinese('张·三·四', false)).toBe(false); // 多个中文点
      expect(isChinese('·', false)).toBe(false); // 只有中文点
    });

    test('空值处理', () => {
      // 不允许空值
      expect(isChinese('', false)).toBe(false);
      expect(isChinese('   ', false)).toBe(false);
      
      // 允许空值（对应 this.optional(element)）
      expect(isChinese('', true)).toBe(true);
      expect(isChinese('   ', true)).toBe(true);
    });
  });

  describe('正则表达式验证', () => {
    test('第一步：中文字符格式验证', () => {
      const regex = CHINESE_NAME_REGEXES.FORMAT;
      
      // 应该通过的
      expect(regex.test('张三')).toBe(true);
      expect(regex.test('爱新觉罗·玄烨')).toBe(true);
      expect(regex.test('爱新觉罗玄烨')).toBe(true); // 中文点可选
      
      // 应该失败的
      expect(regex.test('张San')).toBe(false);
      expect(regex.test('·张三')).toBe(false);
      expect(regex.test('张三·')).toBe(false);
      expect(regex.test('张·三·四')).toBe(false);
    });

    test('第二步：禁用字符检测', () => {
      const regex = CHINESE_NAME_REGEXES.FORBIDDEN_CHARS;
      
      // 应该通过的（不包含禁用字符）
      expect(regex.test('张三')).toBe(true);
      expect(regex.test('爱新觉罗·玄烨')).toBe(true);
      
      // 应该失败的（包含禁用字符）
      expect(regex.test('张【三】')).toBe(false);
      expect(regex.test('李、四')).toBe(false);
      expect(regex.test('王﹒小明')).toBe(false);
      expect(regex.test('赵▪钱')).toBe(false);
      expect(regex.test('孙•李')).toBe(false);
    });
  });

  describe('validateChineseNameWithDetails - 带详细错误信息', () => {
    test('有效姓名', () => {
      const result = validateChineseNameWithDetails('张三', false);
      expect(result.isValid).toBe(true);
      expect(result.errorType).toBeUndefined();
      expect(result.errorMessage).toBeUndefined();
    });

    test('空值错误', () => {
      const result = validateChineseNameWithDetails('', false);
      expect(result.isValid).toBe(false);
      expect(result.errorType).toBe('empty');
      expect(result.errorMessage).toBe('姓名不能为空');
    });

    test('格式错误', () => {
      const result = validateChineseNameWithDetails('张San', false);
      expect(result.isValid).toBe(false);
      expect(result.errorType).toBe('format');
      expect(result.errorMessage).toBe('只能输入中文字符');
    });

    test('禁用字符错误', () => {
      const result = validateChineseNameWithDetails('张【三】', false);
      expect(result.isValid).toBe(false);
      expect(result.errorType).toBe('forbiddenChars');
      expect(result.errorMessage).toBe('姓名包含不允许的字符');
    });

    test('允许空值', () => {
      const result = validateChineseNameWithDetails('', true);
      expect(result.isValid).toBe(true);
    });
  });

  describe('边界情况测试', () => {
    test('特殊中文字符', () => {
      // 测试 Unicode 范围边界
      const specialChars = [
        '龘', // 特殊汉字
        '㐀', // 扩展汉字
        '鿿', // Unicode 范围内的字符
      ];

      specialChars.forEach(char => {
        expect(isChinese(char, false)).toBe(true);
      });
    });

    test('中文点的各种情况', () => {
      // 正确的中文点使用
      expect(isChinese('爱新觉罗·玄烨', false)).toBe(true);
      
      // 中文点可选（根据正则 ·? 表示可选）
      expect(isChinese('爱新觉罗玄烨', false)).toBe(true);
      
      // 错误的中文点使用
      expect(isChinese('爱新觉罗··玄烨', false)).toBe(false); // 两个中文点
      expect(isChinese('·爱新觉罗玄烨', false)).toBe(false); // 开头有中文点
      expect(isChinese('爱新觉罗玄烨·', false)).toBe(false); // 结尾有中文点
    });

    test('空白字符处理', () => {
      // 包含空格的情况
      expect(isChinese('张 三', false)).toBe(false); // 中间有空格
      expect(isChinese(' 张三', false)).toBe(false); // 开头有空格
      expect(isChinese('张三 ', false)).toBe(false); // 结尾有空格
      
      // 但是我们的实现会 trim，所以这些应该通过
      // 注意：原 jQuery 规则可能不会 trim，需要根据实际需求调整
    });
  });

  describe('与原 jQuery 规则对比', () => {
    // 模拟原始的 jQuery Validator 规则
    function originalRule(value: string, allowEmpty: boolean = false): boolean {
      if (allowEmpty && (!value || value.trim() === '')) {
        return true;
      }
      
      if (!value || value.trim() === '') {
        return false;
      }

      const trimmedValue = value.trim();
      let flag = /^[\u0391-\uFFE5]+$|^[\u0391-\uFFE5]+·?[\u0391-\uFFE5]+$/.test(trimmedValue);
      if (flag) {
        flag = /^[^【】、﹒▪•]+$/.test(trimmedValue);
      }
      return flag;
    }

    test('与原规则结果一致', () => {
      const testCases = [
        '张三',
        '爱新觉罗·玄烨',
        '爱新觉罗玄烨',
        '张San',
        '张【三】',
        '·张三',
        '张三·',
        '',
        '   ',
      ];

      testCases.forEach(testCase => {
        const originalResult = originalRule(testCase, false);
        const newResult = isChinese(testCase, false);
        
        expect(newResult).toBe(originalResult);
      });
    });

    test('空值允许情况一致', () => {
      const emptyCases = ['', '   '];

      emptyCases.forEach(testCase => {
        const originalResult = originalRule(testCase, true);
        const newResult = isChinese(testCase, true);
        
        expect(newResult).toBe(originalResult);
      });
    });
  });
});

export {};
