package com.ruoyi.mf.listener;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.idev.excel.context.AnalysisContext;
import cn.idev.excel.event.AnalysisEventListener;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.SpringUtils;
import com.ruoyi.common.core.utils.ValidatorUtils;
import com.ruoyi.common.excel.core.ExcelListener;
import com.ruoyi.common.excel.core.ExcelResult;
import com.ruoyi.common.core.core.domain.Result;
import com.ruoyi.mf.domain.bo.mfstudent.*;
import com.ruoyi.mf.domain.po.MfStudent;
import com.ruoyi.mf.service.*;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 学生信息单自定义导入
 *
 * <AUTHOR>
 * @since 2025-03-17 17:43:53
 */
@Slf4j
public class MfStudentImportListener extends AnalysisEventListener<MfStudentImportBo> implements ExcelListener<MfStudentImportBo> {
    private final MfStudentService mfStudentService;

    private final Boolean isUpdateSupport;
    private int successNum = 0;
    private int failureNum = 0;
    private final StringBuilder successMsg = new StringBuilder();
    private final StringBuilder failureMsg = new StringBuilder();

    public MfStudentImportListener(Boolean isUpdateSupport) {
        this.mfStudentService = SpringUtils.getBean(MfStudentService.class);
        this.isUpdateSupport = isUpdateSupport;
    }

    @Override
    public void invoke(MfStudentImportBo mfStudentImportBo, AnalysisContext context) {
        try {
            MfStudent mfStudent= mfStudentService.getById(mfStudentImportBo);
            if (ObjectUtil.isNull(mfStudent)) {
                //不存在就新增
                MfStudentAddBo mfStudentAddBo = BeanUtil.toBean(mfStudentImportBo, MfStudentAddBo.class);
                ValidatorUtils.validate(mfStudentAddBo);
                  Result<Void> result= mfStudentService.add(mfStudentAddBo);
                if (result.isSuccess()) {
                    successNum++;
                    successMsg.append("<br/>").append(successNum).append("、学生信息单 记录导入成功");
                    return;
                } else {
                    failureNum++;
                    failureMsg.append("<br/>").append(failureNum).append("、学生信息单 记录导入失败");
                    return;
                }
            } else if (isUpdateSupport) {
                //存在就更新
                MfStudentEditBo mfStudentEditBo = BeanUtil.toBean(mfStudentImportBo, MfStudentEditBo.class);
                mfStudentEditBo.setId(mfStudentImportBo.getId());
                Result<Void> result= mfStudentService.edit(mfStudentEditBo);
                if (result.isSuccess()) {
                    successNum++;
                    successMsg.append("<br/>").append(successNum).append("、学生信息单 记录更新成功");
                    return;
                } else {
                    failureNum++;
                    failureMsg.append("<br/>").append(failureNum).append("、学生信息单 记录更新失败");
                    return;
                }
            }
        } catch (Exception e) {
            failureNum++;
            String msg = "<br/>" + failureNum + "、学生信息单 记录导入失败：";
            failureMsg.append(msg).append(e.getMessage());
            log.error(msg, e);
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {

    }

    @Override
    public ExcelResult<MfStudentImportBo> getExcelResult() {
        return new ExcelResult<>() {

            @Override
            public String getAnalysis() {
                if (failureNum > 0) {
                    failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据没有成功导入，错误如下：");
                    throw new ServiceException(failureMsg.toString());
                } else {
                    successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
                }
                return successMsg.toString();
            }

            @Override
            public List<MfStudentImportBo> getList() {
                return null;
            }

            @Override
            public List<String> getErrorList() {
                return null;
            }
        };
    }
}
