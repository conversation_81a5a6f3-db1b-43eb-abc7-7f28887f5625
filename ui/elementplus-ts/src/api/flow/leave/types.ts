export interface PageSet {

  flowChart?: boolean;

  imgUrl?: string | number;
}

export interface LeaveVO extends BaseEntity {
  /**
   * id
   */
  id: string | number;
  /**
   * folw_type
   */
  flowCode: number;
  /**
   * ywmc
   */
  ywmc: string;
  /**
   * start_time
   */
  startTime: string;
  /**
   * end_time
   */
  endTime: string;
  /**
   * instance_id
   */
  instanceId: string | number;
  /**
   * node_code
   */
  nodeCode: string;
  /**
   * node_name
   */
  nodeName: string;
  /**
   * node_type
   */
  nodeType: number;
  /**
   * flow_status
   */
  flowStatus: string;
}

export interface LeaveForm {
  /**
   * id
   */
  id?: string | number;
  /**
   * folw_type
   */
  flowCode?: string;
  /**
   * ywmc
   */
  ywmc?: string;
  /**
   * start_time
   */
  startTime?: string;
  /**
   * end_time
   */
  endTime?: string;
  /**
   * instance_id
   */
  instanceId?: string | number;
  /**
   * node_code
   */
  nodeCode?: string;
  /**
   * node_name
   */
  nodeName?: string;
  /**
   * node_type
   */
  nodeType?: number;
  /**
   * flow_status
   */
  flowStatus?: string;
  /**
   * version
   */
  version?: number;
}


export interface LeaveQuery extends PageQuery {
  /**
   * folw_type
   */
  flowCode?: string;
  /**
   * ywmc
   */
  ywmc?: string;
  /**
   * start_time
   */
  startTime?: string;
  /**
   * end_time
   */
  endTime?: string;
  /**
   * instance_id
   */
  instanceId?: string | number;
  /**
   * node_code
   */
  nodeCode?: string;
  /**
   * node_name
   */
  nodeName?: string;
  /**
   * node_type
   */
  nodeType?: number;
  /**
   * flow_status
   */
  flowStatus?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
