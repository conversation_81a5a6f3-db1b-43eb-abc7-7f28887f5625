package com.ruoyi.system.service;

import com.mybatisflex.core.query.QueryMethods;
import com.mybatisflex.core.query.QueryWrapper;
import com.ruoyi.common.core.core.domain.Result;
import com.ruoyi.common.orm.core.service.impl.BaseServiceImpl;
import com.ruoyi.system.domain.bo.sysrole.AuthUserAllBo;
import com.ruoyi.system.domain.po.SysUserRole;
import com.ruoyi.system.mapper.SysUserRoleMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static com.ruoyi.system.domain.po.table.SysUserRoleTableDef.SYS_USER_ROLE;

/**
 * SysUserRoleService实现类
 *
 * <AUTHOR>
 * <AUTHOR>
 */
@Service
public class SysUserRoleService extends BaseServiceImpl<SysUserRoleMapper, SysUserRole> {

    @Resource
    private SysUserRoleMapper userRoleMapper;

    public QueryWrapper query() {
        return super.query().from(SYS_USER_ROLE);
    }

    /**
     * 通过角色ID查询角色使用数量
     *
     * @param roleId 角色ID
     * @return 结果
     */
    public int countUserRoleByRoleId(String roleId) {
        QueryWrapper queryWrapper = QueryWrapper.create()
            .select(QueryMethods.count(SYS_USER_ROLE.ROLE_ID))
            .from(SYS_USER_ROLE)
            .where(SYS_USER_ROLE.ROLE_ID.eq(roleId));

        return userRoleMapper.selectObjectByQueryAs(queryWrapper, Integer.class);
    }

    /**
     * 取消授权用户:删除用户和角色关联信息
     * delete from sys_user_role where user_id=#{userId} and role_id=#{roleId}
     *
     * @param userRole 用户和角色关联信息
     * @return 结果:true 删除成功，false 删除失败
     */
    @Transactional
    public Result<Void> deleteUserRoleInfo(SysUserRole userRole) {
        QueryWrapper queryWrapper = QueryWrapper.create().from(SYS_USER_ROLE).where(SYS_USER_ROLE.USER_ID.eq(userRole.getUserId()))
            .and(SYS_USER_ROLE.ROLE_ID.eq(userRole.getRoleId()));
        boolean deleted= this.remove(queryWrapper);
        if (!deleted) {
            return Result.fail("取消授权用户角色失败，请联系管理员");
        }
        return Result.success();
    }

    /**
     * 批量取消授权用户角色
     * delete from sys_user_role where role_id=#{roleId} and user_id in
     *
     * @param authUserAllBo  角色ID,需要删除的用户数据ID
     * @return 结果:true 删除成功，false 删除失败
     */
    public Result<Void> deleteUserRoleInfos(AuthUserAllBo authUserAllBo) {
        QueryWrapper queryWrapper = QueryWrapper.create().from(SYS_USER_ROLE).where(SYS_USER_ROLE.ROLE_ID.eq(authUserAllBo.getRoleId()))
            .and(SYS_USER_ROLE.USER_ID.in(Arrays.asList(authUserAllBo.getUserIds())));

        boolean deleted = this.remove(queryWrapper);
        if (!deleted) {
            return Result.fail("批量取消授权用户角色失败，请联系管理员");
        }
        return Result.success();
    }

    /**
     * 批量选择授权用户角色
     * * insert into sys_user_role(user_id, role_id) values
     *
     */
    public Result<Void> insertAuthUsers(AuthUserAllBo authUserAllBo) {
        String roleId = authUserAllBo.getRoleId();
        String[] userIds = authUserAllBo.getUserIds();
        // 新增用户与角色管理
        List<SysUserRole> list = new ArrayList<>();
        for (String userId : userIds) {
            SysUserRole ur = new SysUserRole();
            ur.setUserId(userId);
            ur.setRoleId(roleId);
            list.add(ur);
        }
        boolean inserted = this.saveBatchWithPk(list, 100);//批量插入
        if (!inserted) {
            return Result.fail("批量选择用户授权失败，请联系管理员");
        }
        return Result.success();
    }

    /**
     * 新增用户角色
     *
     * @param userId  用户ID
     * @param roleIds 需要新增的角色数据ID
     * @return 结果：true 保存成功，false 保存失败
     */
    public boolean insertUserRoles(String userId, String[] roleIds) {
        // 新增用户与角色管理
        List<SysUserRole> list = new ArrayList<>();
        for (String roleId : roleIds) {
            SysUserRole ur = new SysUserRole();
            ur.setUserId(userId);
            ur.setRoleId(roleId);
            list.add(ur);
        }
        return this.saveBatchWithPk(list, 100);//批量插入
    }

    /**
     * 通过用户ID删除用户和角色关联
     * delete from sys_user_role where user_id=#{userId}
     *
     * @param userId 用户ID
     */
    public void deleteUserRoleByUserId(String userId) {
        QueryWrapper queryWrapper = query().where(SYS_USER_ROLE.USER_ID.eq(userId));
        this.remove(queryWrapper);
    }

    /**
     * 批量删除用户和角色关联
     * delete from sys_user_role where user_id in
     *
     * @param ids 需要删除的数据ID
     * @return 结果:true 删除成功，false 删除失败
     */
    public boolean deleteUserRole(String[] ids) {
        QueryWrapper queryWrapper = query().where(SYS_USER_ROLE.USER_ID.in(Arrays.asList(ids)));
        return this.remove(queryWrapper);
    }
}
