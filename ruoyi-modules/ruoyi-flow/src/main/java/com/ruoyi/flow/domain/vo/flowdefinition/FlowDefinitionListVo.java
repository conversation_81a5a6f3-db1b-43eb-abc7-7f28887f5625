package com.ruoyi.flow.domain.vo.flowdefinition;


import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import com.ruoyi.common.orm.core.domain.BaseEntity;

/**
 * 流程定义视图对象 flow_definition
 *
 * <AUTHOR>
 * @date 2025-01-06 10:04:52
 */
@Data
public class FlowDefinitionListVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;
    /** 流程编码 */
    private String flowCode;
    /** 流程名称 */
    private String flowName;
    /** 流程类别 */
    private String category;
    /** 是否发布（0未发布 1已发布 9失效） */
    private Integer isPublish;
    /** 审批表单是否自定义（Y是 N否） */
    private String formCustom;
    /** 审批表单路径 */
    private String formPath;
    /** 流程激活状态（0挂起 1激活） */
    private Integer activityStatus;
    /** 监听器类型 */
    private String listenerType;
    /** 监听器路径 */
    private String listenerPath;
    /** 扩展字段，预留给业务系统使用 */
    private String ext;
    /** 删除标志 */
    private String delFlag;

}
