package com.ruoyi.system.domain.vo.sysuser;

import lombok.Data;
import java.io.Serial;
import java.io.Serializable;

/**
 * 用户信息视图对象 sys_user
 *
 * <AUTHOR>
 * @since 2025-04-29 14:00:02
 */
@Data
public class SysUserDetailVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 用户ID */
    private String id;
    /** 部门ID */
    private String deptId;
    /** 用户账号 */
    private String userName;
    /** 用户昵称 */
    private String nickName;
    /** 用户类型（sys_user系统用户） */
    private String userType;
    /** 用户邮箱 */
    private String email;
    /** 手机号码 */
    private String phonenumber;
    /** 用户性别（0男 1女 2未知） */
    private String gender;
    /** 头像地址 */
    private Long avatar;
    /** 密码 */
    private String password;
    /** 帐号状态（0正常 1停用） */
    private String status;
    /** 乐观锁 */
    private Integer version;
    /** 备注 */
    private String remark;


}
