/**
 * 验证器相关的类型定义
 */

/**
 * 身份证验证结果
 */
export interface IdCardValidationResult {
  isValid: boolean;
  errorType?: 'areaCode' | 'birthDate' | 'checksum' | 'format' | 'length';
  errorMessage?: string;
}

/**
 * 身份证信息
 */
export interface IdCardInfo {
  /** 地区代码 */
  areaCode: string;
  /** 出生年份 */
  birthYear: number;
  /** 出生月份 */
  birthMonth: number;
  /** 出生日期 */
  birthDay: number;
  /** 顺序码 */
  sequenceCode: string;
  /** 校验码 */
  checkCode: string;
  /** 性别 */
  gender: 'female' | 'male';
}

/**
 * 验证错误类型
 */
export type ValidationErrorType =
  | 'areaCode'
  | 'birthDate'
  | 'checksum'
  | 'format'
  | 'fullWidth'
  | 'length'
  | 'empty'
  | 'forbiddenChars'
  | 'invalidDot';

/**
 * 通用验证器函数类型
 */
export type ValidatorFunction<T = string> = (value: T) => boolean;

/**
 * 带错误信息的验证器函数类型
 */
export type ValidatorWithErrorFunction<T = string> = (value: T) => {
  errorMessage?: string;
  isValid: boolean;
};

/**
 * 姓名验证结果
 */
export interface NameValidationResult {
  isValid: boolean;
  errorType?: 'empty' | 'length' | 'format' | 'forbiddenChars' | 'invalidDot';
  errorMessage?: string;
}

/**
 * 姓名验证选项
 */
export interface NameValidationOptions {
  /** 最小长度，默认为 2 */
  minLength?: number;
  /** 最大长度，默认为 10 */
  maxLength?: number;
  /** 是否允许空值，默认为 false */
  allowEmpty?: boolean;
}

/**
 * 姓名信息
 */
export interface NameInfo {
  /** 完整姓名 */
  fullName: string;
  /** 姓（对于有中文点的姓名）或姓氏部分 */
  firstName?: string;
  /** 名（对于有中文点的姓名）或名字部分 */
  lastName?: string;
  /** 是否包含中文点 */
  hasChineseDot: boolean;
  /** 姓名长度 */
  length: number;
}
