// 最终测试：验证 noFullWidthString 的完整功能
import { noFullWidthString, BaseSchema, ZodEnum } from './zod-enum';

console.log('=== 最终功能测试 ===');

// 1. 测试基础 noFullWidthString 函数
console.log('\n1. 基础函数测试:');
const basic = noFullWidthString();
console.log('✅ 半角字符:', basic.safeParse('hello'));
console.log('❌ 全角字符:', basic.safeParse('你好'));

// 2. 测试链式调用
console.log('\n2. 链式调用测试:');
const email = noFullWidthString().email('请输入正确的邮箱');
console.log('✅ 正确邮箱:', email.safeParse('<EMAIL>'));
console.log('❌ 全角邮箱:', email.safeParse('测试@example.com'));

// 3. 测试 BaseSchema
console.log('\n3. BaseSchema 测试:');
console.log('✅ STRING:', BaseSchema.STRING.safeParse('hello'));
console.log('❌ STRING:', BaseSchema.STRING.safeParse('你好'));
console.log('✅ EMAIL:', BaseSchema.EMAIL.safeParse('<EMAIL>'));
console.log('❌ EMAIL:', BaseSchema.EMAIL.safeParse('测试@example.com'));

// 4. 测试 ZodEnum
console.log('\n4. ZodEnum 测试:');
console.log('✅ ZodEnum.STRING:', ZodEnum.STRING.safeParse('hello'));
console.log('❌ ZodEnum.STRING:', ZodEnum.STRING.safeParse('你好'));
console.log('✅ ZodEnum.EMAIL:', ZodEnum.EMAIL.safeParse('<EMAIL>'));
console.log('❌ ZodEnum.EMAIL:', ZodEnum.EMAIL.safeParse('测试@example.com'));

// 5. 测试自定义错误消息
console.log('\n5. 自定义错误消息测试:');
const custom = noFullWidthString('用户名不能包含中文字符');
console.log('✅ 自定义消息:', custom.safeParse('username'));
console.log('❌ 自定义消息:', custom.safeParse('用户名'));

// 6. 测试复杂链式调用
console.log('\n6. 复杂链式调用测试:');
const complex = noFullWidthString()
  .min(3, '至少3个字符')
  .max(10, '最多10个字符')
  .regex(/^[a-zA-Z0-9]+$/, '只能包含字母和数字');

console.log('✅ 复杂验证:', complex.safeParse('user123'));
console.log('❌ 复杂验证-全角:', complex.safeParse('用户123'));
console.log('❌ 复杂验证-长度:', complex.safeParse('ab'));
console.log('❌ 复杂验证-格式:', complex.safeParse('user_123'));

// 7. 测试边界情况
console.log('\n7. 边界情况测试:');
console.log('✅ 空字符串:', basic.safeParse(''));
console.log('✅ 纯数字:', basic.safeParse('123456'));
console.log('✅ 纯字母:', basic.safeParse('abcdef'));
console.log('✅ 符号:', basic.safeParse('!@#$%^&*()'));
console.log('❌ 全角空格:', basic.safeParse('　'));
console.log('❌ 全角数字:', basic.safeParse('１２３'));
console.log('❌ 全角字母:', basic.safeParse('ＡＢＣ'));
console.log('❌ 全角符号:', basic.safeParse('！＠＃'));

export {
  basic,
  email,
  custom,
  complex
};
