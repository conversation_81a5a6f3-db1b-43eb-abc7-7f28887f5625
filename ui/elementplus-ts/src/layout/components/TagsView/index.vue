<template>
  <div id="tags-view-container" class="tags-view-container">
    <scroll-pane ref="scrollPaneRef" class="tags-view-wrapper" @scroll="handleScroll">
      <SkContextMenu
        v-for="tag in visitedViews"
        :key="tag.path"
        :schema="[
          {
            icon: 'refresh-right',
            label: '刷新页面',
            command: () => {
              refreshSelectedTag(selectedTag);
            }
          },
          {
            icon: 'full-screen',
            label: '内容最大化',
            command: () => {
              bodyFullscreen();
            }
          },
          {
            divided: true,
            icon: 'close',
            label: '关闭当前',
            disabled: tag.path === '/index',
            command: () => {
              closeSelectedTag(selectedTag);
            }
          },
          {
            icon: 'back',
            label: '关闭左侧',
            command: () => {
              closeLeftTags();
            }
          },
          {
            icon: 'right',
            label: '关闭右侧',
            command: () => {
              closeRightTags();
            }
          },
          {
            divided: true,
            icon: 'circle-close',
            label: '关闭其他',
            command: () => {
              closeOthersTags();
            }
          },
          {
            icon: 'circle-close',
            label: '全部关闭',
            command: () => {
              closeAllTags(selectedTag);
            }
          }
        ]"
      >
        <template #droBody>
          <router-link
            ref="menuRef"
            :data-path="tag.path"
            :class="isActive(tag) ? 'active' : ''"
            :to="{ path: tag.path ? tag.path : '', query: tag.query, fullPath: tag.fullPath ? tag.fullPath : '' }"
            class="tags-view-item"
            :style="activeStyle(tag)"
            @click.middle="!isAffix(tag) ? closeSelectedTag(tag) : ''"
            @contextmenu.prevent="openMenu(tag, $event)"
          >
            <svg-icon :is="tag.meta.icon" v-if="tag.meta.icon" :icon-class="tag.meta.icon" style="margin: 0px 4px 0px 0px" />
            <span style="margin: 0 5px"> {{ tag.title }}</span>
            <span v-if="!isAffix(tag)" style="margin-left: 5px" @click.prevent.stop="closeSelectedTag(tag)">
              <close class="el-icon-close" style="width: 1em; height: 1em; vertical-align: middle" />
            </span>
          </router-link>
        </template>
        +
      </SkContextMenu>
    </scroll-pane>

    <div class="more-button" @click="toggleFullscreen">
      <el-tooltip v-if="!isFullscreen" content="放大">
        <svg-icon icon-class="fullscreen"></svg-icon>
      </el-tooltip>
      <el-tooltip v-if="isFullscreen" content="缩小">
        <svg-icon icon-class="exit-fullscreen"></svg-icon>
      </el-tooltip>
    </div>
    <SkContextMenu
      trigger="click"
      :schema="[
        {
          icon: 'refresh-right',
          label: '刷新页面',
          command: () => {
            refreshSelectedTag(selectedTag);
          }
        },
        {
          icon: 'full-screen',
          label: '内容最大化',
          command: () => {
            bodyFullscreen();
          }
        },
        {
          divided: true,
          icon: 'close',
          label: '关闭当前',
          command: () => {
            closeSelectedTag(selectedTag);
          }
        },
        {
          icon: 'back',
          label: '关闭左侧',
          command: () => {
            closeLeftTags();
          }
        },
        {
          icon: 'right',
          label: '关闭右侧',
          command: () => {
            closeRightTags();
          }
        },
        {
          divided: true,
          icon: 'circle-close',
          label: '关闭其他',
          command: () => {
            closeOthersTags();
          }
        },
        {
          icon: 'circle-close',
          label: '全部关闭',
          command: () => {
            closeAllTags(selectedTag);
          }
        }
      ]"
    >
      <template #droBody>
        <div class="more-button">
          <setting style="width: 1.7em; height: 1.7em"></setting>
        </div>
      </template>
    </SkContextMenu>
  </div>
</template>

<script setup lang="ts">
import ScrollPane from './ScrollPane.vue';
import { getNormalPath } from '@/utils/ruoyi';
import useSettingsStore from '@/store/modules/settings';
import usePermissionStore from '@/store/modules/permission';
import useTagsViewStore from '@/store/modules/tagsView';
import { RouteLocationNormalized, RouteRecordRaw } from 'vue-router';

const visible = ref(false);
const top = ref(0);
const left = ref(0);
const selectedTag = ref<RouteLocationNormalized>();
const affixTags = ref<RouteLocationNormalized[]>([]);
const scrollPaneRef = ref<InstanceType<typeof ScrollPane>>();

const route = useRoute();
const router = useRouter();
const menuRef = ref(null);
const visitedViews = computed(() => useTagsViewStore().getVisitedViews());
const routes = computed(() => usePermissionStore().getRoutes());
const theme = computed(() => useSettingsStore().theme);
const isFullscreen = ref(false);
const isBodyFullscreen = ref(false);
watch(route, () => {
  addTags();
  moveToCurrentTag();
});
watch(visible, (value) => {
  if (value) {
    document.body.addEventListener('click', closeMenu);
  } else {
    document.body.removeEventListener('click', closeMenu);
  }
});

const isActive = (r: RouteLocationNormalized): boolean => {
  return r.path === route.path;
};
const activeStyle = (tag: RouteLocationNormalized) => {
  if (!isActive(tag)) return {};
  return {
    color: theme.value,
    'border-bottom': '2px solid ' + theme.value
  };
};
const isAffix = (tag: RouteLocationNormalized) => {
  return tag?.meta && tag?.meta?.affix;
};
const filterAffixTags = (routes: RouteRecordRaw[], basePath = '') => {
  let tags: RouteLocationNormalized[] = [];

  routes.forEach((route) => {
    if (route.meta && route.meta.affix) {
      const tagPath = getNormalPath(basePath + '/' + route.path);
      tags.push({
        hash: '',
        matched: [],
        params: undefined,
        query: undefined,
        redirectedFrom: undefined,
        fullPath: tagPath,
        path: tagPath,
        name: route.name as string,
        meta: { ...route.meta }
      });
    }
    if (route.children) {
      const tempTags = filterAffixTags(route.children, route.path);
      if (tempTags.length >= 1) {
        tags = [...tags, ...tempTags];
      }
    }
  });
  return tags;
};
const initTags = () => {
  const res = filterAffixTags(routes.value);
  affixTags.value = res;
  for (const tag of res) {
    // Must have tag name
    if (tag.name) {
      useTagsViewStore().addVisitedView(tag);
    }
  }
};
const addTags = () => {
  const { name } = route;
  if (route.query.title) {
    route.meta.title = route.query.title as string;
  }
  if (name) {
    useTagsViewStore().addView(route as any);
    if (route.meta.link) {
      useTagsViewStore().addIframeView(route as any);
    }
  }
  return false;
};
const moveToCurrentTag = () => {
  nextTick(() => {
    for (const r of visitedViews.value) {
      if (r.path === route.path) {
        scrollPaneRef.value?.moveToTarget(r);
        // when query is different then update
        if (r.fullPath !== route.fullPath) {
          useTagsViewStore().updateVisitedView(route);
        }
      }
    }
  });
};
const refreshSelectedTag = (view: RouteLocationNormalized) => {
  window.skynet?.tab.refreshPage(view);
  if (route.meta.link) {
    useTagsViewStore().delIframeView(route);
  }
};
/**
 * 关闭按钮
 * @param view
 */
const closeSelectedTag = (view: RouteLocationNormalized) => {
  window.skynet.tab.closePage(view).then(({ visitedViews }: any) => {
    if (view) {
      if (isActive(view)) {
        toLastView(visitedViews, view);
      }
    }
  });
};
const closeRightTags = () => {
  window.skynet?.tab.closeRightPage(selectedTag.value).then((visitedViews: RouteLocationNormalized[]) => {
    if (!visitedViews.find((i: RouteLocationNormalized) => i.fullPath === route.fullPath)) {
      toLastView(visitedViews);
    }
  });
};
const closeLeftTags = () => {
  window.skynet?.tab.closeLeftPage(selectedTag.value).then((visitedViews: RouteLocationNormalized[]) => {
    if (!visitedViews.find((i: RouteLocationNormalized) => i.fullPath === route.fullPath)) {
      toLastView(visitedViews);
    }
  });
};
const closeOthersTags = () => {
  // router.push(selectedTag.value).catch(() => {});
  window.skynet.tab.closeOtherPage(selectedTag.value).then(() => {
    moveToCurrentTag();
  });
};
const closeAllTags = (view: RouteLocationNormalized) => {
  window.skynet?.tab.closeAllPage().then(({ visitedViews }) => {
    if (affixTags.value.some((tag) => tag.path === route.path)) {
      return;
    }
    toLastView(visitedViews, view);
  });
};
const toLastView = (visitedViews: RouteLocationNormalized[], view?: RouteLocationNormalized) => {
  const latestView = visitedViews.slice(-1)[0];
  if (latestView) {
    router.push(latestView.fullPath as string);
  } else {
    // now the default is to redirect to the home page if there is no tags-view,
    // you can adjust it according to your needs.
    if (view?.name === 'Dashboard') {
      // to reload home page
      router.replace({ path: '/redirect' + view?.fullPath });
    } else {
      router.push('/');
    }
  }
};
const openMenu = (tag: RouteLocationNormalized, e: MouseEvent) => {
  top.value = e.clientY;
  visible.value = true;
  selectedTag.value = tag;
};
const closeMenu = () => {
  visible.value = false;
};
const handleScroll = () => {
  closeMenu();
};

/**
 * 放大缩小页面
 */
const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value;
  emits(`update:isFullscreen`, isFullscreen.value);
};
/**
 * 放大内容页面
 */
const bodyFullscreen = () => {
  isBodyFullscreen.value = true;
  emits(`update:isBodyFullscreen`, isBodyFullscreen.value);
};

const emits = defineEmits(['update:isFullscreen', `update:isBodyFullscreen`]);

onMounted(() => {
  initTags();
  addTags();
});
</script>

<style lang="scss" scoped>
.tags-view-container {
  display: flex;
  height: 39px;
  width: 100%;
  background-color: var(--el-bg-color);
  border: 1px solid var(--el-border-color-light);
  box-shadow:
    0 1px 3px 0 rgba(0, 0, 0, 0.12),
    0 0 3px 0 rgba(0, 0, 0, 0.04);

  .more-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 43px;
    cursor: pointer;
    border-left: 1px solid var(--el-border-color-light);
    transition: all 0.3s;

    &:hover {
      background-color: var(--el-color-info-light-9);
    }

    .iconfont {
      font-size: 12.5px;
    }
  }

  .button-svg {
    margin-right: 8px;
    width: 1em;
    height: 1em;
  }

  .tags-view-wrapper {
    padding: 0 10px;

    .tags-view-item {
      display: inline-block;
      position: relative;
      cursor: pointer;
      height: 32px;
      line-height: 27px;
      background: transparent;
      border: transparent;
      //background-color: var(--el-bg-color);
      //border: 1px solid var(--el-border-color-light);
      color: #afafaf;
      padding: 0 15px;
      font-size: 15px;
      font-weight: 500;
      margin: 5px;

      &:hover {
        color: var(--el-color-primary);
      }

      //&:first-of-type {
      //  margin-left: 15px;
      //}
      &:last-of-type {
        //margin-right: 15px;
      }

      &.active {
        //border-bottom: 1px solid var(--el-bg-color);
        ////background-color: #42b983;
        //color: var(--el-bg-color);
        ////border-color: #42b983;
        //&::before {
        //  content: '';
        //  background-color: var(--el-bg-color);
        //  display: inline-block;
        //  width: 8px;
        //  height: 8px;
        //  border-radius: 50%;
        //  position: relative;
        //  margin-right: 5px;
        //}
      }
    }
  }

  .contextmenu {
    margin: 0;
    background: var(--el-bg-color);
    z-index: 3000;
    position: absolute;
    list-style-type: none;
    padding: 5px 0;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 400;
    box-shadow: 2px 2px 3px 0 rgba(0, 0, 0, 0.3);

    li {
      margin: 0;
      padding: 7px 16px;
      cursor: pointer;

      &:hover {
        background: #eee;
      }
    }
  }
}
</style>

<style lang="scss">
//reset element css of el-icon-close
.tags-view-wrapper {
  .tags-view-item {
    .tags-item-icon {
      height: 1em;
      width: 1em;
    }

    .el-icon-close {
      width: 16px;
      height: 16px;
      vertical-align: 2px;
      border-radius: 50%;
      text-align: center;
      transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
      transform-origin: 100% 50%;

      &:before {
        transform: scale(0.6);
        display: inline-block;
        vertical-align: -3px;
      }

      &:hover {
        background-color: #b4bccc;
        color: #fff;
        width: 12px !important;
        height: 12px !important;
      }
    }
  }
}
</style>
