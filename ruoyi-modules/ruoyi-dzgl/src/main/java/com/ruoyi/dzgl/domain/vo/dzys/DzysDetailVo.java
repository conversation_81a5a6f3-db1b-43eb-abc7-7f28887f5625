package com.ruoyi.dzgl.domain.vo.dzys;

import lombok.Data;
import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 地址元素管理视图对象 dz_dzys
 *
 * <AUTHOR>
 * @since 2025-06-04 16:50:12
 */
@Data
public class DzysDetailVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private String id;
    /** 删除标志（1代表存在 0代表删除） */
    private Integer jlzt;
    /** 代码 */
    private String dm;
    /** 名称 */
    private String mc;
    /** 简称 */
    private String jc;
    /** 父结点ID */
    private String parentId;
    /** 地址元素类型代码 */
    private String dzyslxdm;
    /** 使用状态代码 */
    private String syztdm;
    /** 数据归属单位ID */
    private String sjgsDeptId;
    /** 流动人口数据归属单位ID */
    private String sjgsLdrkDeptId;
    /** 设立日期 */
    private LocalDateTime slrq;
    /** 抵边判断标志 */
    private String dbPdbz;
    /** 乐观锁 */
    private Integer version;


}
